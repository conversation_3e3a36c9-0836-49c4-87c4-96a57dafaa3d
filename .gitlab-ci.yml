image: registry.cn-shenzhen.aliyuncs.com/packer-devops/docker:robot

stages:
  - build
  - deploy

variables:
  REGISTRY_URL: 'registry-vpc.cn-shenzhen.aliyuncs.com'
  TITLE: merchant_mini_v4
  IMAGE: ${TITLE}/image_catering/merchant_mini_v4
  BASE_HOST: **************
  DEBUG_HOST: **************
  PROD_3_0_S5: **************
  PROD_3_0_S6: **************
  BL1: **************

before_script:
  - COMMIT_SHA=${CI_COMMIT_SHA:0:8} # git.commit的提交代号
  - IMAGE_VERSION=${CI_COMMIT_TAG:17} # 版本号,长度为打tag的前缀 len("open-") = 5
  - TAR_TAG=image_catering_merchant_mini_v4_v${IMAGE_VERSION}.tar # 压缩包名称
  - IMAGE_TAG=${IMAGE}:image_catering_merchant_mini_v4_v${IMAGE_VERSION} # 镜像包名称
  - ALI_IMAGE_TAG=${HUB_DOMAIN}/packer/cashier_merchant_mini_v4_${TITLE}:v${IMAGE_VERSION} # 存储在阿里云镜像包名称
  - LATEST_IMAGE_TAG=${IMAGE}:latest # 镜像包名称
  - LATEST_TAR_TAG=image_catering_sh_latest.tar

build_dev:
  stage: build
  script:
    - echo "local13开始打包静态资源"
    - ssh root@$local13 "cd /app/devops/gitlab-ci/frontend/$CI_PROJECT_NAME &&
      rm -rf dist &&
      rm -rf dist.tar.gz &&
      git checkout . &&
      git checkout dev &&
      git pull &&
      yarn install --ignore-engines &&
      yarn run build &&
      tar czf dist.tar.gz dist"
    - echo "local13打包完成"
  tags:
    - local13
  only:
    - dev

deploy_dev:
  stage: deploy
  script:
    - echo "部署到开发环境"
    - scp root@$local13:/app/devops/gitlab-ci/frontend/$CI_PROJECT_NAME/dist.tar.gz root@$JUMPER:/mnt/nfs/share/frontend/$CI_PROJECT_NAME/
    - ssh root@$JUMPER "cd /root/app/frontend/$CI_PROJECT_NAME &&
      mkdir -p dist temp &&
      tar xzf /mnt/nfs/share/frontend/$CI_PROJECT_NAME/dist.tar.gz -C temp &&
      rm -rf dist &&
      mv temp/dist . &&
      rm -rf temp &&
      rm -rf /mnt/nfs/share/frontend/$CI_PROJECT_NAME/dist.tar.gz"
    - echo "部署开发环境完成..."
  tags:
    - local13
  only:
    - dev

build_staging:
  stage: build
  script:
    - ssh root@$local13 "cd /app/devops/gitlab-ci/frontend/$CI_PROJECT_NAME &&
      rm -rf dist &&
      rm -rf dist.tar.gz &&
      git checkout . &&
      git checkout staging &&
      git pull &&
      yarn install --ignore-engines &&
      yarn run build:h5 &&
      tar czf dist.tar.gz dist"
    - scp root@$local13:/app/devops/gitlab-ci/frontend/$CI_PROJECT_NAME/dist.tar.gz root@$JUMPER:/mnt/nfs/share/frontend/$CI_PROJECT_NAME/
    - echo "local13打包完成"
  tags:
    - local13
  only:
    - staging

deploy_staging:
  stage: deploy
  script:
    - echo "部署到正式环境,这里只能用base的runner运行了"
    - ssh root@$DEPLOY_PROD_SERVER_BALANCE "cd /app/frontend/merchant_mini_v4_staging/ &&
      mkdir -p dist temp &&
      tar xzf /mnt/nfs/share/frontend/$CI_PROJECT_NAME/dist.tar.gz -C temp &&
      rm -rf dist &&
      mv temp/dist . &&
      rm -rf temp &&
      rm -rf /mnt/nfs/share/frontend/$CI_PROJECT_NAME/dist.tar.gz"
    - echo "部署预发布环境完成..."
  only:
    - staging

build_prod:
  stage: build
  script:
    - ssh root@$local13 "cd /app/devops/gitlab-ci/frontend/$CI_PROJECT_NAME &&
      rm -rf dist &&
      rm -rf dist.tar.gz &&
      git checkout . &&
      git checkout master &&
      git pull &&
      yarn install --ignore-engines &&
      yarn run build:h5 &&
      tar czf dist.tar.gz dist"
    - scp root@$local13:/app/devops/gitlab-ci/frontend/$CI_PROJECT_NAME/dist.tar.gz root@$JUMPER:/mnt/nfs/share/frontend/$CI_PROJECT_NAME/
    - echo "local13打包完成"
  tags:
    - local13
  only:
    - master

deploy_prod:
  stage: deploy
  script:
    - echo "部署到正式环境,这里只能用base的runner运行了"
    - ssh root@$DEPLOY_PROD_SERVER_BALANCE "cd /app/frontend/$CI_PROJECT_NAME/ &&
      mkdir -p dist temp &&
      tar xzf /mnt/nfs/share/frontend/$CI_PROJECT_NAME/dist.tar.gz -C temp &&
      rm -rf dist &&
      mv temp/dist . &&
      rm -rf temp"
    - ssh root@$DEPLOY_PROD_SERVER_BALANCE_2 "cd /app/frontend/$CI_PROJECT_NAME/ &&
      mkdir -p dist temp &&
      tar xzf /mnt/nfs/share/frontend/$CI_PROJECT_NAME/dist.tar.gz -C temp &&
      rm -rf dist &&
      mv temp/dist . &&
      rm -rf temp &&
      rm -rf /mnt/nfs/share/frontend/$CI_PROJECT_NAME/dist.tar.gz"
    - echo "部署正式环境完成..."
  only:
    - master
  tags:
    - local13

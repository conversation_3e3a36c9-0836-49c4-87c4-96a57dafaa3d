<script>
// import Cache from '@/utils/cache'
export default {
  onLaunch: function () {
    // 隐藏源生tabbar
    uni.hideTabBar({
      animation: false
    })
  },
  onShow: function () {
    console.log('App Show')
  },
  onHide: function () {
    console.log('App Hide')
  }
  // errorCaptured (err, vm, info) {
  //   // err 具体报错信息
  //   // err 报错的组件
  //   // err 报错信息
  //   console.log(err, vm, info, '报错信息')
  // }
}
</script>

<style lang="scss">
/*每个页面公共css */
@import 'static/css/dcss.css';
@import 'uview-ui/index.scss';
@import 'styles/index';
</style>

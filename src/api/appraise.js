import request from '@/utils/request'

/**
 * 评价列表
 * @param { Object } data
 * @return { Promise }
 *
 */
export const apiMerchantMobileEvaluationManagementList = data => request({
  url: '/merchant_mobile/evaluation_management/list',
  method: 'post',
  data: data
})

/**
 * 评价回复
 * @param { Object } data id: 对应单据id, reply_content: 回复内容 最大150
 * @return { Promise }
 *
 */
export const apiBackgroundOperationManagementOrderEvaluationReplyEvaluation = data => request({
  url: '/background_operation_management/order_evaluation/reply_evaluation',
  method: 'post',
  data: data
})

/**
 * 反馈回复
 * @param { Object } data id: 对应单据id, merchant_remark: 回复内容 最大150，feedback_status 反馈状态 delete : "已删除" reply : "已回复" no_reply : "未回复"
 * @return { Promise }，
 *
 */
export const apiBackgroundFeedbackFeedbackRecordModify = data => request({
  url: '/background_feedback/feedback_record/modify',
  method: 'post',
  data: data
})

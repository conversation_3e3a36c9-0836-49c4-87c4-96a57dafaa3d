import request from '@/utils/request'

/**
 * 设备相关信息列表
 * @param {*} data
 * @returns
 */
export const apiBackgroundDeviceDeviceList = data => request({
  url: '/background_device/device/list',
  method: 'post',
  data: data
})

/**
 * 生成激活码
 * @param {*} data
 * @returns
 */
export const apiBackgroundAdminDeviceGenerateActivation = data => request({
  url: '/background/admin/device/generate_activation',
  method: 'post',
  data: data
})

/**
 * 设备设置
 * @param {*} data device_no 设备ID device_settings_pwd 设置密码 menu_type菜谱类型
 * @returns
 */
export const apiBackgroundDeviceDeviceConfig = data => request({
  url: '/background_device/device/config',
  method: 'post',
  data: data
})

/**
 * 存餐管理设备列表
 * @param {*} data  page页码  page_size每页展示数量
 * @returns
 */
export const apiMerchantMobileDeviceCupboardList = data => request({
  url: '/merchant_mobile/device/cupboard_list',
  method: 'post',
  data: data
})

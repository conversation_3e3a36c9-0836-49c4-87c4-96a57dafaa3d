// 字典类接口
import request from '@/utils/request'
/**
 * 部门树列表
 * @param {*} data
 * @returns
 */
export const apiCardServiceCardDepartmentGroupTreeList = data => request({
  url: '/api/card_service/card_department_group/tree_list',
  method: 'post',
  data: data || {}
})

/**
 * 组织树列表
 * @param {*} data 默认不传参，它会反馈这个账号所在的组织和它的下级
 * @returns
 */
export const apiBackgroundOrganizationOrganizationTreeList = data => request({
  url: '/background_organization/organization/tree_list',
  method: 'post',
  data: data || {}
})

/**
 * 获取设备类型
 * @param {*} data
 * @returns
 */
export const apiBackgroundDeviceDeviceDeviceType = data => request({
  url: '/background_device/device/device_type',
  method: 'post',
  data: data || {}
})

/**
 * 当前项目点下全部的部门
 * @param {*} data
 * @returns
 */
export const apiCardServiceCardDepartmentGroupAllTreeList = data => request({
  url: '/card_service/card_department_group/all_tree_list',
  method: 'post',
  data: data || {}
})

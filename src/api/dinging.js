import request from '@/utils/request'

/**
 * 菜品列表
 * @param { Object } data
 * @return { Promise }
 * @description 获取菜品列表
 *
 */
export const getApiBackgroundFoodMenuFoodList = data => request({
  url: '/background_food/menu/food_list',
  method: 'post',
  data: data
})

/**
 * 菜谱列表
 * @param { Object } data
 * @return { Promise }
 * @description 菜谱列表
 *
 */
export const getApiMerchantMobileMenuList = data => request({
  url: '/merchant_mobile/menu/list',
  method: 'post',
  data: data
})

/**
 * 菜谱菜品修改
 * @param { Object } data  date 选择日期  menu_id 菜谱id menu_type 菜谱id week : "周菜谱" month : "月菜谱"
 * @return { Promise }
 * @description 菜谱菜品修改
 *
 */
export const getApiMerchantMobileMenuMenuFoodModify = (data, type) => request({
  url: type === 'week' ? '/background_food/menu_weekly/menu_food_modify' : "/background_food/menu_monthly/menu_food_modify",
  method: 'post',
  data: data
})

/**
 * 获取菜品列表
 * @param { Object } data
 * @return { Promise }
 * @description 获取菜品列表
 *
 */
export const getApiMerchantMobileMenuFoodList = data => request({
  url: '/merchant_mobile/menu/food_list',
  method: 'post',
  data: data
})

/**
 * 获取当前组织餐段
 * @param { Object } data
 * @return { Promise }
 * @description 获取当前组织餐段
 *
 */
export const apiBackgroundReportCenterDataReportGetMealType = data => request({
  url: '/background_report_center/data_report/get_meal_type',
  method: 'post',
  data: data
})

// 每日巡检列表
export const apiMerchantMobileDailyPatrolList = data => request({
  url: '/merchant_mobile/daily_patrol/list',
  method: 'post',
  data: data
})

// 添加巡查记录
export const apiMerchantMobileDailyPatrolAdd = data => request({
  url: '/merchant_mobile/daily_patrol/add',
  method: 'post',
  data: data
})

// 获取巡查项目列表
export const apiBackgroundFundSupervisionDailyPatrolGetProjectList = data => request({
  url: '/background_fund_supervision/daily_patrol/get_project_list',
  method: 'post',
  data: data
})

import request from '../utils/request'

/**
 *
   * ['商户首页']
   * background_homepage.data.get_business_trend_data 获取首页相关数据
   * @param {{org_id:object, start_date:string, end_date:string, second_categories:object, query_type:string}} param org_id 组织id,start_date 开始日期,end_date 结束日期,second_categories 二级分类id,query_type 查询类型(营业额、退款等)
   * @returns {{code:number, msg:string, data:Empty}} - rsp
 */

export const getHomePageData = data => request({
  url: '/background_homepage/data/get_business_data',
  method: 'post',
  data: data
})
/**
 * 营业额日报
 *@param{page ：1 查询的页码，page_size 100 显示条目数 update_cache false 刷新缓存 start_date string 开始时间 end_date string 结束时间
date_type string 时间类型 org_ids array[integer] 组织 add_food_subsidy_fee是否加入餐补}
必须的start_date string 开始时间 end_date string 结束时间date_type string 时间类型
 */
export const getDailyTurnoverReport = data => request({
  url: '/background_report_center/data_report/order_business_list',
  method: 'post',
  data: data
})
/**
 * 获取权限
 * @param {{id:String}} role_id 角色id
 * @returns
 */
export const getTabbarStatistics = data => request({
  url: '/merchant_mobile/role/get_permission_list',
  method: 'post',
  data: data
})
/**
 *  根据添加到首页应用的数据
 * @param {{id:String,select_permission:Array}} role_id 角色id   select_permission添加的权限
 * @returns
 */
export const addHomeAppListData = data => request({
  url: '/merchant_mobile/role/set_select_permission',
  method: 'post',
  data: data
})

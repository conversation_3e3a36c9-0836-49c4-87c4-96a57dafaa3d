import request from '@/utils/request'

/**
 * 配送单
 * @param { Object } data
 * @return { Promise }
 * @description
 *
 */
export const apiMerchantMobileVendorReceivingNoteVendorDeliveryInfoListPost = data => request({
  url: '/merchant_mobile/vendor_receiving_note/vendor_delivery_info_list',
  method: 'post',
  data: data
})

/**
 * 配送单详情
 * @param { Object } data
 * @return { Promise }
 * @description
 *
 */
export const apiMerchantMobileVendorReceivingNoteVendorDeliveryDetailListPost = data => request({
  url: '/merchant_mobile/vendor_receiving_note/vendor_delivery_detail_list',
  method: 'post',
  data: data
})

/**
 * 收货单新增
 * @param { Object } data
 * @return { Promise }
 * @description
 *
 */
export const apiMerchantMobileVendorReceivingNoteReceivingNoteAddPost = data => request({
  url: '/merchant_mobile/vendor_receiving_note/receiving_note_add',
  method: 'post',
  data: data
})

/**
 * 收货单详情
 * @param { Object } data
 * @return { Promise }
 * @description
 *
 */
export const apiMerchantMobileVendorReceivingNoteVendorReceivingInfoListPost = data => request({
  url: '/merchant_mobile/vendor_receiving_note/vendor_receiving_detail_list',
  method: 'post',
  data: data
})

/**
 * 物资库详情
 * @param { Object } data
 * @return { Promise }
 * @description
 *
 */
export const apiBackgroundDrpMaterialsSupplierManageDetails = data => request({
  url: '/background_drp/materials/supplier_manage_details',
  method: 'post',
  data: data
})

import request from '@/utils/request'

/**
 * 选择菜品列表
 *
 */
export const apiMerchantMobileMealAccompanyingGetFoodListPost = data => request({
  url: '/merchant_mobile/meal_accompanying/get_food_list',
  method: 'post',
  data: data
})

/**
 * 陪餐记录
 *
 */
export const apiMerchantMobileMealAccompanyingMealAccompanyingListPost = data => request({
  url: '/merchant_mobile/meal_accompanying/meal_accompanying_list',
  method: 'post',
  data: data
})

/**
 * 陪餐记录保存
 *
 */
export const apiMerchantMobileMealAccompanyingMealAccompanyingSavePost = data => request({
  url: '/merchant_mobile/meal_accompanying/meal_accompanying_save',
  method: 'post',
  data: data
})

/**
 * 获取组织列表
 *
 */
export const apiBackgroundOrganizationOrganizationListPost = data => request({
  url: '/background_organization/organization/list',
  method: 'post',
  data: data
})

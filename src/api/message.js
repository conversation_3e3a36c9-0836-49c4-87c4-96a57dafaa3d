// 公告消息等接口存放
import request from '@/utils/request'

/**
 * 组织接收公告列表
 * @param { Object } data
 * @return { Promise }
 *
 */
export const apiBackgroundMessagesMessagesGetMsgList = data => request({
  url: '/background_messages/messages/get_msg_list',
  method: 'post',
  data: data
})

/**
 * 组织接收公告详情
 * @param { Object } data
 * @return { Promise }
 *
 */
export const apiBackgroundMessagesMessagesGetMsgReceive = data => request({
  url: '/background_messages/messages/get_msg_receive',
  method: 'post',
  data: data
})

/**
 * 公告列表
 * @param { Object } data
 * @return { Promise }
 *
 */
export const apiBackgroundMessagesMessagesList = data => request({
  url: '/background_messages/messages/list',
  method: 'post',
  data: data
})

/**
 * 编辑公告
 * @param { Object } data
 * @return { Promise }
 *
 */
export const apiBackgroundMessagesMessagesModify = data => request({
  url: '/background_messages/messages/modify',
  method: 'post',
  data: data
})
/**
 * 未读公告数量
 * @param { Object } data
 * @return { Promise }
 *
 */
export const apiBackgroundMessagesMessagesGetMsgNum = data => request({
  url: '/background_messages/messages/get_msg_num',
  method: 'post',
  data: data
})

/**
 * 删除公告
 * @param { Object } data msg_no 公告Id
 * @return { Promise }
 *
 */
export const apiBackgroundMessagesMessagesDelete = data => request({
  url: '/background_messages/messages/delete',
  method: 'post',
  data: data
})

/**
 * 发布公告
 * @param { Object } data msg_nos  公告Ids
 * @return { Promise }
 *
 */
export const apiBackgroundMessagesMessagesBulkPush = data => request({
  url: 'background_messages/messages/bulk_push',
  method: 'post',
  data: data
})

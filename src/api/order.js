// 存放订单类的api
import request from '@/utils/request'

/**
 * 订单管理 脱机扣款失败订单
 * @param  data page:number, page_size:number, trade_no:string, name:string, phone:string, person_no:string, payer_department_group_ids:array, order_status:string, payer_group_ids:array, consume_organization_ids:array, meal_type:string, start_create_time:string, end_create_time:string, upload_status:string}} param page Page,page_size Page size,trade_no 订单号,name 用户姓名,phone 手机号,person_no 用户编号,payer_department_group_ids ,order_status 支付状态,payer_group_ids ,consume_organization_ids ,meal_type 餐段,start_create_time 开始时间,end_create_time 结束时间,upload_status 上传状态
 * @return { Promise }
 *
 */
export const apiBackgroundOrderOrderOfflineList = data => request({
  url: '/background_order/order_offline/list',
  method: 'post',
  data: data
})

/**
 * 订单管理 离线订单关闭
 * @param  data order_payment_id 消费订单id is_original_price 走计次消费规则时，是否强制按原价扣款
 * @return { Promise }
 *
 */
export const apiBackgroundOrderOrderOfflineOrderClose = data => request({
  url: '/background_order/order_offline/order_close',
  method: 'post',
  data: data
})

/**
 * 订单管理 离线订单重新发起支付
 * @param  data order_payment_id 消费订单id is_original_price 走计次消费规则时，是否强制按原价扣款
 * @return { Promise }
 *
 */
export const apiBackgroundOrderOrderOfflineOrderPay = data => request({
  url: '/background_order/order_offline/order_pay',
  method: 'post',
  data: data
})
/**
 *收款码明细表
 * @param data page:1，page_size显示条目数,默认100，update_cache刷新缓存默认值:false  ， start_date string开始时间end_date，string，结束时间person_no string人员编号pard_no string卡号  name string姓名phone string 手机号org_ids 组织 array[integer]
 * @returns { Promise }
 * @description 收款码明细表
 */
export const getCollectionCode = data => request({
  url: '/background_report_center/data_report/instore_payment_detail_list',
  method: 'post',
  data: data
})

/**
 *消费订单堂食列表
 * @param data page:1，page_size显示条目数,默认100
 * @returns { Promise }
 * @description
 */
export const apiBackgroundOrderOrderPaymentOnSceneList = data => request({
  url: '/background_order/order_payment/on_scene_list',
  method: 'post',
  data: data
})

/**
 *审核订单列表
 * @param data page:1，page_size显示条目数,默认100
 * @returns { Promise }
 * @description
 */
export const apiBackgroundOrderOrderReviewList = data => request({
  url: '/background_order/order_review/list',
  method: 'post',
  data: data
})

/**
 *申请审批订单接口（移动端专用）
 * @param data page:1，page_size显示条目数,默认10
 * @returns { Promise }
 * @description
 */
export const apiMerchantMobileApprovalGetApprovalList = data => request({
  url: '/merchant_mobile/approval/get_approval_list',
  method: 'post',
  data: data
})

/**
 *
 * 申请订单查看详情（移动端专用）
 * @param data
 * @returns { Promise }
 * @description
 */
export const apimerchantMobileApprovalGetApprovalDetail = data => request({
  url: '/merchant_mobile/approval/get_approval_detail',
  method: 'post',
  data: data
})

/**
 *
 * 处理申诉订单
 * @param data
 * @returns { Promise }
 * @description
 */
export const apiBackgroundOrderOrderAppealDealOrderAppeal = data => request({
  url: '/background_order/order_appeal/deal_order_appeal',
  method: 'post',
  data: data
})

/**
 *
 * 审核操作
 * @param data review_status：success 已同意  reject  已拒绝 reject_reason 拒绝原因
 * @returns { Promise }
 * @description
 */
export const apiBackgroundOrderOrderReviewOrderReviewOperate = data => request({
  url: '/background_order/order_review/order_review_operate',
  method: 'post',
  data: data
})

/**
 *
 * 订单缴费申请操作
 * @param data approval_id：审批id ， approval_status：agree ，refuse   refuse_reason，拒绝理由
 * @returns { Promise }
 * @description
 */
export const apiBackgroundOrderOrderJiaofeiApprovalOperator = data => request({
  url: '/background_order/order_jiaofei/approval_operator',
  method: 'post',
  data: data
})

/**
 *
 * 订单审批-请假审批
 * @param data id：审批id ， for_leave_status：agree ，reject  merchant_remark，拒绝理由
 * @returns { Promise }
 * @description
 */
export const apiBackgroundAttendanceAttendanceForLeaveRecordModify = data => request({
  url: '/background_attendance/attendance_for_leave_record/modify',
  method: 'post',
  data: data
})

/**
 *
 * 订单审批-补卡审批
 * @param data id：审批id ， supplement_status：agree ，reject  merchant_remark，拒绝理由
 * @returns { Promise }
 * @description
 */
export const apiBackgroundAttendanceAttendanceSupplementRecordModify = data => request({
  url: '/background_attendance/attendance_supplement_record/modify',
  method: 'post',
  data: data
})

/**
 *
 * 代办审批订单的数量
 * @param data 不用传参
 * @returns { Promise }
 * @description
 */
export const apiMerchantMobileApprovalGetApprovalCount = data => request({
  url: '/merchant_mobile/approval/get_approval_count',
  method: 'post',
  data: data
})

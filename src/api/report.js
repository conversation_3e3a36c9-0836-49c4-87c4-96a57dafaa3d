// 此文件放置报表类的api
import request from '@/utils/request'

/**
 * 设备消费明细
 * @param {*} data  page页码  page_size每页展示数量
 * @returns
 */
export const apiBackgroundReportCenterDataReportDeviceConsumeList = data => request({
  url: '/background_report_center/data_report/device_consume_list',
  method: 'post',
  data: data
})

/* 部门消费汇总
 * @param {*} data  page页码  page_size每页展示数量
 * @returns
 */
export const apiBackgroundReportCenterDataReportDepartmentPaymentCollectList = data => request({
  url: '/background_report_center/data_report/department_payment_collect_list',
  method: 'post',
  data: data
})

/**
 * 收款码明细表
 * @param {*} data  page页码  page_size每页展示数量
 * @returns
 */
export const apiBackgroundReportCenterDataReportInstorePaymentDetailList = data => request({
  url: '/background_report_center/data_report/instore_payment_detail_list',
  method: 'post',
  data: data
})
/**
 * 收款码明细表（新 h5专用）
 * @param {*} data  page页码  page_size每页展示数量 org_id 组织ID start_date end_date 开始于结束时间
 * @returns
 */
export const apiMerchantMobileReportFormsGetPaymentDailyReport = data => request({
  url: '/merchant_mobile/report_forms/get_payment_daily_report',
  method: 'post',
  data: data
})

/**
 * 获取当前组织的餐段
 * @param {*} data  org_id* 食堂ID
}
 * @returns
 */
export const apiBackgroundReportCenterDataReportGetMealType = data => request({
  url: '/background_report_center/data_report/get_meal_type',
  method: 'post',
  data: data
})

/**
 * 营业报表数据
 * @param {*} data
 * params (object) start_date: 开始时间,
        end_date: 结束日期,
         time_type: 'day',
        date_type: 时间类型,
        ord_id: 组织
 * @returns
 */
export const apiBusinessReportsList = data => request({
  url: '/merchant_mobile/report_forms/get_business_report',
  method: 'post',
  data: data
})
/**
 * 经营情况数据
 * params (object) start_date: 开始时间,
        end_date: 结束日期,
         time_type: 'day',
        ord_id: 组织
 * @param {*} data
 * @returns
 */
export const apiBusinessSituationList = data => request({
  url: '/merchant_mobile/report_forms/get_business_data',
  method: 'post',
  data: data
})
/**
 * 食品一级分类列表
 * @param {*} data
 * @returns
 */
export const apiBackgroundFoodFoodSortListPost = data => request({
  url: '/background_food/food_sort/list',
  method: 'post',
  data: data
})
/**
 * 食品二级分类列表
 * @param {*} data
 * @returns
 */
export const apiBackgroundFoodFoodCategoryListPost = data => request({
  url: '/background_food/food_category/list',
  method: 'post',
  data: data
})

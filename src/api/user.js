import request from '@/utils/request'

/**
 * 用来看用户的基本信息，发卡挂失的操作列表
 * @param { Object } data
 * @return { Promise }
 * @description 卡用户管理
 *
 */
export const getCardServiceCardUserList = data => request({
  url: '/card_service/card_user/list',
  method: 'post',
  data: data
})

/**
 * 账号密码登录
 * @param {*} data
 * @returns
 */
export const login = data => request({
  url: '/background/login',
  method: 'post',
  data: data
})

/**
 * 手机验证码登录
 * @param {*} data
 * @returns
 */
export const loginVerification = data => request({
  url: '/background/verification_code',
  method: 'post',
  data: data
})

/**
 * 登出
 * @param {*} data
 * @returns
 */
export const logout = data => request({
  url: '/background/logout',
  method: 'post',
  data: data
})

/**
 *获取个人信息
 * @param {*} data
 * @returns
 */
export const getInfo = data => request({
  url: '/background/getInfo',
  method: 'post',
  data: data
})

/**
 *超管用户管理
 * @param {*} data
 * @returns
 */
export const getBackgroundAdminUserList = data => request({
  url: '/background/admin/user/list',
  method: 'post',
  data: data
})
/**
 *
 * @param {Object}
 * @returns { Promise }
 * @description 信息协议
 */

export const getbookingAgreement = data => request({
  url: 'background/admin/agreement/agreement_detail_by_type',
  method: 'post',
  data: data
})
/**
 *
 * @param {Object}
 * @returns { Promise }
 * @description 修改密码
 */

export const getfindpasswoad = data => request({
  url: 'background/find_password',
  method: 'post',
  data: data
})
/**
 *
 * @param {Object}
 * @returns { Promise }
 * @description 选择组织
 */

export const getbindOrg = data => request({
  url: 'background/bind_org',
  method: 'post',
  data: data
})

/**
 * 用户分组列表
 * @param {*} data
 */
export const getCardServiceCardUserGroupList = data => request({
  url: '/card_service/card_user_group/list',
  method: 'post',
  data: data
})

/**
 * 部门分组列表
 * @param {*} data
 */
export const getCardServiceCardDepartmentGroupList = data => request({
  url: '/card_service/card_department_group/list',
  method: 'post',
  data: data
})

/**
 * 根据用户传递的类型设置获取部门列表或者用户分组列表
 * @param {*} data
 * @param type  type :1 部门分组 ，type 2 用户分组
 */
export const getGroupListByType = function(data, type) {
  var urlPath = type === 1 ? '/card_service/card_department_group/list' : '/card_service/card_user_group/list'
  return request({
    url: urlPath,
    method: 'post',
    data: data
  })
}

/**
 * 批量冻结用户
 * @param {*} data
 *
 */
export const apiCardServiceCardUserBatchFreezeCardUser = function(data, type) {
  return request({
    url: '/card_service/card_user/batch_freeze_card_user',
    method: 'post',
    data: data
  })
}

/**
 * 挂失
 * @param {*} data
 *
 */
export const apiCardServiceCardOperateLoss = function(data, type) {
  return request({
    url: '/card_service/card_operate/loss',
    method: 'post',
    data: data
  })
}

/**
 * 取消挂失
 * @param {*} data
 *
 */
export const apiCardServiceCardOperateCancelLoss = function(data, type) {
  return request({
    url: '/card_service/card_operate/cancel_loss',
    method: 'post',
    data: data
  })
}

/**
 * 退卡
 * @param {*} data
 *
 */
export const apiCardServiceCardOperateCardQuit = function(data, type) {
  return request({
    url: '/card_service/card_operate/card_quit',
    method: 'post',
    data: data
  })
}
/**
 * 上传face
 * @param {*} data
 *
 */
export const apiBookingUserUploadUserFace = function(data, type) {
  return request({
    url: '/merchant_mobile/card_user/upload_user_face',
    method: 'post',
    data: data
  })
}

/**
 * 编辑卡用户
 * @param {*} data card_user_id 卡用户ID person_no 人员编号 person_name 姓名
 *
 */
export const apiCardServiceCardUserModify = function(data, type) {
  return request({
    url: '/card_service/card_user/modify',
    method: 'post',
    data: data
  })
}
// 获取用户多个项目点
export const apiBackgroundGetPhoneCompanyInfo = function(data) {
  return request({
    url: '/background/get_phone_company_info',
    method: 'post',
    data
  })
}
/**
 * 获取登录验证码图片
 * @param {*} data
 *
 */
export const apiBackgroundGetLoginVerifyCode = function(data, type) {
  return request({
    url: '/background/get_login_verify_code',
    method: 'post',
    data: data
  })
}

/**
 * 获取验证码
 * @param {*} data
 *
 */
export const getLoginVerifyCode = function(data, type) {
  return request({
    url: '/background/get_sms_verify_code',
    method: 'post',
    data: data
  })
}
/**
 * 上传图片
 * @param {*} data
 *
 */
export const apiMerchantMobileCommonUploadUserFacePost = function(data, type) {
  return request({
    url: '/merchant_mobile/common/upload_user_face',
    method: 'post',
    data: data
  })
}

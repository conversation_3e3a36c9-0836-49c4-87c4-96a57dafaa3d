// 放置常用静态json列表
import { $t } from '@/utils/i18n'
import imgPath from '@/common/imgBasePath'
export default {
  // 卡状态
  DIC_CARD_STATUS:
    [{
      name: '全部',
      value: ''
    }, {
      name: '使用中',
      value: 'ENABLE'
    }, {
      name: '未发卡',
      value: 'UNUSED'
    }, {
      name: '挂失',
      value: 'LOSS'
    }, {
      name: '退卡',
      value: 'QUIT'
    }],
  // 录入人脸
  DIC_UPLOAD_FACE: [
    {
      name: '全部',
      value: ''
    }, {
      name: '已录入',
      value: true
    }, {
      name: '未录入',
      value: false
    }
  ],
  // 账户状态
  DIC_USER_STATUS: [{
    name: '全部',
    value: ''
  }, {
    name: '使用中',
    value: 'ENABLE'
  }, {
    name: '冻结中',
    value: 'FREEZE'
  }, {
    name: '退户',
    value: 'PERSON_QUIT'
  }
  ],
  // 菜谱设置
  DIC_DISHES_SETTING: [{
    name: '周菜谱',
    value: 'week'
  }, {
    name: '月菜谱',
    value: 'month'
  }
  ],
  // 设备状态
  DIC_DEVICE_STATUS:
  [{
    name: '全部',
    value: ''
  }, {
    name: '离线',
    value: false
  }, {
    name: '在线',
    value: true
  }],
  // 性别列表
  DIC_USER_SEX:
   [{
     name: '男',
     value: 'MAN'
   }, {
     name: '女',
     value: 'WOMEN'
   }, {
     name: '其他',
     value: 'OTHER'
   }],
  // 评价时间列表
  DIC_RECOMMEND_DATE:
   [{
     name: '今天',
     value: 'today'
   }, {
     name: '最近7天',
     value: 'sevenday'
   }],
  // 评价回复状态列表
  DIC_RECOMMEND_REPLY_STATUS:
  [{
    name: '全部',
    value: ''
  },
  {
    name: '已回复',
    value: "reply"
  },
  {
    name: '未回复',
    value: 'no_reply'
  }
  ],
  // 评价类型列表
  DIC_RECOMMEND_REPLY_TYPE:
  [{
    name: '全部',
    value: ''
  },
  {
    name: '评价',
    value: "evaluation"
  },
  {
    name: '建议',
    value: "feedback"
  }],

  // 统计页面财务报表列表数据
  FINANCE_DATA_LIST: [
    {
      id: 1,
      name: $t('business.Situation'),
      img: imgPath.IMG_STATISTICS_01,
      jumpPath: '/pages_statistics/revenue/revenue',
      key: 'business_management'
    },
    {
      id: 2,
      name: $t('business.reports'),
      img: imgPath.IMG_STATISTICS_02,
      jumpPath: '/pages_statistics/business_reports/business',
      key: 'business_report'
    },
    {
      id: 4,
      name: $t('business.Sector.consumption'),
      img: imgPath.IMG_STATISTICS_04,
      jumpPath: '/pages_statistics/sector_consumption/sector_consumption',
      key: 'departmental_consumption'
    },
    {
      id: 5,
      name: $t('business.device.consumption'),
      img: imgPath.IMG_STATISTICS_05,
      jumpPath: '/pages_statistics/device_consumption/device_consumption',
      key: 'device_consumption'
    },
    {
      id: 7,
      name: $t('business.Collection.Daily'),
      img: imgPath.IMG_STATISTICS_07,
      jumpPath: '/pages_statistics/collection_code_daily/collection_code_daily',
      key: 'payment_code_daily_report'
    },
    {
      id: 10,
      name: $t('passenger.flow'),
      img: imgPath.IMG_STATISTICS_10,
      jumpPath: '/pages_statistics/passenger_flow/passenger_flow',
      key: 'passenger_flow'
    }
  ],
  // 统计页面概况列表数据
  OVERVIEW_DATA_LIST: [
    {
      id: 8,
      name: $t('business.Attendance.overview'),
      img: imgPath.IMG_STATISTICS_08,
      jumpPath: '/pages_statistics/attendance_overview/attendance_overview',
      key: 'attendance_dverview'
    },
    {
      id: 9,
      name: $t('business.Access.overview'),
      img: imgPath.IMG_STATISTICS_09,
      jumpPath: '/pages_statistics/pages/statistics/statistics'
    }

  ],
  // 首页应用通用功能列表数据
  HOME_DATA_LIST: [
    {
      id: 1,
      image: imgPath.IMG_INDEX_01,
      icon: imgPath.IMG_REMOVE_GRAY,
      judgment: true,
      jumpPath: '/pages_common_function/approval/approval',
      key: 'apply_approval',
      text: $t('title.application.approval')
    },
    {
      id: 2,
      image: imgPath.IMG_INDEX_02,
      icon: imgPath.IMG_REMOVE_GRAY,
      judgment: true,
      jumpPath: '/pages_common_function/user/user_manager',
      key: 'user_management',
      text: $t('page.user.userManager')
    },
    {
      id: 3,
      image: imgPath.IMG_INDEX_03,
      icon: imgPath.IMG_REMOVE_GRAY,
      judgment: true,
      jumpPath: '/pages_common_function/message/message',
      key: 'message_notification',
      text: $t('title.message.notification')
    },
    {
      id: 4,
      image: imgPath.IMG_INDEX_04,
      icon: imgPath.IMG_REMOVE_GRAY,
      judgment: true,
      jumpPath: '/pages_common_function/announcement_management/announcement_management',
      key: 'messages_management',
      text: $t('announcement')
    },
    {
      id: 5,
      image: imgPath.IMG_INDEX_05,
      icon: imgPath.IMG_REMOVE_GRAY,
      judgment: true,
      jumpPath: '/pages_common_function/invitation/invitation',
      key: 'visitor_invitation_management',
      text: $t('title.visitor.invitation')
    },
    {
      id: 6,
      image: imgPath.IMG_INDEX_06,
      icon: imgPath.IMG_REMOVE_GRAY,
      judgment: true,
      jumpPath: '/pages_common_function/device/device_manage',
      key: 'merchant_mobile_device_management',
      text: $t('title.device.management')
    },
    {
      id: 7,
      image: imgPath.IMG_INDEX_07,
      icon: imgPath.IMG_REMOVE_GRAY,
      judgment: true,
      jumpPath: '/pages_dining_room/menu/menu_manage',
      key: 'menu_manage',
      text: $t('Recipe.management') // 菜单管理
    },
    {
      id: 8,
      image: imgPath.IMG_INDEX_08,
      icon: imgPath.IMG_REMOVE_GRAY,
      judgment: true,
      jumpPath: '/pages_dining_room/food/food_storage_manage',
      key: 'put_meal_management',
      text: $t('title.food.storage.management')
    },
    {
      id: 9,
      image: imgPath.IMG_INDEX_09,
      icon: imgPath.IMG_REMOVE_GRAY,
      judgment: true,
      jumpPath: '/pages_dining_room/recommend/recommend',
      key: 'Evaluation_management',
      text: $t('title.evaluation.recommendations') // 评价管理
    },
    {
      id: 10,
      image: imgPath.IMG_INDEX_10,
      icon: imgPath.IMG_REMOVE_GRAY,
      judgment: true,
      jumpPath: '/pages_dining_room/order/order_deduction',
      key: 'order_deduction',
      text: $t('title.order.supplementary.deduction')
    },
    {
      id: 11,
      image: imgPath.IMG_INDEX_11,
      icon: imgPath.IMG_REMOVE_GRAY,
      judgment: true,
      key: 'mingchuliangzao',
      text: $t('title.bright.kitchen.bright.stove')
    },
    {
      id: 12,
      image: imgPath.IMG_OTHER_MENU_INVENTORY_BLUE,
      icon: imgPath.IMG_REMOVE_GRAY,
      judgment: true,
      jumpPath: '/pages_others/inventory/purchase_inventory',
      key: 'jinxiaocun',
      text: $t('page.service.menu.purchase.sales.inventory') // 进销存
    },
    {
      id: 13,
      image: imgPath.IMG_SMART_WARNING,
      icon: imgPath.IMG_ADD_ORANGE,
      judgment: true,
      jumpPath: '/pages_supervise/smart_warning/smart_warning',
      key: 'Intelligent_early_warning',
      text: $t('page.service.menu.smart.warning') // 监管
    },
    {
      id: 14,
      image: imgPath.IMG_IC_ACCOMPANYING,
      icon: imgPath.IMG_REMOVE_GRAY,
      judgment: true,
      jumpPath: '/pages_others/meal/accompanying_meal_record',
      key: 'meal_accompanying',
      text: $t('page.service.menu.accompanying.meal.record') // 陪餐记录
    },
    {
      id: 17,
      image: imgPath.IMG_INDEX_15,
      icon: imgPath.IMG_REMOVE_GRAY,
      judgment: true,
      jumpPath: '/pages_dining_room/dailyInspection/index',
      key: 'daily_inspection',
      text: $t('title.daily.inspection')
    }
  ],
  // 全部服务通用功能列表数据
  GENERAL_DATA_LIST: [
    {
      id: 1,
      image: imgPath.IMG_INDEX_01,
      icon: imgPath.IMG_ADD_ORANGE,
      judgment: true,
      jumpPath: '/pages_common_function/approval/approval',
      key: 'apply_approval',
      text: $t('title.application.approval')
    },
    {
      id: 2,
      image: imgPath.IMG_INDEX_02,
      icon: imgPath.IMG_ADD_ORANGE,
      judgment: true,
      jumpPath: '/pages_common_function/user/user_manager',
      key: 'user_management',
      text: $t('page.user.userManager')
    },
    {
      id: 3,
      image: imgPath.IMG_INDEX_03,
      icon: imgPath.IMG_ADD_ORANGE,
      judgment: true,
      jumpPath: '/pages_common_function/message/message',
      key: 'message_notification',
      text: $t('title.message.notification')
    },
    {
      id: 4,
      image: imgPath.IMG_INDEX_04,
      icon: imgPath.IMG_ADD_ORANGE,
      judgment: true,
      jumpPath: '/pages_common_function/announcement_management/announcement_management',
      key: 'messages_management',
      text: $t('announcement')
    },
    {
      id: 5,
      image: imgPath.IMG_INDEX_05,
      icon: imgPath.IMG_ADD_ORANGE,
      judgment: true,
      jumpPath: '/pages_common_function/invitation/invitation',
      key: 'visitor_invitation_management',
      text: $t('title.visitor.invitation')
    },
    {
      id: 6,
      image: imgPath.IMG_INDEX_06,
      icon: imgPath.IMG_ADD_ORANGE,
      judgment: true,
      jumpPath: '/pages_common_function/device/device_manage',
      key: 'merchant_mobile_device_management',
      text: $t('title.device.management')
    }
  ],
  // 全部服务食堂功能列表数据
  CANTEEN_DATA_LIST: [
    {
      id: 7,
      image: imgPath.IMG_INDEX_07,
      icon: imgPath.IMG_ADD_ORANGE,
      judgment: true,
      jumpPath: '/pages_dining_room/menu/menu_manage',
      key: 'menu_manage',
      text: $t('Recipe.management') // 菜单管理
    },
    {
      id: 8,
      image: imgPath.IMG_INDEX_08,
      icon: imgPath.IMG_ADD_ORANGE,
      judgment: true,
      jumpPath: '/pages_dining_room/food/food_storage_manage',
      key: 'put_meal_management',
      text: $t('title.food.storage.management')
    },
    {
      id: 9,
      image: imgPath.IMG_INDEX_09,
      icon: imgPath.IMG_ADD_ORANGE,
      judgment: true,
      jumpPath: '/pages_dining_room/recommend/recommend',
      key: 'Evaluation_management',
      text: $t('title.evaluation.recommendations') // 评价管理
    },
    {
      id: 10,
      image: imgPath.IMG_INDEX_10,
      icon: imgPath.IMG_ADD_ORANGE,
      judgment: true,
      jumpPath: '/pages_dining_room/order/order_deduction',
      key: 'order_deduction',
      text: $t('title.order.supplementary.deduction')
    },
    {
      id: 11,
      image: imgPath.IMG_INDEX_11,
      icon: imgPath.IMG_ADD_ORANGE,
      judgment: true,
      key: 'mingchuliangzao',
      text: $t('title.bright.kitchen.bright.stove')
    },
    {
      id: 17,
      image: imgPath.IMG_INDEX_15,
      icon: imgPath.IMG_ADD_ORANGE,
      judgment: true,
      jumpPath: '/pages_dining_room/dailyInspection/index',
      key: 'daily_inspection',
      text: $t('title.daily.inspection')
    }
  ],
  // 其他功能列表数据
  OTHERS_DATA_LIST: [
    {
      id: 12,
      image: imgPath.IMG_OTHER_MENU_INVENTORY_BLUE,
      icon: imgPath.IMG_ADD_ORANGE,
      judgment: true,
      jumpPath: '/pages_others/inventory/purchase_inventory',
      key: 'jinxiaocun',
      text: $t('page.service.menu.purchase.sales.inventory') // 进销存
    },
    {
      id: 14,
      image: imgPath.IMG_IC_ACCOMPANYING,
      icon: imgPath.IMG_ADD_ORANGE,
      judgment: true,
      jumpPath: '/pages_others/meal/accompanying_meal_record',
      key: 'meal_accompanying',
      text: $t('page.service.menu.accompanying.meal.record') // 陪餐记录
    }
  ],
  SUPERVISE_DATA_LIST: [
    {
      id: 13,
      image: imgPath.IMG_SMART_WARNING,
      icon: imgPath.IMG_ADD_ORANGE,
      judgment: true,
      jumpPath: '/pages_supervise/smart_warning/smart_warning',
      key: 'Intelligent_early_warning',
      text: $t('page.service.menu.smart.warning') // 监管
    }
  ],
  // 设备消费统计情况列表
  DIC_CONSUMPTION_TOTAL_LIST:
  [{
    name: '消费总额',
    value: 'consume',
    number: ''
  },
  {
    name: '退款总额',
    value: 'refund_fee',
    number: ''
  },
  {
    name: '实收总额',
    value: "real_fee",
    number: ''
  },
  {
    name: '消费笔数',
    value: 'consume_count',
    number: ''
  }],
  // 设备消费时间选择
  DIC_CONSUMPTION_DATE_LIST:
  [{
    name: '今天',
    value: 'today',
    number: 1
  }, {
    name: '最近7天',
    value: 'sevenday',
    number: 7
  },
  {
    name: '最近30天',
    value: 'month',
    number: 30
  }
  ],
  // 部门消费时间选择
  DIC_DEPARTMENT_DATE_LIST:
  [{
    name: '昨天',
    value: 'yesterday',
    number: 1
  }, {
    name: '最近7天',
    value: 'sevenday',
    number: 7
  },
  {
    name: '最近30天',
    value: 'month',
    number: 30
  }
  ],
  // 餐段的tabs
  MEALSEGMENTS_LIST: [
    {
      name: $t('Meal.Breakfast'),
      mealType: 'breakfast'
    },
    {
      name: $t('Meal.lunch'),
      mealType: 'lunch'
    },
    {
      name: $t('Meal.tea'),
      mealType: 'afternoon'
    },
    {
      name: $t('Meal.dinner'),
      mealType: 'dinner'
    },
    {
      name: $t('Meal.supper'),
      mealType: 'supper'
    },
    {
      name: $t('Meal.early'),
      mealType: 'morning'
    }
  ],
  // 餐段的数据
  STATISTICS_DATA_LIST: [
    { name: $t('business.turnover'), key: 'total_amount', value: 0 },
    { name: $t('business.actual.amount.received'), key: 'real_income', value: 0 },
    { name: $t('business.Refund.amount'), key: 'refund_amount', value: 0 },
    { name: $t('business.how.many.consumed'), key: 'count', value: 0 }
  ],
  // 设备消费页面设备统计tabs
  DEVICE_STATISTICS_LIST: [
    {
      name: $t('business.Total.consumption')
    },
    {
      name: $t('business.Refund.amount')
    },
    {
      name: $t('business.actual.amount.received')
    },
    {
      name: $t('business.how.many.consumed')
    }
  ],
  // 收款码日报页面的餐段统计数据
  COLLECTION_CODE_DATA_LIST: [
    {
      name: $t('Meal.Breakfast'),
      value: 0,
      key: 'breakfast'
    },
    {
      name: $t('Meal.lunch'),
      value: 0,
      key: 'lunch'
    },
    {
      name: $t('Meal.tea'),
      value: 0,
      key: 'afternoon'
    },
    {
      name: $t('Meal.dinner'),
      value: 0,
      key: 'dinner'
    },
    {
      name: $t('Meal.supper'),
      value: 0,
      key: 'supper'
    },
    {
      name: $t('Meal.early'),
      value: 0,
      key: 'morning'
    }
  ],
  // 部门消费统计情况列表
  DIC_DEPARTMENT_CONSUMPTION_TOTAL_LIST:
   [{
     name: '消费总额',
     value: 'pay_fee',
     number: ''
   },
   {
     name: '优惠金额',
     value: 'discount_fee',
     number: ''
   },
   {
     name: '次数消费',
     value: "jc_count",
     number: ''
   },
   {
     name: '消费笔数',
     value: 'consume_count',
     number: ''
   }],
  // 部门消费统计餐段统计列表
  DIC_DEPARTMENT_CONSUMPTION_MEAL_LIST:
  [{
    name: $t('business.Total.consumption'),
    value: ''
  },
  {
    name: $t('business.preferential.amount'),
    value: ''
  },
  {
    name: $t('business.number.consumption'),
    value: ""
  },
  {
    name: $t('business.how.many.consumed'),
    value: ''
  }],
  // 首页营业数据
  BUSINESS_DATA_LISTS: [
    { label: $t('business.turnover'), value: 0, key: 'turnover' },
    { label: $t('business.how.many.consumed'), value: 0, key: 'consume_order_count' },
    { label: $t('business.number.of.consumption'), value: 0, key: 'consume_person_count' }
  ],
  BUSINESS_TRENDS_DATA_LISTS: [
    { label: $t('Meal.Breakfast'), value: 0, key: 'breakfast_consume' },
    { label: $t('Meal.lunch'), value: 0, key: 'lunch_consume' },
    { label: $t('Meal.tea'), value: 0, key: 'afternoon_consume' },
    { label: $t('Meal.dinner'), value: 0, key: 'dinner_consume' },
    { label: $t('Meal.supper'), value: 0, key: 'supper_consume' },
    { label: $t('Meal.early'), value: 0, key: 'morning_consume' }
  ],
  // 收款码统计时间选择
  DIC_COLLECTION_CODE_DATE_LIST:
    [{
      name: $t('That.Today'),
      value: 'yesterday',
      number: 1
    }, {
      name: $t('That.sevendays'),
      value: 'sevenday',
      number: 7
    },
    {
      name: $t('That.thirtydays'),
      value: 'month',
      number: 30
    }
    ],
  // 申请审批时间选择
  DIC_APPROVAL_DATE_LIST: [
    {
      name: $t('dic.date.all.time'),
      value: '',
      number: 0
    },
    {
      name: $t('dic.date.within.three.day'),
      value: 'sevenday',
      number: 3
    },
    {
      name: $t('dic.date.within.seven.day'),
      value: 'month',
      number: 7
    },
    {
      name: $t('dic.date.within.one.week'),
      value: 'month',
      number: 30
    },

    {
      name: $t('dic.date.within.three.week'),
      value: 'month',
      number: 90
    }
  ],
  DIC_SMART_WARNING_TIME_LIST: [
    {
      name: $t('dic.date.all.time'),
      value: '',
      number: 0
    },
    {
      name: $t('dic.date.within.three.month'),
      value: '',
      number: 3
    },
    {
      name: $t('dic.date.within.six.month'),
      value: '',
      number: 6
    }
  ],
  DIC_SMART_WARNING_TYPE_LIST0: [
    {
      name: $t('dic.date.all.type'),
      value: '',
      key: ''
    },
    {
      name: $t('dic.type.profit.margin.loss'),
      value: 'loss',
      key: 'loss'
    },
    {
      name: $t('dic.type.profit.margin.earnings'),
      value: 'surplus',
      key: 'surplus'
    },
    {
      name: $t('dic.type.proportion.of.raw.materials'),
      value: 'raw_material_percentage',
      key: 'raw_material_percentage'
    }
  ],
  DIC_SMART_WARNING_TYPE_LIST1: [
    {
      name: $t('dic.date.all.type'),
      value: '',
      key: ''
    },
    {
      name: $t('dic.type.get.materials'),
      value: 'RECEIVE_EXIT',
      key: 'RECEIVE_EXIT'
    },
    {
      name: $t('dic.type.allot.out'),
      value: 'BORROW_EXIT',
      key: 'BORROW_EXIT'
    },
    {
      name: $t('dic.type.wastage'),
      value: 'EXPEND_EXIT',
      key: 'EXPEND_EXIT'
    },
    {
      name: $t('dic.type.return.goods'),
      value: 'REFUND_EXIT',
      key: 'REFUND_EXIT'
    },
    {
      name: $t('dic.type.other.out'),
      value: 'OTHER_EXIT',
      key: 'OTHER_EXIT'
    },
    {
      name: $t('dic.type.purchase'),
      value: 'PURCHASE_ENTRY',
      key: 'PURCHASE_ENTRY'
    },
    {
      name: $t('dic.type.allot.in'),
      value: 'BORROW_ENTRY',
      key: 'BORROW_ENTRY'
    },
    {
      name: $t('dic.type.other.in'),
      value: 'OTHER_ENTRY',
      key: 'OTHER_ENTRY'
    },
    {
      name: $t('dic.type.purchase.order'),
      value: 'ORDER_PURCHASE',
      key: 'ORDER_PURCHASE'
    }
  ],
  DIC_SMART_WARNING_TYPE_LIST3: [
    {
      name: $t('dic.date.all.type'),
      value: '',
      key: ''
    },
    {
      name: $t('dic.type.catering.service.license'),
      value: 'catering_service_license',
      key: 'catering_service_license'
    },
    {
      name: $t('dic.type.food.business.license'),
      value: 'food_business_license',
      key: 'food_business_license'
    },
    {
      name: $t('dic.type.food.production.license'),
      value: 'food_production_license',
      key: 'food_production_license'
    },
    {
      name: $t('dic.type.sanitary.permit'),
      value: 'hygienic_license',
      key: 'hygienic_license'
    },
    {
      name: $t('dic.type.health.certificate'),
      value: 'healthy_license',
      key: 'healthy_license'
    },
    {
      name: $t('dic.type.supplier.entry.certificate'),
      value: 'supplier_shortlisted_contract',
      key: 'supplier_shortlisted_contract'
    }
  ],
  // 申请申请类型选择
  DIC_APPROVAL_TYPE_LIST: [
    {
      name: $t('page.approval.order.refund.request'),
      value: 'order_review',
      key: 'refund_apply'
    },
    {
      name: $t('dic.type.refund.appeal'),
      value: 'order_appeal',
      key: 'refund_apply'
    },
    {
      name: $t('dic.type.visitor.application'),
      value: 'visitor_apply',
      key: 'visitor_apply'
    },
    {
      name: $t('dic.type.payment.refund'),
      value: 'order_jiaofei',
      key: 'jf_refund'
    },
    {
      name: $t('dic.type.card.replacement.application'),
      value: 'attendance_supplement',
      key: 'card_replacement_apply'
    },
    {
      name: $t('dic.type.leave.application'),
      value: 'attendance_for_leave',
      key: 'leave_apply'
    }
  ],
  CONSUME_COUNT_DATA: [
    // 交易笔数分析数据
    {
      id: 1,
      color: '#42cba0',
      name: '扣费消费',
      key: 'kf_consume',
      labelShow: false,
      value: 0
    },
    {
      id: 2,
      color: '#4da8f5',
      name: '计次消费',
      labelShow: false,
      key: 'jc_consume',
      value: 0
    },
    {
      id: 3,
      color: '#ffb626',
      name: '预约消费',
      labelShow: false,
      key: 'reservation_consume',
      value: 0
    },
    {
      id: 4,
      color: '#fd953c',
      name: '报餐消费',
      labelShow: false,
      key: 'report_meal_consume',
      value: 0
    },
    {
      id: 5,
      color: '#fa6f71',
      name: '称重消费',
      labelShow: false,
      key: 'buffet_consume',
      value: 0
    }
  ],
  TIME_SWITCHING: [
    {
      name: $t('That.day'),
      type: 'day'
    },
    {
      name: $t('That.Week'),
      type: 'week'
    },
    {
      name: $t('That.Month'),
      type: 'month'
    },
    {
      name: $t('That.definition'),
      type: 'all'
    }
  ],
  WALLETUSAGE_DATA: [
    // 钱包类型使用情况
    {
      id: 1,
      color: '#fd953c',
      name: '补贴类',
      type: 'subsidy',
      labelShow: false,
      value: 0
    },
    {
      id: 2,
      color: '#ffb626',
      name: '储值类',
      type: 'store',
      labelShow: false,
      value: 0
    },
    {
      id: 3,
      color: '#fa6f71',
      name: '赠送类',
      type: 'complimentary',
      labelShow: false,
      value: 0
    }
  ],
  CONSTITUTE_DATA: [
    {
      name: $t('manner.Payment.type'),
      type: 'payinfo_consume_data'
    },
    {
      name: $t('Meal'),
      type: 'meal_type_consume_data'
    },
    {
      name: $t('manner.How.to.eat'),
      type: 'order_type_consume_data'
    },
    {
      name: $t('manner.Dishes'),
      type: 'food_sales_consume_data'
    }
  ],
  // 钱包分析
  WALLE_STATISTI: [
    {
      name: '提现金额',
      value: 0,
      type: 'withdraw'
    },
    {
      name: '充值金额',
      value: 0,
      type: 'charge_fee'
    },
    {
      name: '退款金额',
      value: 0,
      type: 'refund_fee'
    },
    {
      name: '消费金额',
      value: 0,
      type: 'consume_fee'
    },
    {
      name: '储值钱包余额',
      value: 0,
      type: 'wallet_balance'
    },
    {
      name: '充值退款金额',
      value: 0,
      type: 'charge_refund_fee'
    }
  ],
  SUBSIDY_STATISTIC: [
    {
      name: '消费金额',
      value: 0,
      type: 'subsidy_fee'
    },
    {
      name: '补贴发放金额',
      value: 0,
      type: 'subsidy_add_fee'
    },
    {
      name: '补贴余额',
      value: 0,
      type: 'subsidy_balance'
    },
    {
      name: '补贴清零金额',
      value: 0,
      type: 'subsidy_clear_fee'
    }
  ],
  COMPLIMENTARY_STATISTIC: [
    {
      name: '消费金额',
      value: 0,
      type: 'complimentary_fee'
    },
    {
      name: '赠送发放金额',
      value: 0,
      type: 'complimentary_add_fee'
    },
    {
      name: ' 赠送余额',
      value: 0,
      type: 'complimentary_balance'
    },
    {
      name: '赠送清零金额',
      value: 0,
      type: 'complimentary_clear_fee'
    }
  ],
  STATISTIC_TABS: [
    {
      name: '储值钱包',
      key: 'walle_tstatistic'
    },
    {
      name: '补贴钱包',
      key: 'subsidy_statistic'
    },
    {
      name: '赠送钱包',
      key: 'complimentary_statistic'
    }
  ],
  TREND_ONE_DATA: [
    // 营业额分析
    {
      name: '营业额',
      key: 'total_amount'
    },
    {
      name: '消费笔数',
      key: 'total_count'
    }
  ],
  TREND_TWO_DATA: [
    // 营业额分析
    {
      name: '营业额',
      key: 'total_amount'
    },
    {
      name: '消费笔数',
      key: 'total_count'
    }
  ],
  QUERY_DATA_LIST: [
    $t('Meal.Breakfast'),
    $t('Meal.lunch'),
    $t('Meal.tea'),
    $t('Meal.dinner'),
    $t('Meal.supper'),
    $t('Meal.early')
  ],
  TRADE_OPTS: {
    rotate: false,
    rotateLock: false,
    color: ['#fd953c', '#ffb626', '#fa6f71'],
    padding: [5, 5, 5, 5],
    dataLabel: true,
    enableScroll: false,
    legend: {
      show: false,
      position: 'right',
      lineHeight: 25
    },
    title: {
      name: '',
      fontSize: 20,
      color: '#000'
    },
    subtitle: {
      name: '交易笔数',
      fontSize: 8,
      color: '#666'
    },
    extra: {
      ring: {
        ringWidth: 15,
        activeOpacity: 0.5,
        activeRadius: 5,
        customRadius: 60,
        offsetAngle: 0,
        labelWidth: 15,
        border: true,
        borderWidth: 3,
        borderColor: '#FFFFFF'
      }
    }
  },
  PURES_OPTS: {
    rotate: false,
    rotateLock: false,
    color: ['#fd953c', '#ffb626', '#fa6f71'],
    padding: [5, 5, 5, 5],
    dataLabel: true,
    enableScroll: false,
    legend: {
      show: false,
      position: 'right',
      lineHeight: 25
    },
    title: {
      name: '',
      fontSize: 20,
      color: '#000'
    },
    subtitle: {
      name: '交易笔数',
      fontSize: 8,
      color: '#666'
    },
    extra: {
      ring: {
        ringWidth: 15,
        activeOpacity: 0.5,
        activeRadius: 5,
        customRadius: 60,
        offsetAngle: 0,
        labelWidth: 15,
        border: true,
        borderWidth: 3,
        borderColor: '#FFFFFF'
      }
    }
  },
  LINE_OPTS: {
    color: ['#fd953c'],
    legend: {
      show: false
    },
    update: true,
    dataLabel: false,
    padding: [30, 30, 10, 20],
    xAxis: {
      disableGrid: true,
      boundaryGap: 'justify',
      fontSize: 13,
      marginTop: 15,
      labelCount: 7
    },
    yAxis: {
      disabled: true,
      gridType: 'dash',
      axisline: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    extra: {
      area: {
        type: 'curve',
        opacity: 0.4,
        addLine: true,
        width: 2,
        gradient: true,
        activeType: 'hollow'
      }
    }
  }
}

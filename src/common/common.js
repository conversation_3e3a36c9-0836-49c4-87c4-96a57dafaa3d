export default {
  // 放置全局定义的常量 常量注意统一大写
  MSG_REPLY_SUCCESS: 'msg_reply_success', // 消息回复成功
  CACHE_HISTORY_GOODS: 'cache_history_goods', // 历史菜品
  API_TOKEN: 'api_token', // 秘钥
  API_ORGS: 'api_orgs', // 所选组织
  KEY_USER_INFO: 'key_user_info', // 个人信息
  KEY_PERSON_USER_INFO: 'key_person_user_info', // 存储人员信息key
  KEY_RECOMMEND_ITEM_INFO: 'key_recommend_item_info', // 存储评价反馈的项key
  KEY_USER_ORGAN: 'user_organ', // 存放部门组织的key  建涛注意后续要改成驼峰或者下划线模式
  KEY_APPROVAL_ITEM_INFO: 'key_approval_item_info', // 存储申请审批每个子项的key
  KEY_MERCHANT_APP_PERMISSION_KEYS: 'merchant_app_permission_keys', // 存储用户的页面权限key
  MSG_APPROVAL_SUCCESS: 'msg_approval_success', // 订单审批页面操作成功信息
  TITLE_DEPARTMENT_NAME: 'title_department_name', // 用户管理部门名称key
  KEY_FACE_PATH: 'Key_face_path', // 人脸路径path
  MSG_UPLOAD_FACE_SUCCESS: 'key_upload_face_success', // 人脸上传成功，
  MSG_CHOOSE_ORGN_SUCCESS: 'msg_choose_orgn_success', // 消息选择组织成功
  MSG_UPDATE_MENU_SUCCESS: 'msg_update_menu_success', // 消息更新常用菜单成功
  MSG_UPDATE_UNREAD_BACK: 'msg_update_unread_back', // 消息跟代办用户返回要重新获取已读跟未读
  MSG_UPDATE_PERSON_INFO: 'msg_update_person_info', // 个人信息更新成功返回
  MSG_UPDATE_GOODS_ADD: 'msg_update_goods_add', // 添加菜品成功返回
  KEY_UPDATE_GOODS_INFO: 'key_update_goods_info', // 更新商品信息
  KEY_COOKIES: 'cookie',
  KEY_UPLOAD_PERSON_PIC: 'key_upload_person_pic', // 上传人员信息图片成功返回
  KEY_INVENTORY_INFO: 'key_inventory_info', // 进销存收货暂存信息
  KEY_INVENTORY_ORDER_ITEM: 'key_inventory_order_item', // 进销存收货单据信息
  KEY_ACCOMPANYING_ITEM_INFO: 'key_accompanying_item_info', // 陪餐详情的项key
  KEY_ACCOMPANYING_SIGN_IMG: 'key_accompanying_sign_img', // 陪餐签名图片
  KEY_ACCOMPANYING_TAKE_IMG: 'key_accompanying_take_img', // 陪餐拍照图片
  KEY_ACCOMPANYING_TAKE_IMG_NAME: 'key_accompanying_take_img_name', // 陪餐拍照图片名称
  KEY_ACCOMPANYING_TAKE_IMG_LIST: 'key_accompanying_take_img_list' // 陪餐拍照图片列表
}

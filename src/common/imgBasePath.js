
import basePath from '../config/index'
var IMG_BASE = basePath.imgUrl
var IMG_MAPP = basePath.baseImgAppURL
export default {
  IMG_BG_EMPTY_GRAY: IMG_BASE + '/img_bg_empty_gray.png',
  IMG_LOCATION_LEFT_GRAY: IMG_BASE + '/img_location_left_gray.png',
  IMG_RECOMMEND_TITLE_GRAY: IMG_BASE + '/img_recommend_title_gray.png',
  IMG_APPROVAL_GREEN: IMG_BASE + '/img_approval_green.png',
  IMG_APPROVAL_RED: IMG_BASE + '/img_approval_red.png',
  IMG_APPROVAL_ORANGE: IMG_BASE + '/img_approval_orange.png',
  IMG_ADD_GRAY: IMG_BASE + '/img_add_gray.png',
  IMG_INDEX_BG_ORANGE: IMG_BASE + '/img_index_bg_orange.png',
  IMG_REQUIRE_ORANGE: IMG_BASE + '/img_require_orange.png',
  IMG_HEAD_DEFAULT: IMG_BASE + '/img_head_default.png',
  IMG_MORE_BLACK: IMG_BASE + '/img_more_black.png',
  IMG_INDEX_HOME_ACTIVE: IMG_BASE + '/img_index_home_active.png',
  IMG_INDEX_MIDDLE_DEFAULT: IMG_BASE + '/img_index_middle_default.png',
  IMG_INDEX_MINE_DEFAULT: IMG_BASE + '/img_index_mine_default.png',
  IMG_INDEX_HOME_DEFAULT: IMG_BASE + '/img_index_home_default.png',
  IMG_INDEX_MIDDLE_ACTIVE: IMG_BASE + '/img_index_middle_active.png',
  IMG_INDEX_MINE_ACTIVE: IMG_BASE + '/img_index_mine_active.png',
  IMG_GOODS_EMPTY_GRAY: IMG_BASE + '/img_goods_empty_gray.png',
  IMG_GOODS_ACTIVE_ORANGE: IMG_BASE + '/img_goods_active_orange.png',
  IMG_ARROW_DOWN_BLACK: IMG_BASE + '/img_arrow_down_black.png',
  IMG_EYE_GRAY: IMG_BASE + '/img_eye_gray.png',
  IMG_FEMALE_PINK: IMG_BASE + '/img_female_pink.png',
  IMG_MALE_BLUE: IMG_BASE + '/img_male_blue.png',
  IMG_STAR_ORGNGE: IMG_BASE + '/img_star_orgnge.png',
  IMG_STAR_GRAY: IMG_BASE + '/img_star_gray.png',
  IMG_CHECK_ACTIVE: IMG_BASE + '/img_check_active.png',
  IMG_CHECK_DEFAULT: IMG_BASE + '/img_check_default.png',
  IMG_WEIXIN_GREEN: IMG_BASE + '/img_weixin_green.png',
  IMG_STATISTICS_01: IMG_BASE + '/img_statistics_01.png',
  IMG_STATISTICS_02: IMG_BASE + '/img_statistics_02.png',
  IMG_STATISTICS_04: IMG_BASE + '/img_statistics_04.png',
  IMG_STATISTICS_05: IMG_BASE + '/img_statistics_05.png',
  IMG_STATISTICS_07: IMG_BASE + '/img_statistics_07.png',
  IMG_STATISTICS_08: IMG_BASE + '/img_statistics_08.png',
  IMG_STATISTICS_09: IMG_BASE + '/img_statistics_09.png',
  IMG_STATISTICS_10: IMG_BASE + '/img_statistics_10.png',
  IMG_INDEX_01: IMG_BASE + '/img_index_01.png',
  IMG_INDEX_02: IMG_BASE + '/img_index_02.png',
  IMG_INDEX_03: IMG_BASE + '/img_index_03.png',
  IMG_INDEX_04: IMG_BASE + '/img_index_04.png',
  IMG_INDEX_05: IMG_BASE + '/img_index_05.png',
  IMG_INDEX_06: IMG_BASE + '/img_index_06.png',
  IMG_INDEX_07: IMG_BASE + '/img_index_07.png',
  IMG_INDEX_08: IMG_BASE + '/img_index_08.png',
  IMG_INDEX_09: IMG_BASE + '/img_index_09.png',
  IMG_INDEX_10: IMG_BASE + '/img_index_10.png',
  IMG_INDEX_11: IMG_BASE + '/img_index_11.png',
  IMG_INDEX_15: IMG_BASE + '/img_index_15.png',
  IMG_INDEX_BG_ORANGE_BIG: IMG_BASE + '/img_index_bg_orange_big.png',
  IMG_AGENT_01: IMG_BASE + '/img_agent_01.png',
  IMG_AGENT_02: IMG_BASE + '/img_agent_02.png',
  IMG_AGENT_03: IMG_BASE + '/img_agent_03.png',
  IMG_MORE_WHITE: IMG_BASE + '/img_more_white.png',
  IMG_ADD_ORANGE: IMG_BASE + '/img_add_orange.png',
  IMG_REMOVE_GRAY: IMG_BASE + '/img_remove_gray.png',
  IMG_EXIT_WHITE: IMG_BASE + '/img_exit_white.png',
  IMG_INDEX_BG_ORANGE_SM: IMG_BASE + '/img_index_bg_orange_sm.png',
  IMG_INDEX_BG_WHITE: IMG_BASE + '/img_index_bg_white.png',
  IMG_FACE: '/pages_common_function/static/image/face.png',
  IMG_FACE_PHOTO: '/pages_common_function/static/image/photho.png',
  IMG_FACE_REVERSAL: '/pages_common_function/static/image/reversal.png',
  IMG_ARROW_BOTTOM_RED: '/static/icon/img_arrow_bottom_red.png',
  IMG_ARROW_TOP_GREEN: '/static/icon/img_arrow_top_green.png',
  IMG_ARROW_TOP_WHITE: '/static/icon/img_arrow_top_white.png',
  IMG_ARROW_BOTTOM_WHITE: '/static/icon/img_arrow_bottom_white.png',
  IMG_DEFAULT: '/static/image/default.png',
  IMG_GOOD_DEFAULT: '/static/image/good_default.png',
  IMG_OTHER_ADD_ORANGE: IMG_BASE + '/ic_add_orange.png',
  IMG_OTHER_ARROW_SUCCESS: IMG_BASE + '/ic_arrow_success.png',
  IMG_OTHER_MENU_GOOD_ORANGE: IMG_BASE + '/ic_menu_good_orange.png',
  IMG_OTHER_MENU_INVENTORY_BLUE: IMG_BASE + '/ic_menu_inventory_blue.png',
  IMG_SMART_WARNING: IMG_BASE + '/img_smart_warning.png',
  IMG_IC_ACCOMPANYING: IMG_BASE + '/ic_accompanying.png',
  IMG_IC_CAMERA_KUANG: IMG_BASE + '/ic_camera_kuang.png',
  IMG_IC_DELETE_BLACK: IMG_BASE + '/ic_delete_black.png',
  IMG_IC_DELETE_RED: IMG_BASE + '/ic_delete_red.png',
  IMG_IC_DELETE_RED_LAJI: IMG_BASE + '/ic_delete_red_laji.gif',
  IMG_IC_ORANGE_ARROW_DOWN: IMG_BASE + '/ic_orange_arrow_down.png',
  IMG_IC_START_ACTIVE_ORANGE: IMG_BASE + '/ic_start_active_orange.png',
  IMG_IC_START_DEFAULT_GRAY: IMG_BASE + '/ic_start_default_gray.png',
  IMG_RATE1_0: IMG_MAPP + 'common/supervision/' + 'rate1_0.png',
  IMG_RATE1_1: IMG_MAPP + 'common/supervision/' + 'rate1_1.png',
  IMG_RATE2_0: IMG_MAPP + 'common/supervision/' + 'rate2_0.png',
  img_rate2_1: IMG_MAPP + 'common/supervision/' + 'rate2_1.png',
  img_rate3_0: IMG_MAPP + 'common/supervision/' + 'rate3_0.png',
  img_rate3_1: IMG_MAPP + 'common/supervision/' + 'rate3_1.png',
  img_rate4_0: IMG_MAPP + 'common/supervision/' + 'rate4_0.png',
  img_rate4_1: IMG_MAPP + 'common/supervision/' + 'rate4_1.png',
  img_rate5_0: IMG_MAPP + 'common/supervision/' + 'rate5_0.png',
  img_rate5_1: IMG_MAPP + 'common/supervision/' + 'rate5_1.png',
  img_rate6_0: IMG_MAPP + 'common/supervision/' + 'rate6_0.png',
  img_rate6_1: IMG_MAPP + 'common/supervision/' + 'rate6_1.png',
  img_zidingyi: IMG_MAPP + 'common/supervision/' + 'zidingyi.png',
  img_zidingyi1: IMG_MAPP + 'common/supervision/' + 'zidingyi1.png',
  AI_TITLE: IMG_BASE + '/ai_title.png',
  AI_BANNER: IMG_BASE + '/ai_banner.png',
  IMG_INDEX_AI_ACTIVE: IMG_BASE + '/ai_tab_icon.png',
  IMG_INDEX_AI_DEFAULT: IMG_BASE + '/ai_tab_icon_un.png'
}

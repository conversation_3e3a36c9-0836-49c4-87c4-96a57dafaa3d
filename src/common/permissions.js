
ap = partial(p, perm_type=PermissionType.MerchantApp, is_log=False, is_menu=False)
export default {
  MERCHANT_APP_PERMISSION_LIST = [
    ap('35', _('首页'), key='merchant_mobile_homepage'),
        ap('35.1', _('申请审批'), key='apply_approval'),
            ap('35.1.1', _('退款申请'), key='refund_apply'),
            ap('35.1.2', _('退款申诉'), key='refund_appeal'),
            ap('35.1.3', _('访客申请'), key='visitor_apply'),
            ap('35.1.4', _('缴费退款'), key='jf_refund'),
            ap('35.1.5', _('补卡申请'), key='card_replacement_apply'),
            ap('35.1.6', _('请假申请'), key='leave_apply'),
        ap('35.2', _('用户管理'), key='user_management'),
            ap('35.2.1', _('冻结'), key='freeze_card_user'),
            ap('35.2.2', _('挂失/取消挂失'), key='loss_card_user'),
            ap('35.2.3', _('编辑'), key='edit_card_user'),
            // #ifdef H5
            ap('35.2.4', _('上传人脸'), key='upload_face'),
            // #endif
            ap('35.2.5', _('退卡'), key='quit_card_user'),
        ap('35.3', _('公告管理'), key='messages_management'),
        ap('35.4', _('访客邀约'), key='visitor_invitation_management'),
        ap('35.5', _('设备管理'), key='merchant_mobile_device_management'),
            ap('35.5.1', _('设置'), key='edit_device'),
            ap('35.5.2', _('激活码'), key='activation_code'),
        ap('35.6', _('菜单管理'), key='menu_manage'),
            ap('35.6.1', _('编辑'), key='edit_menu'),
        ap('35.7', _('存餐管理'), key='put_meal_management'),
        ap('35.8', _('评价管理'), key='Evaluation_management'),
            ap('35.8.1', _('回复'), key='evaluation_reply'),
        ap('35.9', _('订单补扣'), key='order_deduction'),
        ap('35.10', _('明厨亮灶'), key='mingchuliangzao'),
        ap('35.11', _('消息通知'), key='message_notification'),
    ap('36', _('统计'), key='statistics_management'),
        ap('35.11', _('经营管理'), key='business_management'),
        ap('35.11', _('营业报表'), key='business_report'),
        ap('35.11', _('部门消费'), key='departmental_consumption'),
        ap('35.11', _('设备消费'), key='device_consumption'),
        ap('35.11', _('客流概况'), key='passenger_flow'),
        ap('35.11', _('收款码日报'), key='payment_code_daily_report'),
        ap('35.11', _('考勤概况'), key='attendance_dverview'),
]

}

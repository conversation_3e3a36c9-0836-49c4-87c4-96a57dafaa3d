<!-- eslint-disable -->

<template>
  <view class="date bg-white">
    <!-- <view class="head" v-if="!hiddenHeader">
      <view class="prev" @click="switch_month_week('prev', true)">
        <u-icon color="#484848" name="arrow-left"></u-icon>
      </view>
      <view class="title" @click="showPicker = true">
        <text class="lg black m-r-4">{{ `${nowYear}.${('0' + nowMonth).slice(-2)}` }}</text>
        <trigonometry />
      </view>
      <view class="next" @click="switch_month_week('next', true)">
        <u-icon color="#484848" name="arrow-right"></u-icon>


  :style="{ height: (retract ? 2 * 50 : week_list.length * 100) + 40 + 'rpx' }"

      </view>
    </view> -->
    <view class="date-lists">
      <view class="date-item week" v-for="(item, index) in week" :key="index">{{ item }}</view>
    </view>
    <swiper :style="{ height: '90rpx' }" :current="current" circular @change="change_date">
      <swiper-item>
        <!-- 外层套个view 是为了兼容支付宝swiper高度的问题 -->
        <!-- swiper的高度貌似是相对于由第一个swiper-item的子元素撑开的 -->
        <view :style="{ height: (retract ? 2 * 50 : week_list.length * 100) + 40 + 'rpx' }">
          <view v-for="(item, index) in week_list_prev_co" :key="index">
            <view class="date-lists" v-if="!retract || index == to_prev_week_index">
              <view
                class="date-item"
                @click="item_click(vo, index, key)"
                v-for="(vo, key) in item"
                :key="key"
              >
                <view
                  :style="{ height: 53 + 'rpx' }"
                  class="num"
                  :class="[vo.today ? 'today' : '', disabledFlag(vo)]"
                >
                  <text v-if="retract || vo.type == 'month'">{{ vo.day }}</text>
                </view>
                <view v-if="vo.type == 'month' || retract">
                  <text v-for="(docItem, docKey, docIndex) in docData" :key="docIndex">
                    <text v-if="funDate(docKey, vo)" class="dot flex">
                      <text
                        v-for="(
                          reservationDocItem, reservationDocKey, reservationDocIndex
                        ) in docItem"
                        :key="reservationDocIndex"
                        :class="[
                          reservationDocItem ? 'dot-wrapp-mark-active' : 'dot-wrapp-close-active',
                          'dot-wrapp'
                        ]"
                      ></text>
                    </text>
                  </text>
                </view>
                <!-- <view v-show="vo.dot && (vo.type == 'month' || retract)" class="dot">{{ dotText }}</view> -->
              </view>
            </view>
          </view>
        </view>
        <!-- <view @click="open" class="retract">
          <u-icon color="#484848" :name="retract ? 'arrow-down' : 'arrow-up'"></u-icon>
        </view> -->
      </swiper-item>
      <swiper-item>
        <view v-for="(item, index) in week_list" :key="index">
          <view class="date-lists" v-if="!retract || index == to_week_index">
            <view
              class="date-item"
              @click="item_click(vo, index, key)"
              v-for="(vo, key) in item"
              :key="key"
            >
              <!--   vo.type == 'month'  ? 'month' : retract ? '' : 'disabled'  -->
              <view class="num" :class="[vo.today ? 'today' : '', disabledFlag(vo)]">
                <text v-if="retract || vo.type == 'month'">{{ vo.day }}</text>
              </view>
              <view v-if="vo.type == 'month' || retract">
                <text v-for="(docItem, docKey, docIndex) in docData" :key="docIndex">
                  <text v-if="funDate(docKey, vo)" class="dot flex">
                    <text
                      v-for="(
                        reservationDocItem, reservationDocKey, reservationDocIndex
                      ) in docItem"
                      :key="reservationDocIndex"
                      :class="[
                        reservationDocItem ? 'dot-wrapp-mark-active' : 'dot-wrapp-close-active',
                        'dot-wrapp'
                      ]"
                    ></text>
                  </text>
                </text>
              </view>
              <!-- <view v-show="vo.dot && (vo.type == 'month' || retract)" class="dot">{{ dotText }}</view> -->
            </view>
          </view>
        </view>

        <!-- <view @click="open" class="retract">
          <u-icon color="#484848" :name="retract ? 'arrow-down' : 'arrow-up'"></u-icon>
        </view> -->
      </swiper-item>
      <swiper-item>
        <view v-for="(item, index) in week_list_next_co" :key="index">
          <view class="date-lists" v-if="!retract || index == to_next_week_index">
            <view
              class="date-item"
              @click="item_click(vo, index, key)"
              v-for="(vo, key) in item"
              :key="key"
            >
              <view class="num" :class="[vo.today ? 'today' : '', disabledFlag(vo)]">
                <text v-if="retract || vo.type == 'month'">{{ vo.day }}</text>
              </view>
              <view v-if="vo.type == 'month' || retract">
                <text v-for="(docItem, docKey, docIndex) in docData" :key="docIndex">
                  <text v-if="funDate(docKey, vo)" class="dot flex">
                    <text
                      v-for="(
                        reservationDocItem, reservationDocKey, reservationDocIndex
                      ) in docItem"
                      :key="reservationDocIndex"
                      :class="[
                        reservationDocItem ? 'dot-wrapp-mark-active' : 'dot-wrapp-close-active',
                        'dot-wrapp'
                      ]"
                    ></text>
                  </text>
                </text>
              </view>
              <!-- <view v-show="vo.dot && (vo.type == 'month' || retract)" class="dot">{{ dotText }}</view> -->
            </view>
          </view>
        </view>
        <!-- <view @click="open" class="retract">
          <u-icon color="#484848" :name="retract ? 'arrow-down' : 'arrow-up'"></u-icon>
        </view> -->
      </swiper-item>
    </swiper>
    <!-- <view class="text-center muted mini p-b-20" v-if="appointType == 'reservation'">
      <view class="">*日期下方的点代表餐段，绿色为已点，灰色为未点</view>
      <view class="">若全天都没有预约，则不显示</view>
  早餐、午餐、下午茶、晚餐、凌晨餐 
      <view class="">目前餐段有：{{ mealName }}</view>
    </view> -->
    <u-datetime-picker
      :show="showPicker"
      v-model="selectYearMonth"
      mode="year-month"
      @confirm="confirmSelect"
      @cancel="closePicker"
    ></u-datetime-picker>
  </view>
</template>

<script>
import { timeFormat } from '@/utils/date.js'
export default {
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    // 我的预约进来 控制餐段显示
    appointType: {
      type: [String, Number],
      default: ''
    },
    reservationDocData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    dotText: {
      type: String,
      default: '.'
    },
    hiddenHeader: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      addone:'',
      addtwe:'',
      debug: false,
      showPicker: false,
      selectYearMonth: Number(new Date()),
      week: ['日', '一', '二', '三', '四', '五', '六'],
      week_list: [],
      week_list_prev: [],
      week_list_prev_week: [],
      week_list_next: [],
      week_list_next_week: [],
      now_date: '',
      start_date: '',
      end_date: '',
      prev_date: '',
      next_date: '',
      nowYear: '',
      nowMonth: '',
      nowDay: '',// 今天的时间
      retract: true,
      to_week_index: 0,
      to_prev_week_index: 0,
      to_next_week_index: 0,
      nowTime: 0,
      dot_list: [],
      current: 1,
      date: '',
      mealName: '',
      docData: {}
    }
  },
  mounted() {
    this.imming()
  },
  watch: {
    value(value) {
      console.log(value)
      this.get_date(this.date_parse(value))
    },
    reservationDocData: {
      immediate: true,
      handler(value) {
        this.docData = value
        this.funMealName(this.date)
        // this.set_doc_lists_update()
      }
    },
    showPicker(value) {
      // $eventbus 跨组件间通信
      console.log(value)
      this.$eventbus.$emit('changeBacker', value)
    },
    // addone:{
    //   handler(newValue, oldValue) {
    //         // newValue 和 oldValue 是一样的
    //         console.log(newValue); 
    //         console.log(oldValue);
           
    //       },
    //       // 深度监听 监听对象，数组的变化
    //       deep: true
        
    // }
  },
  computed: {
    week_list_prev_co() {
      return this.retract ? this.week_list_prev_week : this.week_list_prev
    },
    week_list_next_co() {
      return this.retract ? this.week_list_next_week : this.week_list_next
    }
  },
  created() {
    this.init()
    // console.log(this.week_list_prev_co, ' week_list_prev_co')
    // console.log(this.week_list, ' week_list')
    // console.log(this.week_list_next_co, 'week_list_next_co')
  },
  methods: {
    imming() {
      this.week_list.forEach((item, index) => {
        let addone = item.filter(items => {
          return items.day == this.nowDay
        })
        let addtwe = item.filter(items => {
          return items.day == this.nowDay + 1
        })
        if (addone.length != 0) {
          this.addone = addone[0].day
         addone[0].day = this.$t('That.now')
       
        }
        if (addtwe.length != 0) {
          addtwe[0].day = this.$t('That.Tomorrow')
        }
      })
    },
    confirmSelect({ value }) {
      this.get_date(value)
      this.closePicker()
      this.week_list_prev_week = this.week_list
      this.week_list_prev = this.week_list
      this.week_list_next_week = this.week_list
      this.week_list_next = this.week_list
    },
    closePicker() {
      this.showPicker = false
    },
    change() {
      let nowWeek = this.week_list[this.to_week_index]
      let range = []
      let begin
      let end
      // retract 周
      if (this.retract) {
        // begin = timeFormat(new Date(nowWeek[0].date).getTime(), 'yyyy-mm-dd') + ' 00:00:00'
        begin = nowWeek[0].date + ' 00:00:00'
        // end = timeFormat(new Date(nowWeek[nowWeek.length - 1].date).getTime(), 'yyyy-mm-dd') + ' 23:59:59'
        end = nowWeek[nowWeek.length - 1].date + ' 23:59:59'
      } else {
        begin = this.nowYear + '-' + this.nowMonth.toString().padStart(2, 0) + '-01 00:00:00'
        end = timeFormat(new Date(this.nowYear, this.nowMonth, 0)) + ' 23:59:59'
      }
      range = [begin, end]
      let value = {
        time: this.nowTime,
        fulldate: this.date.replace(/-(\d)(?!\d)/g, '-0$1'),
        range
      }
      this.$emit('changeBack', value)
     
    },
    funMealName() {
      let date = this.date.replace(/-(\d)(?!\d)/g, '-0$1')
      let name = []
      for (let doc in this.docData) {
        if (new Date(doc).getTime() == new Date(date).getTime()) {
          for (let meal in this.docData[doc]) {
            switch (meal) {
              case 'breakfast':
                name.push('早餐')
                break
              case 'lunch':
                name.push('午餐')
                break
              case 'afternoon':
                name.push('下午茶')
                break
              case 'dinner':
                name.push('晚餐')
                break
              case 'supper':
                name.push('宵夜')
                break
              case 'morning':
                name.push('凌晨餐')
                break
            }
          }
        }
      }
      this.mealName = name.join('、')
    },
    init() {
      if (this.value) {
        this.get_date(this.date_parse(this.value))
      } else {
        this.get_date()
      }
      // this.doc_list_update()
      this.update_month()
      this.change()
    },
    funDate(data, data1) {
      // console.log(88888, data,data1)
      let status = false
      if (
        new Date(data.replace(/-(\d)(?!\d)/g, '-0$1')).getTime() ==
        new Date(data1.date.replace(/-(\d)(?!\d)/g, '-0$1')).getTime()
      ) {
        status = true
      } else {
        status = false
      }
      return status
    },
    open() {
      this.retract = !this.retract
      this.get_date(this.nowTime)
      this.set_to_day('week_list_prev')
      this.set_to_day('week_list_next')

      this.change_week()

      if (this.retract) {
        this.update_swiper_item('week')
      } else {
        this.update_swiper_item('month')
      }
      // this.set_doc_lists_update()
    },
    change_week() {
      if (this.to_week_index < this.week_list.length - 1) {
        this.to_next_week_index = this.to_week_index + 1
        this.week_list_next_week = this.week_list
      } else {
        this.to_next_week_index = 0
        this.week_list_next_week = this.week_list_next
      }

      if (this.to_week_index == 0) {
        this.update_month()

        // if(){
        let next_day = this.week_list_prev[this.week_list_prev.length - 1][6].day

        // }
        this.to_prev_week_index = this.week_list_prev.length - 1 - Math.ceil(next_day / 7)

        this.week_list_prev_week = JSON.parse(JSON.stringify(this.week_list_prev))
      } else {
        this.to_prev_week_index = this.to_week_index - 1
        this.week_list_prev_week = this.week_list
      }

      // if(this.current == 1){

      // }
      // let to_week_index = this.to_week_index;
      // if(this.current == 2){
      // 	this.to_next_week_index = this.to_week_index;
      // 	this.to_week_index = this.to_week_index - 1;
      // 	this.to_prev_week_index =  this.to_next_week_index + 1;
      // }else if(this.current == 0){
      // 	this.to_next_week_index = this.to_week_index;
      // 	this.to_week_index = this.to_week_index - 1;
      // 	this.to_prev_week_index =  this.to_next_week_index + 1;
      // }

      this.change()
    },
    change_date_week(type) {
      let week_list = this.week_list
      let to_week_index = this.to_week_index
      if (type == 'prev') {
        this.to_week_index = this.to_prev_week_index
        this.to_prev_week_index = this.to_next_week_index
        this.to_next_week_index = to_week_index
        this.week_list = this.week_list_prev_week
        this.week_list_prev_week = this.week_list_next_week
        this.week_list_next_week = week_list
      } else if (type == 'next') {
        this.to_week_index = this.to_next_week_index
        this.to_next_week_index = this.to_prev_week_index
        this.to_prev_week_index = to_week_index

        this.week_list = this.week_list_next_week
        this.week_list_next_week = this.week_list_prev_week
        this.week_list_prev_week = week_list
      }
      this.set_to_day_all()
    },
    change_date_month(type) {
      let week_list = this.week_list
      if (type == 'prev') {
        this.week_list = this.week_list_prev
        this.week_list_prev = this.week_list_next
        this.week_list_next = week_list
      } else if (type == 'next') {
        this.week_list = this.week_list_next
        this.week_list_next = this.week_list_prev
        this.week_list_prev = week_list
      }
    },
    change_date(e) {
      console.log(e, '滑动日期')
      // console.log(this.addone);
      let primary_current = this.current
      let current = e.detail.current
      this.current = current      
      if (primary_current - current == -1 || primary_current - current == 2) {
        if (this.retract) {
          this.switch_month_week('next')
          this.change_week()
          if (primary_current - current == -1 && current != 1) {
            this.change_date_week('prev')
          } else if (primary_current - current == 2) {
            this.change_date_week('next')
          }
        } else {
          this.get_date(this.get_month('next'))
          this.update_month()
          if (primary_current - current == -1 && current != 1) {
            this.change_date_month('prev')
          } else if (primary_current - current == 2) {
            this.change_date_month('next')
          }

          this.change()
        }
      } else {
        if (this.retract) {
          this.switch_month_week('prev')
          this.change_week()
          if (primary_current - current == 1 && current != 1) {
            this.change_date_week('next')
          } else if (primary_current - current == -2) {
            this.change_date_week('prev')
          }
        } else {
          this.get_date(this.get_month('prev'))
          this.update_month()
          if (primary_current - current == 1 && current != 1) {
            this.change_date_month('next')
          } else if (primary_current - current == -2) {
            this.change_date_month('prev')
          }

          this.change()
        }
      }
      this.set_to_day_all()
      this.get_date(this.nowTime)
      // this.set_doc_lists_update()
    },
    update_month() {
      this.get_date(this.get_month('prev'), 'prev')
      this.get_date(this.get_month('next'), 'next')
    },
    // set_doc_lists_update() {
    //   this.doc_list_update('week_list')
    //   this.doc_list_update('week_list_prev')
    //   this.doc_list_update('week_list_next')
    //   this.doc_list_update('week_list_prev_week')
    //   this.doc_list_update('week_list_next_week')
    // },
    // doc_list_update(week_list = 'week_list') {
    //   let list = []

    //   this[week_list].map((item, index) => {
    //     list.push(
    //       item.map((vo, key) => {
    //         if (this.dot_list.indexOf(vo.date) > -1 || this.dot_list.indexOf(vo.date.replace(/-(\d)(?!\d)/g, '-0$1')) > -1) {
    //           vo.dot = true
    //         } else {
    //           vo.dot = false
    //         }
    //         return {
    //           ...vo
    //         }
    //       })
    //     )
    //   })
    //   this[week_list] = list
    // },
    set_to_day(type) {
      let list = []

      this[type].map((item, index) => {
        list.push(
          item.map((vo, key) => {
            if (vo.date == `${this.date}`) {
              vo.today = true
            } else {
              vo.today = false
            }
            return {
              ...vo
            }
          })
        )
      })
      this[type] = list
    },
    item_click(item, item_index = -1) {
      // 往后的日期不能点击
      // if(this.$route.path.indexOf('user_appoint') == -1 && new Date(item.date).getTime() < new Date(this.$u.timeFormat(new Date().getTime(), 'yyyy-m-d')).getTime()){
      // 	return uni.$u.toast('不能少于当前日期')
      // }
      // 是否开启了disabled参数
      let currentDate = uni.$u.timeFormat(new Date().getTime(), 'yyyy-mm-dd')
      let clickedDate = uni.$u.timeFormat(new Date(item.date).getTime(), 'yyyy-mm-dd')
      if (this.disabled && new Date(currentDate).getTime() > new Date(clickedDate).getTime()) {
        return uni.$u.toast('不能少于当前日期')
      }
      if (!this.retract && item.type !== 'month') {
        return false
      }
      this.date = item.date
      if (item.type == 'month') {
        this.nowDay = item.day
        // 因为拿的数据不同 循环一遍拿到index 给week_list
        this.week_list.map((v, weekIndex) => {
          v.map(k => {
            if (new Date(item.date).getTime() == new Date(k.date).getTime()) {
              this.to_week_index = weekIndex
            }
          })
        })
      } else if (this.retract) {
        this.nowDay = item.day
      }

      let now_arr = item.date.split('-')
      this.nowYear = now_arr[0]
      this.nowMonth = now_arr[1]
      this.nowDay = now_arr[2]
      this.set_to_day_all(item_index)

      this.nowTime = this.date_parse(`${item.date}`)

      this.change()

      // this.set_doc_lists_update()
    },
    set_to_day_all(item_index) {
      this.set_to_day('week_list')
      this.set_to_day('week_list_prev')
      this.set_to_day('week_list_next')
      this.set_to_day('week_list_prev_week')
      this.set_to_day('week_list_next_week')
    },
    get_month(type) {
      let nowMonth = this.nowMonth
      let nowYear = this.nowYear
      let nowDay = this.nowDay

      if (type == 'prev') {
        if (nowMonth == 1) {
          nowMonth = 12
          nowYear = nowYear - 1
        } else {
          nowMonth--
        }
      } else if (type == 'next') {
        if (nowMonth == 12) {
          nowMonth = 1
          nowYear = nowYear + 1
        } else {
          nowMonth++
        }
      }

      let days = this.get_month_days(nowMonth, nowYear)
      if (nowDay > days) {
        nowDay = days
      }

      return this.date_parse(`${nowYear}-${nowMonth}-${nowDay}`)
    },

    date_parse(str) {
      return Date.parse(str.replace(/-(\d)(?!\d)/g, '-0$1'))
    },
    switch_month_week(type = 'next', update_week = false) {
      if (this.retract) {
        if (type == 'prev') {
          this.get_date(this.nowTime - 86400 * 7 * 1000)
        } else if (type == 'next') {
          this.get_date(this.nowTime + 86401 * 7 * 1000)
        }
        if (update_week) {
          this.update_swiper_item('week')
          // this.set_doc_lists_update()
        }
      } else {
        this.get_date(this.get_month(type))
        this.update_swiper_item('month')
      }
      // this.set_doc_lists_update()

      this.set_to_day_all()

      if (update_week) {
        this.change()
      }
    },
    update_swiper_item(type = 'month') {
      if (type == 'month') {
        if (this.current == 0) {
          this.change_date_month('next')
        } else if (this.current == 2) {
          this.change_date_month('prev')
        }
      } else if (type == 'week') {
        if (this.current == 0) {
          this.change_date_week('next')
        } else if (this.current == 2) {
          this.change_date_week('prev')
        }
      }
    },
    next() {
      this.get_date(this.next_date)
    },
    get_date(value = '', type = 'same') {
      let date = new Date()
      if (value) {
        date = new Date(value)
      }
      let nowMonth = date.getMonth() + 1
      let nowYear = date.getFullYear()
      let nowDay = date.getDate()
      let nowTime = date.getTime()
      let nowWeek = date.getDay()

      let days = this.get_month_days(nowMonth, nowYear)
      let start_date = new Date(nowYear, nowMonth - 1, 1)
      let end_date = new Date(nowYear, nowMonth - 1, days)
      let prev_date = new Date(start_date.getTime() - 1)
      let prev_date_days = prev_date.getDate()
      let next_date = new Date(end_date.getTime() + 86401 * 1000)
      let next_date_days = next_date.getDate()

      let start_week = start_date.getDay()
      let date_arrs = []

      let week_list = []
      let count_days = 35
      for (let i = prev_date_days - start_week + 1; i <= prev_date_days; i++) {
        date_arrs.push({
          day: i,
          type: 'prev',
          date: `${prev_date.getFullYear()}-${prev_date.getMonth() + 1}-${i}`
        })
      }
      for (let i = 1; i <= days; i++) {
        date_arrs.push({
          day: i,
          type: 'month',
          today: i == nowDay,
          date: `${nowYear}-${nowMonth}-${i}`
        })

        if (i == nowDay && type == 'same') {
          this.date = `${nowYear}-${nowMonth}-${i}`
        }
      }
      if (this.debug) {
        console.log(
          value,
          date,
          this.date,
          `${next_date.getFullYear()}-${next_date.getMonth() + 1}-${next_date.getDate()}`
        )
      }
      let date_arrs_length = date_arrs.length
      if (date_arrs_length > 35) {
        count_days = 42
      }
      for (let i = 1; i <= count_days - date_arrs_length; i++) {
        date_arrs.push({
          day: i,
          type: 'next',
          date: `${next_date.getFullYear()}-${next_date.getMonth() + 1}-${i}`
        })
      }
      for (let i = 0; i < date_arrs.length / 7; i++) {
        let arr = []
        for (let j = 0; j < 7; j++) {
          if (date_arrs[i * 7 + j].today) {
            if (type == 'same') {
              this.to_week_index = i
            }
          }
          arr.push(date_arrs[i * 7 + j])
        }
        week_list.push(arr)
      }
      if (type == 'same') {
        this.week_list = week_list
        this.nowYear = nowYear
        this.nowMonth = nowMonth
        this.nowDay = nowDay
        this.nowTime = nowTime
        this.start_date = start_date
        this.end_date = end_date
        this.prev_date = prev_date
        this.next_date = next_date
      } else if (type == 'prev') {
        this.week_list_prev = week_list
      } else if (type == 'next') {
        this.week_list_next = week_list
      }
    },
    get_month_days(nowMonth, nowYear) {
      let month_arr = [1, 3, 5, 7, 8, 10, 12]
      let days = 0
      if (nowMonth == 2) {
        if (nowYear % 4 == 0) {
          days = 29
        } else {
          days = 28
        }
      } else if (month_arr.indexOf(nowMonth) >= 0) {
        days = 31
      } else {
        days = 30
      }
      return days
    },
    disabledFlag(data) {
      let disabledclass = ''
      let currentDate = uni.$u.timeFormat(new Date().getTime(), 'yyyy-mm-dd')
      let clickedDate = uni.$u.timeFormat(new Date(data.date).getTime(), 'yyyy-mm-dd')
      if (this.disabled && new Date(currentDate).getTime() > new Date(clickedDate).getTime()) {
        disabledclass = 'disabled'
      } else if (data.type == 'month') {
        disabledclass = 'month'
      }
      return disabledclass
    }
  }
}
</script>

<style lang="scss">
.date {
  .head {
    display: flex;
    align-items: center;
    height: 100rpx;
    justify-content: space-between;
    border-bottom: 1rpx solid $border-color-base;
    color: $color-text-primary;
    padding: 0 40rpx;

    .title {
      width: 200rpx;
      font-size: 30rpx;
      padding-left: 4rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 50rpx;
      border-radius: 60rpx;
      background-color: $background-color;
    }
  }
}

.retract {
  position: absolute;
  width: 100%;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  // height: 50rpx;
}

.date-lists {
  display: flex;
  width: 100%;

  .date-item {
    flex: 1;
    text-align: center;
    height: 60rpx;
    font-size: $font-size-xl;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    &.week {
      height: 60rpx;
      color: $color-text-secondary;
      font-size: $font-size-xs;
      justify-content: center;
    }

    .num {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      line-height: 60rpx;

      &.disabled {
        color: $color-disabled;
      }

      &.month {
        color: $color-text-primary;
      }

      &.today {
        background: $but;
        color: #fff;
      }
    }

    .dot {
      font-size: $font-size-mini;
      // color: $color-primary;
      margin-top: 5rpx;
      .dot-wrapp {
        margin-right: 3rpx;
        display: inline-block;
        width: 10rpx;
        height: 10rpx;
        border-radius: 10rpx;
      }
      .dot-wrapp-close-active {
        background-color: #d7d7d7;
      }
      .dot-wrapp-mark-active {
        background-color: $color-primary;
      }
    }
  }
}
</style>

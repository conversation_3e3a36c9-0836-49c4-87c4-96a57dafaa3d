<!-- 公用弹窗 请优先使用此公用弹窗

1. 基础使用（单个按钮）：<customDialogComponent ref="customDialog" customDialogTitle="填写标题" customContent="填写内容 =" confirmBtnTxt="填写按钮名称" ></customDialogComponent>
   基础使用（双按钮）  ：<customDialogComponent ref="customDialog" customDialogTitle="填写标题" customContent="填写内容 =" confirmBtnTxt="填写按钮名称" isShowCancelBtn></customDialogComponent>
   基础使用（点击按钮返回监听）：<customDialogComponent ref="customDialog" customDialogTitle="营业额" customContent="营业额 " confirmBtnTxt="知道了"  isShowCancelBtn @handlerCustomConfirm="handlerCustomConfirm" @handlerCustomCancel="handlerCustomCancel"></customDialogComponent>
2.复杂使用
   中间显示输入框; <customDialogComponent ref="customDialog" dialogType="input" customDialogTitle="营业额"  confirmBtnTxt="知道了"  isShowCancelBtn @handlerCustomConfirm="handlerCustomConfirm" @handlerCustomCancel="handlerCustomCancel">
    </customDialogComponent>
   中间显示层自定义：
    <customDialogComponent ref="customDialog" dialogType="custom" customDialogTitle="营业额"  confirmBtnTxt="知道了"  isShowCancelBtn @handlerCustomConfirm="handlerCustomConfirm" @handlerCustomCancel="handlerCustomCancel">
      <template slot="custom">
        <view>hello</view>
      </template>
    </customDialogComponent>

3. 组件公共方法：
   显示弹窗：showCustomDialog(),例如： this.$refs.customDialog.showCustomDialog();
   隐藏弹窗：hideCustomDialog(),例如： this.$refs.customDialog.hideCustomDialog();

4. 组件回调方法：
   handlerCustomConfirm()//用户点击了确认按钮
   handlerCustomCancel()// 用户点击了取消按钮
   handlerCustomInputChange(e)  //用户输入改变监听
   handlerOutConnerClick()  //用户点击了弹窗以外的部分

-->
<template>
    <u-overlay :show="isShowDialog" @click="handlerOutConnerClick" catchtouchmove="true" >
      <view class="custom_dialog_container" >
        <view class="custom_dialog_item"  @tap.stop>
          <!-- 标题-->
          <view class="custom_dialog_title"> {{titleTxt }}</view>
          <!-- 中间文字内容-->
          <slot name="content" v-if="dialogTypeValue=='content'" >
          <view class="custom_dialog_content" >
            <span>{{ contentTxt }}</span>
          </view>
        </slot>
          <!-- 中间输入框内容-->
          <slot name="input" v-else-if="dialogTypeValue=='input'">
          <!--input上面的内容，可以作为提示-->
          <view class="custom_dialog_content_input_title" v-if="isShowInputTitle">
            {{ inputTitle }}
          </view>
          <!--单一输入框 默认都是这个-->
          <view class="custom_dialog_content_input">
            <view class="custom_dialog_content_input_tip" v-if="isShowInputLeft">{{ inputLeftTip+"：" }}</view><u-input v-if="!isTxtArea" fontSize="42rpx" color="#201e1d" v-model="customInputTxt" border="surround" :placeholder="customInputTxtHolder" :type="inputTypeName" clearable  focus @change="handlerInputChange" :maxlength="maxLength"/>
            <u-textarea class="custom_dialog_content_area" v-if='isTxtArea' v-model="customInputTxt" :placeholder="customInputTxtHolder" count autoHeight ref="textarea" :maxlength="maxLength" @change="handlerInputChange" confirmType="done"></u-textarea>
          </view>
          <view class="tip-warn" v-if="inputTipWarn.length>0">{{ inputTipWarn }}</view>
        </slot>
        <!-- 中间层自定义-->
        <view class="custom_dialog_content_custom" v-else>
          <slot name = "custom"  >
          </slot>
        </view>
          <!-- 分割线-->
          <view class="horizontal_cell_line" ></view>
          <!-- 按钮层-->
          <view  class="custom_dialog_btns">
            <u-button v-if="isShowCancelBtn"  :customStyle="customStyleBtn"  plain :class="['custom_dialog_btn_style',isShowCancelBtn?'borderRadiusLeft':'']" :color="color.colorBtnGray" @click="handlerCustomCancel()">{{ cancelTxt }}</u-button>
            <view v-if="isShowCancelBtn"  class="vertical_cell_line"></view>
            <u-button plain   :customStyle="customStyleBtn" :class="['custom_dialog_btn_style',isShowCancelBtn?'borderRadiusRight':'']" :color="color.colorBtnOrange" @click="handlerCustomConfirm()">{{ confirmTxt }}</u-button>
          </view>
        </view>
      </view>
  </u-overlay>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  props: {
    dialogType: { // 弹窗的默认类型， content 默认显示文字层，input 显示输入框层， custom  中间层自定义
      type: String,
      default: "content"
    },
    customContent: { // 内容
      type: String,
      default: ""
    },
    customDialogTitle: { // 标题
      type: String,
      default: "标题"
    },
    confirmBtnTxt: { // 按钮确认名字
      type: String,
      default: '确认'
    },
    cancelBtnTxt: { // 按钮取消名字
      type: String,
      default: "取消"
    },
    isShowCancelBtn: { // 是否显示取消按钮
      type: Boolean,
      default: true
    },
    customInputHolder: { // 输入框提示语
      type: String,
      default: ""
    },
    inputType: { // 输入框输入类型 number | idcard | digit | password
      type: String,
      default: "text"
    },
    isShowInputLeftValue: { // 是否显示input左侧提示
      type: Boolean,
      default: false
    },
    isShowInputTitleValue: { // 是否显示input头部
      type: Boolean,
      default: false
    },
    inputTitleTxt: { // input头部txt
      type: String,
      default: ""
    },
    inputLeftTipTxt: { // input左侧提示txt
      type: String,
      default: ""
    },
    isInputTxtArea: { // 是否输入框是textarea
      type: Boolean,
      default: false
    },
    inputMaxLength: {
      type: Number,
      default: 100
    },
    inputTipWarn: { // 输入下方提示
      type: String,
      default: ""
    },
    isCloseOverlay: { // 是否可以点击外部进行关闭
      type: Boolean,
      default: true
    }
  },
  name: "customDialogComponent", // 公用弹窗
  data() {
    return {
      dialogTypeValue: this.dialogType, // 弹窗类型
      titleTxt: this.customDialogTitle, // 弹窗标题
      contentTxt: this.customContent, // 弹窗内容
      confirmTxt: this.confirmBtnTxt, // 按钮确认名字
      cancelTxt: this.cancelBtnTxt, // 按钮取消名字
      isShowDialog: false, // 是否显示弹窗
      customInputTxt: "", // 输入内容，
      customInputTxtHolder: this.customInputHolder, // 输入提示
      customStyleBtn: {
        flex: 1,
        width: '100%',
        height: '90rpx',
        fontSize: '36rpx',
        border: 'none !important',
        padding: '0 !important',
        borderRadius: ' 0 0 20rpx 20rpx'
      },
      cancelCallBack: null, // 取消回调
      confirmCallBack: null, // 确认回调
      inputTitle: this.inputTitleTxt, // 输入框头部
      inputLeftTip: this.inputLeftTipTxt, // 输入框左侧提示
      isShowInputTitle: this.isShowInputTitleValue,
      isShowInputLeft: this.isShowInputLeftValue,
      isTxtArea: this.isInputTxtArea, // 是否输入框是文本域
      isWhole: false, // 是否使用了全局组件
      inputTypeName: this.inputType,
      maxLength: this.inputMaxLength
    };
  },
  created() {
    console.log('customDialogComponent', this);
  },
  computed: {
    ...mapGetters(['color'])
  },
  watch: {
    isShowDialog(newVal) {
      console.log("isShowDialog", newVal, this, Reflect.has(this, 'close'));
      if (!newVal && this.isWhole) { // 为了防止自定义引用的时候冲突，所以，该条件处理是否全局插入弹窗，如果是用this.$comfirm 引用，就要调用close 销毁组件，否则就不用调用它
        this.close()
      }
    }
  },
  methods: {
    /**
     * 显示弹窗
     */
    showCustomDialog() {
      console.log("showQrDialog");
      if (!this.confirmTxt) {
        this.confirmTxt = this.$t('page.btn.confirm')
      }
      if (!this.cancelTxt) {
        this.cancelTxt = this.$t('page.btn.cancel')
      }
      this.isShowDialog = true
    },
    /**
     * 隐藏弹窗
     */
    hideCustomDialog() {
      console.log("hideQrDialog");
      this.isShowDialog = false
      this.customInputTxt = ""
    },
    /**
     * 确定按钮
     */
    handlerCustomConfirm() {
      var content = this.customInputTxt
      this.$emit("handlerCustomConfirm", { inputContent: content, confirmTxt: this.confirmTxt })
      if (this.confirmCallBack) {
        this.confirmCallBack({ inputContent: content, confirmTxt: this.confirmTxt })
      }
      if (this.dialogTypeValue === "content") {
        this.isShowDialog = false
      }
    },
    /**
     * 取消按钮
     */
    handlerCustomCancel() {
      this.isShowDialog = false
      this.customInputTxt = ""
      this.$emit("handlerCustomCancel", true)
      if (this.cancelCallBack) {
        this.cancelCallBack(true)
      }
    },
    /**
     * 输入监听
     */
    handlerInputChange(inputContent) {
      console.log("handlerInputChange", inputContent);
      this.$emit("handlerCustomInputChange", inputContent)
    },
    /**
     * 弹窗外部区域点击
     */
    handlerOutConnerClick() {
      console.log("handlerOutConnerClick");
      if (this.isCloseOverlay) {
        this.isShowDialog = false
      }
      this.$emit("handlerOutConnerClick", true)
    },
    /**
     * 设置标题
     * @param {} titleValue
     */
    setTitle(titleValue) {
      this.titleTxt = titleValue
    },
    /**
     * 修改弹窗data 数据
     * @param {*} info
     */
    modifyData(info) {
      if (info && Object.keys(info).length > 0) {
        for (let key in info) {
          if (Object.hasOwnProperty.call(this, key)) {
            this[key] = info[key]
          }
        }
      }
      console.log("this.data", this);
    },
    /**
     * 设置输入内容
     */
    setInputContent(content) {
      this.customInputTxt = content
    },
    /**
     * 设置内容
     * @param {*} content
     */
    setContent(content) {
      this.contentTxt = content
    }
  }
}
</script>

<style lang="scss" scoped>
.custom_dialog_container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .custom_dialog_item {
    width: 687rpx;
    min-height: 100rpx;
    background-color: #ffffff;
    border-radius: 20rpx;
    .custom_dialog_content{
      min-height: 80rpx;
      margin:  0rpx 45rpx ;
      color: #666666;
      font-size: 32rpx;
      text-align: center;
    }
    .custom_dialog_content_input_title{
      margin:  35rpx ;
    }
    .custom_dialog_content_input{
      width: 620rpx;
      min-height: 80rpx;
      margin:  0rpx 35rpx ;
      display: inline-flex;
      align-items: center;
      .custom_dialog_content_area{
        width: 620rpx;
        min-height: 80rpx;
        margin: auto;
      }

    }
    .custom_dialog_content_custom{
      // min-height: 80rpx;
      font-size: 32rpx;
      margin:50rpx ;
    }

    .custom_dialog_title{
      color: #201e1d;
      font-size: 36rpx;
      margin: 55rpx auto;
      text-align: center;
      font-weight: 600;
    }
    .horizontal_cell_line{
      width: 100%;
      height: 1px;
      background-color: #e5e5e5;
      margin-top:45rpx ;
    }
  }

.custom_dialog_btns{
  display: flex;
  align-items: center;
  flex-grow: 2;
  .custom_dialog_btn_style{
    flex:1;
    width: 100%;
    height: 90rpx;
    font-size: 36rpx;
    border: none !important;
    padding: 0 !important;
    border-radius: 0 0 20rpx 20rpx;
  }
  .borderRadiusLeft{
    border-radius: 0 0 0 20rpx;
  }
  .borderRadiusRight{
    border-radius: 0 0 20rpx 0;
  }
  .vertical_cell_line{
    width: 1px;
    height: 90rpx;
    background-color: #e5e5e5;
  }
}
.tip-warn {
  color: $color_primary;
  display: block;
  margin: 10rpx 40rpx;
}

</style>

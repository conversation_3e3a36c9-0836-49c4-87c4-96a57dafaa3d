import Vue from 'vue';
import custonDialogComponent from './CustomDialogComponent.vue';
// 设置初始化弹窗时的初始化值
const defaults = {
  dialogTypeValue: "content", // 弹窗类型
  titleTxt: "", // 弹窗标题
  contentTxt: "", // 弹窗内容
  confirmTxt: "确认", // 按钮确认名字
  cancelTxt: "取消", // 按钮取消名字
  isShowDialog: false, // 是否显示弹窗
  customInputTxt: "", // 输入内容，
  customInputTxtHolder: '',
  inputTitle: '', // 输入框头部
  inputLeftTip: '', // 输入框左侧提示
  isShowInputTitle: false,
  isShowInputLeft: false,
  isTxtArea: false, // 是否是文本域
  cancelCallBack: null, // 取消点击回调
  confirmCallBack: null, // 确认点击回调
  isWhole: true// 是否全局
};

let confirmVueLoading;
// 手动挂载控件
const ConfirmVueConstructor = Vue.extend(custonDialogComponent);
// 设置控件的关闭时移除这个弹窗
ConfirmVueConstructor.prototype.close = function() {
  var vm = this;
  var promise = new Promise(function(resolve, reject) {
    if (vm.$el && vm.$el.parentNode) {
      vm.$el.parentNode.removeChild(vm.$el);
    }
    resolve();
  })
  vm.$destroy();
  vm.isShowDialog = false;
  confirmVueLoading = null;
  return promise
};

// 弹窗初始化方法，一般用这个进行初始化
const customDialog = (options = {}, vm) => {
  // #ifdef H5
  if (Vue.prototype.$isServer) return;
  options = Object.assign({}, defaults, options);
  console.log("document", document);
  let parent = document.body;
  // 如果弹窗初始化过了。就别再初始化了
  if (confirmVueLoading) {
    confirmVueLoading = null
  }
  var instance = new ConfirmVueConstructor({
    el: document.createElement('view'),
    data: options
  });

  parent.appendChild(instance.$el);
  // 设置显示弹窗
  Vue.nextTick(() => {
    instance.isShowDialog = true;
  });
  confirmVueLoading = instance
  // #endif
  // #ifdef MP-WEIXIN || MP-ALIPAY
  if (!vm) {
    return
  }
  if (vm.$refs.customDialog) {
    vm.$refs.customDialog.modifyData(options)
    vm.$refs.customDialog.showCustomDialog()
  }
  return vm.$refs.customDialog
  // #endif

  return instance;
};

export default customDialog;

<template>
  <view class="project-home">
    <!-- <button @click="reduceDate">前一天</button>
    <p>{{ date }}</p>
    <button @click="addDate">后一天</button>
    <br />
    <button @click="reduceYear">前一月</button>
    <p>{{ month }}</p>
    <button @click="addYear">后一月</button>
    <br /> -->
    <!-- <button @click="getaddweek">前一周</button> -->
    <view class="flex col-center row-center font_size">
      <!-- <u-icon
        name="arrow-left"
        class="p-r-10"
        :color="limitPreviousDateColor"
        size="28"
        @click="clickPrevious"
      ></u-icon> -->
      <text v-if="type === 'day'">{{ date }}</text>
      <text v-if="type === 'month'">{{ month }}</text>
      <text v-if="type === 'week'">{{ weekstart }}～{{ weekend }}</text>
      <!-- <u-icon
        name="arrow-right"
        class="p-l-10"
        :color="limitLastDateColor"
        size="28"
        @click="clickLast"
      ></u-icon> -->
    </view>
    <!-- <button @click="getalastweek">后一周</button> -->
  </view>
</template>

<script>
/* eslint-disable */
import { timeFormat } from '@/utils/date'
export default {
  props: {
    // 限制不能点下一个
    limitLastDate: {
      type: Boolean,
      default: false
    },
    // 限制不能点上一个日期
    limitPreviousDate: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      date: '暂无当前日期',
      month: '暂无月',
      weekstart: '暂无开始',
      weekend: '暂无结束'
    }
  },
  computed: {
    limitLastDateColor() {
      let color = '#1d201e'
      let currentDate = uni.$u.timeFormat(new Date(), 'yyyy-mm-dd')
      let lastDate = ''
      switch (this.type) {
        case 'week':
          lastDate = this.weekend
          break
        case 'month':
          lastDate = this.month
          currentDate = uni.$u.timeFormat(new Date(), 'yyyy-mm')
          break
        case 'day':
          lastDate = this.date
          break
        default:
          break
      }
      if (this.limitLastDate && new Date(currentDate).getTime() <= new Date(lastDate).getTime()) {
        color = '#b2b6b9'
      }
      return color
    },
    limitPreviousDateColor() {
      let color = '#1d201e'
      let currentDate = uni.$u.timeFormat(new Date(), 'yyyy-mm-dd')
      let lastDate = ''
      switch (this.type) {
        case 'week':
          lastDate = this.weekstart
          break
        case 'month':
          lastDate = this.month
          currentDate = uni.$u.timeFormat(new Date(), 'yyyy-mm')
          break
        case 'day':
          // eslint-disable-next-line no-unused-vars
          lastDate = this.date
          break
        default:
          break
      }
      if (
        this.limitPreviousDate &&
        // eslint-disable-next-line no-self-compare
        new Date(currentDate).getTime() >= new Date(currentDate).getTime()
      ) {
        color = '#b2b6b9'
      }
      return color
    }
  },
  watch: {
    type(newValue, oldValue) {
      this.getDefaultDate()
    }
  },
  created() {
    this.getDefaultDate()
  },
  onShow() {},
  methods: {
    clickPrevious() {
      switch (this.type) {
        case 'week':
          this.getaddweek()
          break
        case 'month':
          this.reduceYear()
          break
        case 'day':
          this.reduceDate()
          break
        default:
          break
      }
    },
    clickLast() {
      switch (this.type) {
        case 'week':
          this.getalastweek()
          break
        case 'month':
          this.addYear()
          break
        case 'day':
          this.addDate()
          break
        default:
          break
      }
    },
    // 获取默认的数据
    getDefaultDate() {
      var mydate = new Date()
      this.date = uni.$u.timeFormat(new Date(), 'yyyy-mm-dd')
      this.month = uni.$u.timeFormat(new Date(), 'yyyy-mm')
      switch (this.type) {
        case 'day':
          this.$emit('change', {
            date: this.date
          })
          break
        case 'week':
          this.getWeek(mydate.getDay())
          break
        case 'month':
          let monthEnd = timeFormat(new Date(this.month.split('-')[0], this.month.split('-')[1], 0))
          this.$emit('change', {
            month: this.month,
            monthStart: this.month + '-01',
            monthEnd
          })
          break
        default:
          break
      }
    },

    //* *********************************************** //获取周 //************************************************

    // 获取当前周星期日
    getWeek(value) {
      let weekxq = value
      if (weekxq == 0) {
        this.weekstart = this.date
      } else {
        let weeka = new Date(this.date)
        let weeks = weeka.getDate() - weekxq
        weeka.setDate(weeks)
        this.weekstart = this.formatDate(weeka, 0)
      }
      this.getWeekarry(this.weekstart)
    },

    // 获取一周所有时间
    getWeekarry(values) {
      let ag = new Date(values) // console.log(new Date(values))

      let ac = null

      let weekaRrry = []

      for (let i = 0; i < 7; i++) {
        if (i == 0) {
          ac = values
        } else {
          ag.setDate(ag.getDate() + 1)

          ac = this.formatDate(ag, 0)
        }

        weekaRrry.push(ac)
      }

      this.weekend = weekaRrry[6] // console.log(weekaRrry)
      this.$emit('change', {
        weekstart: this.weekstart,
        weekend: this.weekend
      })
      // console.log(this.weekstart, this.weekend)
    },

    // 获取上一周开始时间和结束时间

    getaddweek() {
      if (
        this.limitPreviousDate &&
        new Date(uni.$u.timeFormat(new Date(), 'yyyy-mm-dd')).getTime() >=
          new Date(this.weekstart).getTime()
      ) {
        return
      }
      this.getWeekdate('1')
    },

    // 获取下一周开始时间和结束时间

    getalastweek() {
      if (
        this.limitLastDate &&
        new Date(uni.$u.timeFormat(new Date(), 'yyyy-mm-dd')).getTime() <=
          new Date(this.weekend).getTime()
      ) {
        return
      }
      this.getWeekdate('2')
    },

    getWeekdate(type) {
      let weeks = new Date(this.weekstart)

      let weekd = new Date(this.weekstart)

      let ag = null

      switch (type) {
        case '1':
          let weekls = weeks.getDate() - 7
          weekd.setDate(weekls)
          ag = this.formatDate(weekd, 0)
          this.weekstart = ag
          this.getWeekarry(this.weekstart) // 获取这周最后一天
          ag = null
          break

        case '2':
          let weekl = weeks.getDate() + 7
          weekd.setDate(weekl)
          ag = this.formatDate(weekd, 0)
          this.weekstart = ag
          this.getWeekarry(this.weekstart) // 获取这周最后一天
          ag = null
          break
        default:
          break
      }
    },

    //* *********************************************** //获取天 //************************************************ //后一天

    addDate() {
      if (
        this.limitLastDate &&
        new Date(uni.$u.timeFormat(new Date(), 'yyyy-mm-dd')).getTime() <=
          new Date(this.date).getTime()
      ) {
        return
      }
      let dates = new Date(this.date)
      this.getDaysOrMonth(dates, '1')
    }, // 前一天

    reduceDate() {
      if (
        this.limitPreviousDate &&
        new Date(uni.$u.timeFormat(new Date(), 'yyyy-mm-dd')).getTime() >=
          new Date(this.date).getTime()
      ) {
        return
      }
      let dates = new Date(this.date)

      this.getDaysOrMonth(dates, '2')
    },

    getDaysOrMonth(value, type) {
      switch (type) {
        case '1':
          let getDatesa = value.getDate() + 1
          value.setDate(getDatesa)
          this.date = this.formatDate(value, 0)
          break
        case '2':
          let getDates = value.getDate() - 1
          value.setDate(getDates)
          this.date = this.formatDate(value, 0)
          break
        // 上一个月
        case '3':
          let getMonths = value.getMonth() - 1
          value.setMonth(getMonths)
          this.month = this.formatDate(value, 1)
          break
        // 下一个月
        case '4':
          let getMonthsf = value.getMonth() + 1
          value.setMonth(getMonthsf)
          this.month = this.formatDate(value, 1)
          break
        default:
          break
      }
      if (type === '1' || type === '2') {
        // 日
        this.$emit('change', {
          date: this.date
        })
      } else if (type === '3' || type === '4') {
        // 月
        // 获取当前月份的最后一天
        let monthEnd = timeFormat(new Date(this.month.split('-')[0], this.month.split('-')[1], 0))
        this.$emit('change', {
          month: this.month,
          monthStart: this.month + '-01',
          monthEnd
        })
      }
    },

    //* *********************************************** //获取月 //************************************************

    // 上一个月
    reduceYear() {
      if (
        this.limitPreviousDate &&
        new Date(uni.$u.timeFormat(new Date(), 'yyyy-mm')).getTime() >=
          new Date(this.month).getTime()
      ) {
        return
      }
      let months = new Date(this.month)
      this.getDaysOrMonth(months, '3')
    },
    // 下一个月
    addYear() {
      if (
        this.limitLastDate &&
        new Date(uni.$u.timeFormat(new Date(), 'yyyy-mm')).getTime() <=
          new Date(this.month).getTime()
      ) {
        return
      }
      let months = new Date(this.month)
      this.getDaysOrMonth(months, '4')
    },
    formatDate(date, i) {
      // var year = date.getFullYear().toString()
      // var month = (date.getMonth() + 1).toString()
      // var day = date.getDate().toString()
      // console.log(year,month,day)
      // console.log(uni.$u.timeFormat(new Date(date), 'yyyy-mm-dd'))
      // if (month < 10) {
      // 	month = '0' + month
      // }
      // if (day < 10) {
      // 	day = '0' + day
      // }
      switch (i) {
        case 0:
          // this.month = uni.$u.timeFormat(new Date(), 'yyyy-mm')
          // year + '.' + month + '.' + day
          return uni.$u.timeFormat(new Date(date), 'yyyy-mm-dd')
          break
        case 1:
          return uni.$u.timeFormat(new Date(date), 'yyyy-mm')
          break
      }
    }
  }
}
</script>

<style scoped>
.font_size{
  font-size:36rpx ;
}
</style>

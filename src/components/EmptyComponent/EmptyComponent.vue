<template>
  <!-- 空白内容页-->
  <view class="empty_container" :style="[customEmptyStyle]">
      <image class= 'empty_content_img' :src="emptyImg" mode="scaleToFill" ></image>
      <text class= 'empty_content_txt'>{{ emptyContent }}</text>
  </view>
</template>

<script>
export default {
  name: "emptyCompontent",
  props: {
    emptyContent: {
      type: String,
      default: "没有相关内容"
    },
    customEmptyStyle: {
      type: Object,
      default: () => {
        return {}
      }
    }

  },
  data() {
    return {
      emptyImg: this.$imgPath.IMG_BG_EMPTY_GRAY
    };
  }
}
</script>

<style lang="scss" scoped>
.empty_container{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top:302rpx;

    .empty_content_img{
      display: flex;
      align-items: center;
      justify-content: center;
      width: 198rpx;
      height: 190rpx;
    }
    .empty_content_txt{
      font-size: 30rpx;
      color: #c2c5c8;
      margin-top: 30rpx;
      justify-self: center;

    }

}

</style>

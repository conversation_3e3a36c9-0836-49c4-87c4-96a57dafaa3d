<template>
  <!-- 下拉列表-->
  <view class="filter_drop_down_container padding-top-h5 padding-top-wechat" :style="{ height: vheight }" v-if="ishowDropDown" >

    <scroll-view scroll-y class="scroll-style" :style="[scrollStyle]" :show-scrollbar="dataList.length>10" enhanced>
    <view
      class="filter_drop_down_content"
      v-for="(item, index) in dataList"
      :key="index"
      @click="handlerDropDownClick(item)"
    >
      <view class="filter_drop_down_item">
        <view
          class="filter_drop_down_item_title"
          :class="{ active: chooseItem == item.name, no_active: chooseItem != item.name }"
        >
          {{ item.name }}
        </view>
        <u-icon
          v-if="chooseItem == item.name"
          name="checkbox-mark"
          :color="color.colorBtnOrange"
          size="28"
        ></u-icon>
      </view>
      <view class="horizontal_cell_line" v-if="index != dataList.length - 1"></view>
    </view>
  </scroll-view>
    <!-- 空数据-->
    <view class="filter_empty_view" v-if="dataList.length == 0">{{ emptyTxt }}</view>
    <!-- 遮罩层-->
    <view
      class="filter_drop_down_mask"
      :class="{ show: isShowMask, hide: isShowMask != true }"
      @tap="showDropDown(false)"
    ></view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'filterDropDown',
  data() {
    return {
      ishowDropDown: false, // 是否显示下拉菜单
      isShowMask: true, // 是否显示遮罩层
      isShowBottomLine: false, // 是否显示下面的分割线
      vheight: '', // 控件高度
      topHeight: this.topHeightDistance,
      chooseItem: '', // 选中的选项
      emptyTxt: this.$t('page.search.no.options.available'), // 暂无数据
      scrollStyle: { minHeight: '80rpx', background: "#fff" }
    }
  },
  props: {
    dataList: {
      type: Array,
      default: () => {
        return [
          {
            name: '第一项'
          },
          {
            name: '第二项'
          },
          {
            name: '第三项'
          }
        ]
      }
    },
    topHeightDistance: {
      // 距离底部的高度
      type: Number,
      default: 0
    },
    chooseTarget: {
      // 选择的项目
      type: String,
      default: ''
    }
  },
  computed: {
    ...mapGetters(['color'])
  },
  watch: {
    chooseTarget(newVal) {
      // console.log("watch chooseTarget", newVal);
      this.setChooseItem(newVal)
    },
    dataList: {
      handler(newValue) {
        if (newValue.length > 10) {
          this.scrollStyle = { height: "50vh", background: "#fff" }
        } else {
          this.scrollStyle = { minHeight: "80rpx", background: "#fff" }
        }
      },
      deep: true
    }
  },
  created() {
    // 计算屏幕剩余高度  填补剩余高度
    let that = this
    uni.getSystemInfo({
      success(res) {
        that.vheight = res.windowHeight - uni.upx2px(80) - that.topHeight + 'px'
        // console.log("vheight", res.windowHeight, that.vheight, that.topHeight);
      }
    })
  },
  methods: {
    /**
     * 显示与隐藏下拉列表
     * @param {*} isShow
     */
    showDropDown(isShow) {
      this.ishowDropDown = isShow
      this.$emit('showDropDownChange', this.ishowDropDown)
    },
    /**
     * 获取当前显示隐藏的状态
     *
     */
    getDropDownStatus() {
      return this.ishowDropDown
    },
    /**
     * 列表项目点击
     */
    handlerDropDownClick(data) {
      this.showDropDown(false)
      this.chooseItem = data.name
      this.$emit('handlerDropDownClick', data)
    },
    /**
     * 设置选中项目
     * @param {*} name
     */
    setChooseItem(name) {
      this.chooseItem = name
    },
    /**
     * 设置控件距离上面顶部的距离
     */
    setTopHeight(distance) {
      this.topHeight = distance
    }
  }
}
</script>

<style lang="scss" scoped>
// 去掉列表滚动条
::-webkit-scrollbar {
  display: none;
}
/* #ifdef H5 */
.padding-top-h5 {
  margin-top: 80rpx;
}
/* #endif */
/* #ifdef MP-WEIXIN || MP-ALIPAY */
.padding-top-wechat {
  margin-top: 70rpx;
}
/* #endif */
.filter_drop_down_container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;

    .scroll-style{
      z-index: 9999;
    }
    .filter_empty_view{
      width: 100%;
      height: 100rpx;
      line-height: 100rpx;
      color:#c2c5c8 ;
      font-size: 24rpx;
      text-align: center;
      background: #ffffff;
      z-index: 9999;
    }

  .filter_drop_down_mask {
    width: 100%;
    flex: 1 1 auto;
    flex-basis: 0;
    background-color: rgba(0, 0, 0, 0);
    transition: background-color 0.15s linear;
    overflow: hidden;
    z-index: 9000;
    &.show {
      background-color: rgba(0, 0, 0, 0.5);
    }
    &.hide {
      display: none;
    }
  }

}
.filter_drop_down_content{
    width: 100%;
    padding: 0 40rpx;
    background-color: #ffffff;
    box-shadow: 0 5px 5px rgba(0, 0, 0, 0.1);
    transition: transform 0.15s linear;
    z-index: 9999;
    .filter_drop_down_item {
      width: 675rpx;
      height: 80rpx;
      line-height: 80rpx;
      flex-direction: row;
      font-size: 24rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #ffffff;

      &.hide {
        display: none;
      }

      &.show {
        transform: translate3d(0, calc(44px + 1rpx), 0);
      }
      .filter_drop_down_item_title {
        font-size: 32rpx;
        color: #1d1e20;
        line-height: 60rpx;
        &.active {
          color: #fd953c;
        }
        &.no_active {
          color: #1d1e20;
        }
      }
    }

    .horizontal_cell_line {
      width: 675rpx;
      height: 1px;
      background-color: #eae9ed;
      margin: 0 auto;
    }
    }
</style>

<template>
  <view
    :class="['filter_layout_container','height-h5', filterDataLayoutList.length == 2 ? 'flex_evenly' : '']"
    :style="[filterCustomStyle]"
    catchtouchmove="true"
    @touchmove.stop.prevent="moveHandle"
  >
    <view
      @click="handlerItemClick"
      v-for="(item, index) in filterDataLayoutList"
      :key="index"
      :data-title="item.title"
      :data-index="index"
      :class="[
        'filter_layout_item',
        filterDataLayoutList.length == 2 ? 'flex_content_center' : '',
        currentIndex == index ? 'active' : '',
        currentIndex != index ? 'no_active' : ''
      ]"
    >
      <!--最左边显示的图标,可以自定义-->
      <u-icon
        v-if="item.leftIconName"
        :name="item.leftIconName"
        :color="item.leftIconColor ? item.leftIconColor : currentIndex == index?'active':'no_active'"
        :size="item.leftIconSize"
        :width="item.leftIconWidth"
        :height="item.leftIconHeight"
        >
      ></u-icon>
      <!--左侧标题-->
      <view class="filter_layout_content" :style="[filterTiltleCustomStyle]">{{ item.title }}</view>
      <!--右侧三角形-->
      <u-icon
        :name="isRightIconFill ? 'arrow-down-fill' : 'arrow-down'"
        v-if="currentIndex != index"
        width="24rpx"
        height="12rpx"
        class="filter_layout_arrow"
        :color="item.rightIconColor? item.rightIconColor : currentIndex == index?'#fd953c':'#bfc2c5'"
        size="24rpx"
      ></u-icon>
      <u-icon
        :name="isRightIconFill ? 'arrow-up-fill' : 'arrow-up'"
        v-if="currentIndex == index"
        width="24rpx"
        height="12rpx"
        class="filter_layout_arrow"
        :color="item.rightIconColor ? item.rightIconColor : currentIndex == index?'#fd953c':'#bfc2c5'"
        size="24rpx"
      ></u-icon>
    </view>
    <!--中间分隔线只有在长度等于2的时候才会显示-->
    <view
      class="vertical_line_style"
      v-if="filterDataLayoutList.length == 2 && isShowVerticalLine"
    ></view>
    <!--下拉列表-->
    <filterDropDown
      :topHeightDistance="topHeight"
      id="drowDown"
      ref="drowDown"
      @showDropDownChange="showDropDownChange"
      @handlerDropDownClick="handlerDropDownClick"
      :dataList="chooseDataList"
      :chooseTarget="chooseItem"
    ></filterDropDown>
  </view>
</template>

<script>
import filterDropDown from '@/components/FilterDropdown/FilterDropdown.vue'

export default {
  props: {
    filterDataLayoutList: {
      // 筛选层头部列表
      type: Array,
      default() {
        return [
          {
            title: '全部状态',
            chooseItem: '第一项',
            dataList: [
              {
                name: '第一项'
              },
              {
                name: '第二项'
              },
              {
                name: '第三项'
              }
            ]
          },
          {
            title: '卡状态',
            chooseItem: '未激活',
            dataList: [
              {
                name: '激活'
              },
              {
                name: '未激活'
              },
              {
                name: '作废'
              }
            ]
          },
          {
            title: '账户状态',
            chooseItem: '游客',
            dataList: [
              {
                name: '超级会员'
              },
              {
                name: '会员'
              },
              {
                name: '游客'
              }
            ]
          }
        ]
      }
    },
    filterCustomStyle: {
      // 自定义样式
      type: Object,
      default: () => ({ height: '90rpx' })
    },
    // 自定义样式
    isRightIconFill: {
      // 是否 右侧 图标 默认是实心的
      type: Boolean,
      default: true
    },
    filterTiltleCustomStyle: {
      // 标题自定义样式
      type: Object,
      default: () => ({})
    },
    filterRightIConCustomStyle: {
      // 右侧图标自定义样式
      type: Object,
      default: () => ({})
    },
    topHeight: {
      type: Number,
      default: 0
    },
    isShowVerticalLine: {
      // 是否显示中间的竖线
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      searchData: '', // 搜索内容,
      currentIndex: -1, // 默认选中的当前项目
      chooseDataList: [], // 下拉选择数据列表
      chooseItem: '', // 下拉列表选择的数据
      activeStyle: { 'font-size': '24rpx', color: '#8f9295' },
      noActiveStyle: { 'font-size': '24rpx', color: '#1d1e20' },
      chooseIndex: -1 // 记录每次点击列表的位置
    }
  },
  components: {
    filterDropDown
  },
  methods: {
    /**
     * 选择项选择
     */
    handlerItemClick(e) {
      // console.log(e)
      this.currentIndex = e.currentTarget.dataset.index
      this.chooseIndex = e.currentTarget.dataset.index
      var itemData = {
        index: e.currentTarget.dataset.index,
        title: e.currentTarget.dataset.title
      }

      // console.log('handlerItemClick', itemData)
      // 显示与隐藏下拉列表
      if (this.$refs.drowDown) {
        // 设置数据，
        this.setChooseList(
          Reflect.has(this.filterDataLayoutList[itemData.index], 'dataList')
            ? this.filterDataLayoutList[itemData.index].dataList
            : [],
          Reflect.has(this.filterDataLayoutList[itemData.index], 'chooseItem')
            ? this.filterDataLayoutList[itemData.index].chooseItem
            : ''
        )

        this.idShowDrow = !this.$refs.drowDown.getDropDownStatus()
        if (this.$refs.drowDown.getDropDownStatus()) {
          this.currentIndex = -1
        }
        this.$refs.drowDown.showDropDown(this.idShowDrow)
      }
      this.$emit('handlerFilterClick', itemData)
    },
    /**
     * 监听聚焦
     */
    searchFocus() {
      console.log('searchFocus')
      this.isShowClear = this.searchData.length > 0
    },
    /**
     * 监听失去焦点
     */
    searchFocusBlur() {
      console.log('searchFocusBlur')
      this.isShowClear = false
    },
    /**
     * 下拉列表变化
     * @param {*} e
     */
    showDropDownChange(e) {
      if (!e) {
        this.currentIndex = -1
      }
      this.$emit('showDropDownStatusChange', e)
    },
    /**
     * 下拉项目点击
     */
    handlerDropDownClick(data) {
      // console.log(data)
      this.$emit('handlerItemClick', data, this.chooseIndex)
    },
    /**
     * 设置
     * @param  dataList  数据列表
     * @param chooseItem 默认已选的的选项
     */
    setChooseList(dataList, chooseItem) {
      if (dataList) {
        this.chooseDataList = dataList
      }
      if (chooseItem) {
        this.chooseItem = chooseItem
      }
    },
    /**
     * 获取下拉框的显示状态
     */
    getDropDownShowStatus() {
      var isShowStatus
      if (this.refs.drowDown) {
        isShowStatus = this.refs.drowDown.getDropDownStatus()
      }
      return isShowStatus
    },
    /**
     * js禁止滚动
     */
    moveHandle() {

    }
  }
}
</script>

<style lang="scss" scoped>
/* #ifdef H5 */
.height-h5 {
  height: 80rpx;
}
/* #endif */

.filter_layout_container {
  background-color: #ffffff;
  font-size: 26rpx;
  color: #1d201e;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  padding: 0 40rpx;
  position: relative;
}

.flex_evenly {
  justify-content: space-evenly;
}
.flex_content_center {
  justify-items: center;
  justify-content: center;
  width: 100%;
}
.vertical_line_style {
  width: 1px;
  height: 48rpx;
  background-color: #eae9ed;
  position: absolute;
  top: 20rpx;
  left: 50%;
}
.filter_layout_item {
  display: inline-flex;

  &.active {
    color: #fd953c;
  }

  &.no_active {
    color: #1d1e20;
  }
  &.default {
    color: #bfc2c5;
  }

  .filter_layout_content {
    height: 40rpx;
    line-height: 40rpx;
    text-align: center;
  }
  .filter_layout_arrow {
    margin: 1rpx;
  }
}
</style>

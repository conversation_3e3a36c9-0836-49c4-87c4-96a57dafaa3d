<template>

   <view class="horizontal_cell"  v-bind:style="[cellStyle]" >
    <u-cell v-bind:style="[cellStyle]" :icon="leftIcon" :iconStyle="iconStyle" :title="titleTxt"  :isLink="isLink" :arrow-direction="arrowDirection" :url="linkUrl" @click="handlerClick(titleTxt)" :rightIcon="rightIcon" :clickable="clickable" :border="bottomBorder" :titleStyle="titleStyle" >

      <view  slot="value"  class="u-slot-value">
        <text
          v-if="!isCountDown&&isShowValue"
          v-bind:style="[valueStyle]"
           >{{valueTxt||'--'}}
        </text>
        <view  v-if="isCountDown">
          <u-count-down
            v-if="times>0"
            :time="times"
            :format="timeFormat"
            >
          </u-count-down>
          <view
            v-if="times<=0"
            class="u-slot-value">
            --
          </view>
      </view>
      </view>
    </u-cell>
    <view class="horizontal_cell_line" v-if="isShowBottomLine"></view>
  </view>
</template>

<script>
export default {
  props: {
    titleTxt: { // 左侧内容
      type: String,
      default: ""
    },
    valueTxt: { // 右侧内容
      type: String,
      default: ""
    },
    arrowDirection: { // 右侧箭头
      type: String,
      default: ""
    },
    linkUrl: { // 跳转url
      type: String,
      default: ""
    },
    leftIcon: { // 左侧显示的图标
      type: String,
      default: ""
    },
    isLink: { // 是否跳转，跳转要将linkUrl 也传进去
      type: Boolean,
      default: false
    },
    rightIcon: { // 右侧图标
      type: String,
      default: ""
    },
    clickable: { // 是否点击
      type: Boolean,
      default: false
    },
    bottomBorder: { // 是否显示下方间隔
      type: Boolean,
      default: false
    },
    isShowBottomLine: { // 是否显示底部横线
      type: Boolean,
      default: false
    },
    cellStyle: { // 单元格样式
      type: Object,
      default: () => ({})
    },
    titleStyle: {
      type: Object, // 标题样式
      default: () => ({})
    },
    valueStyle: {
      type: Object, // 右边样式
      default: () => ({})
    },
    iconStyle: { // 左侧显示的图标的样式
      type: Object, // 右边样式
      default: () => ({})
    },
    isCountDown: { // 是否显示倒计时
      type: Boolean,
      default: false
    },
    times: {
      type: Number,
      default: 0
    },
    timeFormat: {
      type: String,
      default: "HH:mm:ss"
    },
    isShowValue: {
      type: Boolean,
      default: true
    }

  },
  data() {
    return {

    }
  },

  methods: {
    /**
     * 自定义点击跳转
     * @param {内容} e
     */
    handlerClick(e) {
      this.$emit("handlerCellClick", e)
    }

  }

}

</script>

<style lang="scss" scoped>
.horizontal_cell{
  font-size: 28rpx;
  width: 100%;
  box-sizing: border-box;

  ::v-deep .u-cell__body{
    width: inherit;
    height: 100%;
    font-size: 28rpx;
    padding: 20rpx 30rpx;
      .u-cell__title-text{
        color: #8f9295;
      }
      .u-cell__value{
        color: #1d1e20;
      }
    }
    .horizontal_cell_line{
      width: 610rpx;
      height: 1rpx;
      background-color: #eae9ed;
      margin: 0 auto;
    }
    // .u-slot-value{
    //   height: 100%;
    //   line-height: 44rpx;
    //   text-align: center;
    // }
}

</style>

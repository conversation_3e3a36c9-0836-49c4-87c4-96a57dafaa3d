<template>
  <u-overlay :show="isShowDialog" @click="isShowDialog = false" catchtouchmove="true">
    <view class="image-preview-container" @tap.stop>
      <view class="image-preview-item">
        <view class="image-preview-title"> {{ dialogTitle }}</view>
        <view class="image-preview-content">
          <u-icon name="arrow-left" @click="handlerLeftImg"
            v-show="imgList && imgList.length > 1"></u-icon>
          <img :src="imgList[currentIndex]" mode="widthFix" class="img-tag" />
          <u-icon name="arrow-right" @click="handlerRightImg"
            v-show="imgList && imgList.length > 1 "></u-icon>
        </view>
        <view class="horizontal_cell_line"></view>
        <view class="image-preview-btns">
          <u-button plain :customStyle="customStyleBtn" class="btn-close-style" :color="color.colorBtnOrange"
            @click="handlerConfirm">{{ $t('btn.close') }}</u-button>
        </view>
      </view>
    </view>
  </u-overlay>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  props: {
    dialogTitle: {
      type: String,
      default: "图片预览"
    }
  },
  name: "ImagePreview",
  data() {
    return {
      isShowDialog: false, // 是否显示Diglog
      confirmTxt: this.$t('page.btn.confirm'), // 按钮文字
      qrCodesize: 200,
      customStyleBtn: {
        width: '100%',
        height: '90rpx',
        fontSize: '36rpx',
        border: 'none !important',
        padding: '0 !important',
        borderRadius: '0 0 20rpx 20rpx'
      },
      imgList: [], // 图片列表
      currentIndex: 0 // 当前图片索引
    };
  },
  computed: {
    ...mapGetters(['color'])
  },

  methods: {
    /**
     * 显示弹窗
     */
    showDialog() {
      console.log("showDialog");
      this.isShowDialog = true
    },
    /**
     * 隐藏弹窗
     */
    hideDialog() {
      console.log("hideDialog");
      this.isShowDialog = false
    },
    /**
     * 确定按钮
     */
    handlerConfirm() {
      this.isShowDialog = false
      this.$emit("handlerConfirm", true)
    },
    /**
     * 设置图片与当前index
     */
    setImgList(imgList, currentIndex) {
      console.log('this.imgList', imgList)
      this.imgList = imgList
      this.currentIndex = currentIndex
    },
    // 左侧按钮
    handlerLeftImg() {
      if (this.currentIndex <= 0) {
        return this.$u.toast("已经是第一张了")
      }
      this.currentIndex = this.currentIndex - 1
    },
    // 右侧按钮
    handlerRightImg() {
      if (this.currentIndex === this.imgList.length - 1) {
        return this.$u.toast("已经是最后一张了")
      }
      this.currentIndex = this.currentIndex + 1
    }
  }
}
</script>

<style lang='scss' scoped>
.image-preview-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.image-preview-item {
  width: 650rpx;
  min-height: 374rpx;
  background-color: #ffffff;
  border-radius: 20rpx;

  .image-preview-content {
    display: flex;
    justify-content: center;
    min-height: 200rpx;
    max-height: 1200rpx;
  }

  .image-preview-title {
    color: #1d1e20;
    font-size: 36rpx;
    margin: 20rpx 0 auto;
    text-align: center;
  }

  .horizontal_cell_line {
    width: 100%;
    height: 1rpx;
    background-color: #e5e5e5;

  }

  .img-tag {
    width: 500rpx;
    height: fit-content;
    margin: 20rpx;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      max-height: 900rpx;
    }
  }
}

.image-preview-btns {
  .btn-close-style {
    width: 100%;
    height: 90rpx;
    font-size: 36rpx;
    border: none !important;
    padding: 0 !important;
    border-radius: 0 0 20rpx 20rpx;
  }

}
</style>

<template>
  <view>
    <!-- 账号密码验证 -->
    <view class="input_wrap" v-if="type == '1'">
      <u-form :model="formData" :rules="rules" ref="Account">
        <view class="input_account">{{ $t('pages.login.zhanghao') }}</view>
        <u-form-item prop="phone">
          <u-input v-model="formData.phone" border="bottom" :placeholder="$t('pages.login.zhanghaos')"></u-input>
        </u-form-item>
        <view class="input_password">{{ $t('pages.login.passwords') }}</view>
        <u-form-item prop="password">
          <u-input password v-model="formData.password" border="bottom" :placeholder="$t('pages.login.password')"
            autocomplete="off"></u-input>
        </u-form-item>
        <view class="input_password">{{ $t('pages.login.Captcha') }}</view>
        <u-form-item prop="code">
          <u-input v-model="formData.code" border="bottom" :placeholder="$t('tip.page.login.code')" autocomplete="off" style="width: 100%;"
          maxlength="15"></u-input>
          <view class="vrcode">
            <img :src="vrCode" class="input-code" @click="handlerVrCodeClick">
          </view>
        </u-form-item>
      </u-form>
      <view class="sign_in_button">
        <!-- 登录 -->
        <!-- <u-button :color="color.themeColor" @click="loginzm">{{ $t('login') }}</u-button> -->
        <!-- 登录加防抖immediate=true点击时触发， -->
        <u-button :color="color.themeColor" @click="$u.debounce(loginAccount, 1000, true)">
          {{ $t('pages.login.login') }}
        </u-button>
      </view>
      <view class="input_bottom_text">
        <text @click="jumpPhone">{{ $t('pages.login.butsjh') }}</text>
        <text @click="goBackpass()">{{ $t('pages.login.passworder') }}</text>
      </view>
      <!--  <view class="input_bottom_jump_phone">
        <u-divider :text="$t('pages.login.qita')"></u-divider>
      </view>
      <view class="input__bottom_logo" @click="gowxd">
        <image :src="imgPath.IMG_WEIXIN_GREEN" mode="scaleToFill" ></image>
      </view> -->
    </view>
    <!-- 手机号验证码 -->
    <view class="input_wrap" v-if="type == '2'">
      <u-form :model="forndate" :rules="ruleser" ref="Captcha">
        <view class="input_account">{{ $t('pages.login.sj') }}</view>
        <u-form-item prop="phones">
          <u-input v-model="forndate.phones" border="bottom" :placeholder="$t('pages.login.zhanghs')"></u-input>
        </u-form-item>
        <view class="input_password">{{ $t('pages.login.Captcha') }}</view>
        <u-form-item prop="code">
          <u-input v-model="forndate.code" border="bottom" :placeholder="$t('pages.login.Pleaseerw')">
            <template v-slot:suffix>
              <u-code :keep-running="true" ref="uCode" @change="codeChange" seconds="60" changeText="X秒重新获取"></u-code>
              <u-button :color="color.themeColor" @tap="handlerRefreshCode" :text="Getverificationcode" type="success"
                size="mini" plain></u-button>
            </template>
          </u-input>
        </u-form-item>
      </u-form>
      <view class="sign_in_button">
        <!-- 登录 -->
        <u-button :color="color.themeColor" @click="$u.debounce(loginCaptcha, 2000, true)">
          {{ $t('pages.login.login') }}
        </u-button>
      </view>
      <view class="input_bottom_text" @click="jumpAccount()">
        <text>{{ $t('pages.login.ouracco') }}</text>
        <text></text>
      </view>
      <CustomDialogComponent ref="customDialogRef" customDialogTitle="选择项目点" @handlerCustomConfirm="pointsDialogConfirm"
        @handlerCustomCancel="pointsDialogCancel" dialogType="custom" :isCloseOverlay="false">
        <template #custom>
          <view class="content-container">
            <scroll-view style="height: 450rpx" scroll-y>
              <u-radio-group v-model="radioValue1" placement="column" @change="radioGroupChange">
                <!-- 单个项目点 start -->
                <view class="radio-card" v-for="items in userPointsData" :key="items.company_id">
                  <view class="uni-h6">
                    <text class="uni-h6-text">{{ items.company_name }}</text>
                    <u-radio :customStyle="{ width: '40rpx', height: '40rpx' }" :name="items.company_id" size="36rpx"
                      activeColor="orange" iconSize="26rpx"></u-radio>
                  </view>
                </view>
                <!-- 单个项目点 end -->
              </u-radio-group>
            </scroll-view>
          </view>
        </template>
      </CustomDialogComponent>
      <!-- <view class="input_bottom_jump_phone">
        <u-divider :text="$t('pages.login.qita')"></u-divider>
      </view> -->
      <!--  <view class="input__bottom_logo" @click="gowxd" >
        <image :src="imgPath.IMG_WEIXIN_GREEN" mode="scaleToFill" ></image>
      </view> -->
    </view>
    <verify-code ref="verifyCode" :is-number="true" @success="verifyCodeSuccess"
      @refresh="verifyCodeRefresh"></verify-code>
  </view>
</template>

<script>
import md5 from 'js-md5'
import { mapGetters } from 'vuex'
import { loginVerification, apiBackgroundGetPhoneCompanyInfo, apiBackgroundGetLoginVerifyCode, getLoginVerifyCode } from '@/api/user.js'
import CustomDialogComponent from './CustomDialogComponent/CustomDialogComponent.vue'
import Base64 from 'base-64'

export default {
  computed: {
    ...mapGetters(['color'])
  },
  // props: ['type'],
  props: {
    type: {
      type: Number,
      default: 1
    }
  },
  components: {
    CustomDialogComponent
  },
  data() {
    return {
      imgPath: this.$imgPath,
      userinfo: {},
      Getverificationcode: this.$t('login.getverificationcodgite'),
      formData: {
        // 账号密码
        phone: '',
        password: '',
        code: ''
      },
      forndate: {
        // 手机号验证码
        phones: '',
        code: ''
      },
      // 账号密码的验证
      rules: {
        // 账号密码
        phone: [
          {
            required: true,
            message: this.$t('pages.login.zhanghaos'),
            trigger: ['blur', 'change']
          },
          {
            max: 20,
            message: this.$t('login.characters'),
            trigger: ['blur', 'change']
          }
        ],
        password: [
          {
            required: true,
            message: this.$t('pages.login.password'),
            trigger: ['blur', 'change']
          },
          {
            pattern: /^(?=.*[0-9])(?=.*[a-zA-Z])[a-zA-Z0-9\W_]{8,20}$/,
            // 正则检验前先将值转为字符串
            transform(value) {
              // return String(value)
              return value.toString()
            },
            message: this.$t('login.numbers'),
            trigger: ['blur', 'change']
          },
          // 6-8个字符之间的判断
          {
            max: 20,
            message: this.$t('login.characters'),
            trigger: ['blur', 'change']
          }
        ],
        code: [
          {
            required: true,
            message: this.$t('pages.login.Pleaseerw'),
            trigger: ['blur', 'change']
          }
        ]
      },
      // 手机号验证码的验证
      ruleser: {
        // 手机号验证码
        phones: [
          {
            required: true,
            message: this.$t('pages.login.zhanghs'),
            trigger: ['blur', 'change']
          },
          {
            validator: (rule, value, callback) => {
              return uni.$u.test.mobile(value)
            },
            message: this.$t('login.phone'),
            // 触发器可以同时用blur和change
            trigger: ['change', 'blur']
          }
        ],
        code: [
          {
            type: 'string',
            required: true,
            len: 6,
            message: this.$t('pages.login.yanz'),
            trigger: ['change', 'blur']
          }
        ]
      },
      // 新增代码 --- 单选value
      radioValue1: '',
      // 新增代码 --- 保存多个项目点的信息
      userPointsData: [],
      vrCode: '', // 验证码
      resultTxtList: [], // 验证码数组
      vercodeBase64: '' // 验证码base64
    }
  },
  created() {
    console.log("onShow", this.type);
    if (this.type === 1) {
      this.getPicCode()
    }
  },
  methods: {
    radioGroupChange(val) {
      console.log(val)
    },
    // 账号密码登录
    loginAccount() {
      console.log('loginAccount')
      // 验证
      this.$refs.Account.validate()
        .then(res => {
          this.$showLoading({
            title: '加载中....',
            mask: true
          })
          //  登录调用
          let params = {
            username: this.formData.phone,
            password: md5(this.formData.password),
            verifyCode: this.formData.code,
            mode: 'account'
          }
          this.$store
            .dispatch('user/login', params)
            .then(res => {
              uni.hideLoading()
              if (res.code === 0 || res.code === 5 || res.code === 5) {
                // 成功回调
                console.log(res)
                uni.$u.toast(res.code === 0 ? this.$t('login.passert') : res.msg)
                // 跳转页面
                setTimeout(() => {
                  this.$miRouter.replace({ path: '/pages/login/organ' })
                }, 1000)
              } else {
                this.getPicCode()
                uni.$u.toast(res.msg)
              }
            })
            .catch(err => {
              uni.hideLoading()
              // 调接口失败
              uni.$u.toast(err.message)
            })
        })
        .catch(errors => {
          console.log('errors', errors)
          // 验证失败
          uni.$u.toast(this.$t('login.failed'))
        })
    },
    async loginCaptcha() {
      this.$refs.Captcha.validate()
        .then(res => {
          this.$showLoading({
            title: '加载中....',
            mask: true
          })
          // 之前是直接登录，现在要选择项目点，在多项目点的情况下会触发弹窗
          this.getAllPoints({
            phone: this.forndate.phones,
            sms_code: this.forndate.code,
            code: this.vercodeBase64
          })
          uni.hideLoading()
          // this.verificationLoginAPI({
          //   phone: this.forndate.phones,
          //   smsCode: this.forndate.code,
          //   mode: 'sms'
          // })
          // 手机号验证码登录
          // let params = {
          //   phone: this.forndate.phones,
          //   smsCode: this.forndate.code,
          //   mode: 'sms'
          // }
          // // this.getAllPoints({
          // //   phone: this.forndate.phones,
          // //   sms_code: this.forndate.code
          // // })
          // // 验证码无法发送了 --- 接下来要看本地存储里面的数据
          // this.$store
          //   .dispatch('user/Verification', params)
          //   .then(res => {
          //     uni.hideLoading()
          //     if (res.code === 0) {
          //       uni.$u.toast(this.$t('login.passert'))
          //       // 跳转页面
          //       setTimeout(() => {
          //         this.$miRouter.replace({ path: '/pages/login/organ' })
          //       }, 1000)
          //       // 打开弹窗
          //       // this.goOrangePage()
          //     } else {
          //       uni.$u.toast(res.msg)
          //     }
          //   })
          //   .catch(err => {
          //     uni.hideLoading()
          //     uni.$u.toast(err.message)
          //   })
        })
        .catch(errors => {
          // 验证失败
          uni.$u.toast(this.$t('login.phonenumber'))
        })
      // const res = await this.$to(apiBackgroundGetPhoneCompanyInfo({
      //   phone: 18903075625,
      //   sms_code: this.forndate.code
      // }))
      // console.log('项目点信息', res)
    },
    // 新增代码 --- orange页面跳转抽离
    goOrangePage() {
      setTimeout(() => {
        this.$miRouter.replace({ path: '/pages/login/organ' })
      }, 1000)
    },
    // 新增代码 --- 调用多项目点接口
    async getAllPoints(params) {
      const [error, res] = await this.$to(apiBackgroundGetPhoneCompanyInfo(params))
      if (error) {
        uni.$u.toast(error.message)
        return
      }
      if (res && res.code === 0) {
        var data = res.data || []
        if (data && Array.isArray(data)) {
          this.userPointsData = data.filter((items) => {
            return items.company_id !== 1
          })
        }
        if (this.userPointsData && this.userPointsData.length > 1) {
          this.$refs.customDialogRef.showCustomDialog()
          // 当项目点大于1时，先默认让单选框选中第一项
        } else {
          this.verificationLoginAPI({
            phone: this.forndate.phones,
            smsCode: this.forndate.code,
            mode: 'sms',
            code: this.vercodeBase64
          })
        }
      }
    },
    // 新增代码 --- 监听对话框确认
    pointsDialogConfirm() {
      if (!this.radioValue1) {
        this.$u.toast("请先选择项目点")
        return
      }
      if (this.$refs.customDialogRef) {
        this.$refs.customDialogRef.hideCustomDialog()
      }
      this.verificationLoginAPI({
        phone: this.forndate.phones,
        smsCode: this.forndate.code,
        mode: 'sms',
        companyId: this.radioValue1,
        code: this.vercodeBase64
      })
    },
    // 新增代码 --- 监听对话框取消
    pointsDialogCancel() {
      this.$refs.customDialogRef.hideCustomDialog()
      return
    },
    // 新增代码 --- 抽离原有的验证码登录接口的逻辑
    verificationLoginAPI(params) {
      this.$showLoading({
        title: '加载中....',
        mask: true
      })
      this.$store
        .dispatch('user/Verification', params)
        .then(res => {
          uni.hideLoading()
          if (res.code === 0) {
            uni.$u.toast(this.$t('login.passert'))
            // 跳转orange
            this.goOrangePage()
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.hideLoading()
          uni.$u.toast(err.message)
        })
    },
    jumpPhone() {
      this.$miRouter.replace({
        path: '/pages/login/login',
        query: {
          type: 2
        }
      })
    },
    jumpAccount() {
      this.$miRouter.replace({
        path: '/pages/login/login',
        query: {
          type: 1
        }
      })
    },
    goBackpass() {
      this.$miRouter.push({
        path: '/pages/login/backpass'
      })
    },
    codeChange(e) {
      // console.log('change', e)
      this.Getverificationcode = e
    },
    // 获取验证码
    getCode() {
      if (uni.$u.test.mobile(this.forndate.phones)) {
        if (this.$refs.uCode.canGetCode) {
          let params = { phone: this.forndate.phones, choices: 0, code: this.vercodeBase64 }
          loginVerification(params)
            .then(res => {
              if (res.code === 0) {
                // 通知验证码组件内部开始倒计时
                this.$refs.uCode.start()
                console.log(res)
              } else {
                uni.$u.toast(res.msg)
              }
            })
            .catch(err => {
              uni.$u.toast(err.message)
            })
        } else {
          // 倒计时结束后再发送
          uni.$u.toast(this.$t('login.afterthecountdown'))
        }
      } else {
        // 手机号不正确
        uni.$u.toast(this.$t('login.phonenumberncorrect'))
      }
    },
    // 获取图片验证码
    getPicCode() {
      apiBackgroundGetLoginVerifyCode().then(res => {
        if (res && res.code === 0) {
          this.vrCode = res.data || ''
        }
      }).catch(error => {
        console.log("error", error);
      })
    },
    // 验证码点击
    handlerVrCodeClick() {
      this.getPicCode()
    },
    // 验证成功
    verifyCodeSuccess(value) {
      console.log("verifyCodeSuccess", value);
      this.getCode()
    },
    // 图形验证码的刷新按钮
    verifyCodeRefresh() {
      this.getVerCode(this.forndate.phones, true)
    },
    // 刷新图形验证码
    handlerRefreshCode() {
      if (!this.$refs.uCode.canGetCode) return
      this.getVerCode(this.forndate.phones, false)
    },
    // 获取图形验证码
    getVerCode(phone, flag) {
      console.log("phone", phone);
      if (!phone || !/^1[3456789]\d{9}$/.test(phone)) {
        return this.$u.toast('请输入正确的手机号码')
      }
      getLoginVerifyCode({ phone }).then(res => {
        if (res && res.code === 0) {
          let data = res.data || ''
          if (data) {
            let keys = Base64.decode(data.key) ? JSON.parse(Base64.decode(data.key)) : ''
            this.vercodeBase64 = data.key
            console.log("getLoginVerifyCode", keys);
            this.resultTxtList = []
            if (keys && typeof keys === 'object') {
              for (let keyName in keys) {
                this.resultTxtList.push(keys[keyName])
              }
            }
            if (this.$refs.verifyCode) {
              this.$refs.verifyCode.setResultTxt(this.resultTxtList)
              if (flag) {
                this.$refs.verifyCode.init()
              } else {
                this.$refs.verifyCode.open()
              }
            }
          }
        } else {
          uni.$u.toast(res.msg)
        }
      }).catch(() => { })
    }
  }
}
</script>

<style lang="scss" scoped>
.input_account {
  // width: 214rpx;
  height: 35rpx;
  font-size: $font-size-xxl;
  color: $color-text;
  margin-top: 64rpx;
  margin-left: 12rpx;
}

.input_wrap {
  margin: 42rpx 80rpx;
}

.sign_in_button {
  margin-top: 64rpx;
}

.input_bottom_text {
  font-size: $font-size-xs;
  color: $color-text-a;
  display: flex;
  justify-content: space-between;
  margin-top: 22rpx;
}

.input_bottom_jump_phone {
  margin-top: 130rpx;
}

.input__bottom_logo {
  width: 78rpx;
  height: 78rpx;
  // background-color: #11e69e;
  margin: 28rpx auto;

  image {
    width: 100%;
    height: 100%;
  }
}

.content-container {
  ::v-deep .radio-card {
    display: flex;
    justify-content: center;
    margin-bottom: 15rpx;
  }
}

// .radio-card {
//   margin-left: 20rpx;
// }
.uni-h6 {
  display: flex;
  justify-content: space-between;
  width: 90%;
  border: 2rpx solid #ebeaea;
  padding: 30rpx;
  border-radius: 14rpx;

  .uni-h6-text {
    font-size: 30rpx;
  }

  .u-radio__icon-wrap--circle {
    ::v-deep .uicon-checkbox-mark {
      font-weight: bold !important;
    }
  }
}
 // #ifdef H5
.input-code {
  width: 200rpx;
  height: 80rpx;
  cursor: pointer;
  position: absolute;
  top: -60rpx;
  right: 0;
}
 // #endif
 // #ifdef MP_WEIXIN
.input-code {
  width: 200rpx;
  height: 80rpx;
  cursor: pointer;
  position: absolute;
  top: -90rpx;
  right: 0;
}
// #endif
.vrcode {
  position: relative;
}
</style>

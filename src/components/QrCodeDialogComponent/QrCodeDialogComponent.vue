<template>
  <u-overlay :show="isShowDialog" @click="show = false" catchtouchmove="true">
      <view class="qrCode_dialog_container"  @tap.stop>
        <view class="qrCode_dialog_item">
          <view class="qrCode_dialog_title"> {{qrDialogTitle }}</view>
          <view class="qrCode_dialog_code">
            <uqrcode  ref="uQRCode" :text="qrCodeContent" :size="qrCodesize"/>
          </view>
          <view class="horizontal_cell_line" ></view>
          <view class="qrCode_dialog_btns">
            <u-button plain   :customStyle="customStyleBtn"  class="qrCode_dialog_btn_style" :color="color.colorBtnOrange" @click="handlerQrConfirm()">{{ confirmTxt }}</u-button>
          </view>
        </view>
      </view>
  </u-overlay>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  props: {
    qrCodeContent: {
      type: String,
      default: ""
    },
    qrDialogTitle: {
      type: String,
      default: "设备激活码"
    }

  },
  name: "QrCodeDialogComponent",
  data() {
    return {
      isShowDialog: false, // 是否显示Diglog
      confirmTxt: this.$t('page.btn.confirm'), // 按钮文字
      qrCodesize: 200,
      customStyleBtn: {
        width: '100%',
        height: '90rpx',
        fontSize: '36rpx',
        border: 'none !important',
        padding: '0 !important',
        borderRadius: '0 0 20rpx 20rpx'
      }

    };
  },
  computed: {
    ...mapGetters(['color'])
  },

  methods: {
    /**
     * 显示弹窗
     */
    showQrDialog() {
      console.log("showQrDialog");
      this.qrCodesize = uni.upx2px(300);
      console.log("this.qrCodesize", this.qrCodesize);
      this.isShowDialog = true
    },
    /**
     * 隐藏弹窗
     */
    hideQrDialog() {
      console.log("hideQrDialog");
      this.isShowDialog = false
    },
    /**
     * 确定按钮
     */
    handlerQrConfirm() {
      this.isShowDialog = false
      this.$emit("handlerQrConfirm", true)
    }

  }
}
</script>

<style  lang ='scss' scoped>
.qrCode_dialog_container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .qrCode_dialog_item {
    width: 500rpx;
    height: 574rpx;
    background-color: #ffffff;
    border-radius: 20rpx;
    .qrCode_dialog_code{
      width: 300rpx;
      height: 300rpx;
      margin:  40rpx auto;

    }

    .qrCode_dialog_title{
      color: #1d1e20;
      font-size: 36rpx;
      margin: 56rpx 0 auto;
      text-align: center;
    }
    .horizontal_cell_line{
      width: 100%;
      height: 1rpx;
      background-color: #e5e5e5;

    }
  }

.qrCode_dialog_btns{
  .qrCode_dialog_btn_style{
    width: 100%;
    height: 90rpx;
    font-size: 36rpx;
    border: none !important;
    padding: 0 !important;
    border-radius: 0 0 20rpx 20rpx;
  }

}
</style>

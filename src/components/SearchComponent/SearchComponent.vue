<template>
   <view class="search_main">
    <!-- u-input 前置后置图标有些不兼容，这里自己家写兼容性好一些-->
    <view  class="search_item" id="searchTab" ref="searchTab" @click="handlerSearchLayoutClick" :style="{width:width}">
      <!--左侧搜索-->
      <u-icon name="search" class ="search_item_icon_left" color="#b0b7bd" size="30rpx"></u-icon>
      <!--中间输入-->
      <u-input
            v-model="searchData"
            type="text"
            :placeholder="searchPlaceholder"
            @change="searchChange"
            border="none"
            fontSize="28rpx"
            :focus="hasSearchFocus"
            @focus="searchFocus"
            @blur ="searchFocusBlur"
            :readonly="inputReadonly"
            :maxlength="maxlength"
          >
        </u-input>
      <!--右侧清除-->
      <u-icon  v-if="isShowClear" name="close-circle"  class ="search_item_icon_right" color="#b0b7bd" size="28rpx" @click="handlerClear" @tab="handlerClear"></u-icon>
      <view class="search_right_txt" v-if="searchData.length>0" @click="handlerSearch">{{$t("title.search")}} </view>

    </view>
    <!--下拉历史列表-->
    <view class="search_item_history" id = "hository" ref="hository" v-show="isShowHistoryLayout" :style="hositoryStyle">
      <!--下拉历史列表头部-->
      <view class="search_item_history_title">
        <view class="search_item_history_title_txt">{{ historyTxt }}</view>
        <view class="search_item_history_title_txt" @click="handlerClearHistory" >{{ clearHistoryTxt }}</view>
      </view>
       <!--下拉历史列表内容-->
       <view class="search_item_history_content">
          <view class="search_item_history_item" v-for="(item,index) in historyDataList" :key="index" @click="handlerHistoryItem(item)">
            {{ item }}
          </view>
       </view>

    </view>

  </view>
</template>

<script>
import Cache from '../../utils/cache'
export default {
  props: {
    searchPlaceholder: { // 输入框的提示
      type: String,
      default: ""
    },
    isShowHistory: { // 这个控制组件历史记录显示不显示的，可以修改
      type: Boolean,
      default: false
    },
    inputReadonly: { // 这个控制设置input 是否只读，设置只读不会造成组件置灰
      type: Boolean,
      default: false
    },
    hasSearchFocus: { // 是否自动获取焦点，默认是false
      type: Boolean,
      default: false
    },
    inputMaxLength: {
      type: Number,
      default: -1
    },
    historyCount: { // 历史记录条数，默认10条
      type: Number,
      default: 10
    },
    type: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '678rpx'
    }
  },
  data() {
    return {
      searchData: "", // 搜索内容
      isShowClear: false, // 是否显示清除图标，默认不显示
      historyTxt: this.$t('page.search.history'), // 历史记录txt
      clearHistoryTxt: this.$t('page.search.clear.history'), // 清除记录
      historyDataList: [],
      isShowHistoryLayout: this.isShowHistory,
      hositoryStyle: {},
      maxlength: this.inputMaxLength,
      clearTimeOut: null, // 控制清除按钮超时
      cacheSearchData: ''
    }
  },
  created() {
    var list = Cache.get(this.$common.CACHE_HISTORY_GOODS + this.type)
    console.log("list", list);
    if (list && list.length > 0) {
      this.historyDataList = list
    }
  },
  destroyed() {
    if (this.clearTimeOut) {
      clearTimeout(this.clearTimeOut)
    }
  },
  methods: {
    /**
     * 输入改变监听
     * @param {内容} e
     */
    searchChange(e) {
      console.log("searchChange", e);
      this.isShowClear = e && e.length > 0
      if (e && e.length > 0) {
        this.$emit("searchChange", e)
        this.cacheSearchData = e
      } else
      if (!e) {
        console.log("没有数据");
        if (this.cacheSearchData) {
          this.cacheSearchData = e
          this.$emit("searchClear", true)
        }
      }
    },
    /**
     * 清除内容
     */
    handlerClear() {
      console.log("handlerClear");
      this.isShowClear = false
      this.searchData = ""
    },
    /**
     * 监听聚焦
     */
    searchFocus() {
      console.log("searchFocus")
      this.$emit('searchFocus', true)
      this.isShowClear = this.searchData.length > 0
      if (this.isShowHistory) {
        this.isShowHistoryLayout = true
      }
    },
    /**
     * 监听失去焦点
     */
    searchFocusBlur() {
      console.log("searchFocusBlur")
      // 失去焦点不能立刻消失图标，会导致无法按，间隔1秒消失
      this.clearTimeOut = setTimeout(() => {
        this.isShowClear = false
        this.isShowHistoryLayout = false
        this.clearTimeOut = null
      }, 300);
    },
    /**
     * 清除历史记录
     */
    handlerClearHistory() {
      console.log("handlerClearHistory");
      this.historyDataList = []
      Cache.remove(this.$common.CACHE_HISTORY_GOODS + this.type)
    },
    /**
     * 点击其中一个历史
     * @param {*} e
     */
    handlerHistoryItem(data) {
      var content = data
      this.cacheSearchData = content
      this.searchData = content
      this.$emit("searchHandlerItem", content)
      this.isShowHistoryLayout = false
    },
    /**
     * 增加历史，需要在增加历史的时候调用这个方法
     */
    addhistory(content) {
      var strList = JSON.stringify(this.historyDataList)
      if (strList.indexOf(content) === -1) {
        // 增加历史到第一个
        this.historyDataList.unshift(content)
      }
      // 历史列表最大是historyCount,如果超出这个就删除后面的
      if (this.historyDataList.length > this.historyCount) {
        this.historyDataList.splice(this.historyCount, this.historyDataList.length - 10)
      }
      // 实际用的时候弄到缓存那里
      Cache.set(this.$common.CACHE_HISTORY_GOODS + this.type, this.historyDataList)
    },
    /**
     * 整个搜索layout的点击
     */
    handlerSearchLayoutClick() {
      this.$emit('handlerSearchLayoutClick', true)
    },
    /**
     * 搜索按钮点击
     */
    handlerSearch() {
      this.addhistory(this.searchData)
      this.$emit('handlerSearch', this.searchData)
    },
    /**
     * 设置或者隐藏历史记录栏目
     */
    setSearchLayoutVisible(isVisible) {
      this.isShowHistoryLayout = isVisible
    }

  }

}

</script>

<style lang="scss">
.search_main{
  width: 100%;
  background-color: white;
  padding: 26rpx 36rpx ;
  font-size: 28rpx;
  .search_item{
    width: 678rpx;
    height: 70rpx;
    background-color: #f0f3f5;
    border-radius: 12rpx;
    padding: 0 24rpx;
    display: flex;
    justify-items: center;
    justify-content: space-between;
    &u-input{
      font-size: 28rpx;
    }
    .search_item_icon_left .search_item_icon_right{
      width: 28rpx;
      height: 28rpx;
      color: red;
    }
    .search_right_txt{
      font-size: 36prx;
      color:$color-primary ;
      line-height: 70rpx;
      text-align: center;
      margin-left: 20rpx;
    }

  }
  .search_item_history{
    width: 100%;
    padding :0 36rpx  ;
    .search_item_history_title{
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 36rpx 0;
      .search_item_history_title_txt{
        font-size: 24rpx;
        color: #8f9295;
      }
    }
    .search_item_history_content{
      display: flex;
      flex-wrap: wrap;
      .search_item_history_item{
        height: 56rpx;
        line-height: 56rpx;
        border-radius: 8rpx;
        border: solid 1px #efefef;
        padding:0 28rpx;
        margin: 0 10rpx 10rpx 0;
      }
    }
  }

}

</style>

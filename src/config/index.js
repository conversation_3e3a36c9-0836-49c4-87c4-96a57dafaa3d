
// import { getRunSceneSync } from '@/utils/util'
let baseImgURL = 'https://packer-data-files.oss-cn-shenzhen.aliyuncs.com/merchant_app_static/merchant_app_icon'
let baseImgAppURL = 'https://packer-data-files.oss-cn-shenzhen.aliyuncs.com/mapp_static/'
let baseAPI = '/api'
let production = 'https://cashier-v4.packertec.com'
// #ifdef H5
production = window.location.origin
if (production && production.indexOf('file://') !== -1) {
  production = 'https://cashier-v4.debug.packertec.com'
}
// #endif
// 支付宝环境的话没法改同步。。。
// let envVersion = getRunSceneSync()
let envVersion = 'release'

// #ifdef MP-WEIXIN
envVersion = uni.getAccountInfoSync().miniProgram.envVersion;
// #endif

console.log("初始化", envVersion)
console.log("production", production)
// #ifdef MP-WEIXIN
switch (envVersion) {
  case "develop": // 开发版
    production = 'https://cashier-v4.debug.packertec.com'
    break;
  case "trial": // 体验版
    production = 'https://cashier-v4.debug.packertec.com'
    break;
  case "release": // 正式版
    production = 'https://cashier-v4.packertec.com'
    // production = 'https://cashier-v4.debug.packertec.com'
    break;
}
// #endif

// if(location.href.indexOf('debug') > -1){
// production = 'https://h5-v4.debug.packertec.com'//测试环境
// }else{
// production = "https://h5-v4.packertec.com"//正式环境
// }
// console.log(222, window.location.origin)
/** S API BaseURL **/
const baseURLMap = {
  // 开发环境
  // development: 'http://*************:8002',
  // development: 'http://************3:8000',
  development: "http://cashier-v4.debug.packertec.com",
  // 生产环境
  production: production
  // production: 'http://************:8000'
}
let baseURL = baseURLMap[process.env.NODE_ENV]

console.log("envVersion", envVersion)
console.log("process.env.NODE_ENV", process.env.NODE_ENV)
console.log("baseURL", baseURL)

// if (process.env.NODE_ENV === 'development' || (location && location.href.indexOf('debug') > -1)) {
//   const settingUrl = Cache.get('BASEURL')
//   if (settingUrl) {
//     baseURL = settingUrl
//   }
// }

export default {
  /**
   * @description Api请求数据路径
  */
  baseUrl: baseURL,
  /**
   * @description 线上图片地址
  */
  imgUrl: baseImgURL,
  /**
   *  api请求公用拦截
   */
  baseApi: baseAPI,
  /**
   * @description 线上图片地址mapp
  */
  baseImgAppURL: baseImgAppURL
};

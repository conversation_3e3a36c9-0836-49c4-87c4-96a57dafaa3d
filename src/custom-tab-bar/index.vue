<!-- 自定义底部tabbar -->
<template>
  <view>
    <view class="tabbar">
      <view class="tabbarContent" :style="[tabBarCustomStyle]">
        <view
          class="barItem"
          v-for="(item, index) in tabBar"
          :key="index"
          @click="navgetTo(item, index)"
        >
          <view>
            <view class="barImage">
              <image
                class="barImage"
                :src="index == currentPage ? item.imgClick : item.imgNormal"
                mode="aspectFit"
              ></image>
            </view>
            <view class="barText" :style="{ color: currentPage == index ? '#FD953C' : '#BABCBC' }">
              {{ $t(item.text) }}
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="standView"></view>
  </view>
</template>

<script>
import imgBasePath from '../common/imgBasePath';
import { getSystemInfo } from '@/utils/util'

export default {
  props: {
    currentPage: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      tabBar: [
        {
          text: 'tabbar.indexer',
          url: '/pages/index/index',
          imgNormal: imgBasePath.IMG_INDEX_HOME_DEFAULT,
          imgClick: imgBasePath.IMG_INDEX_HOME_ACTIVE
        },
        {
          text: 'tabbar.ai_assistant',
          url: '/pages/ai_assistant/index',
          imgNormal: imgBasePath.IMG_INDEX_AI_DEFAULT,
          imgClick: imgBasePath.IMG_INDEX_AI_ACTIVE
        },
        {
          text: 'tabbar.statistics',
          url: '/pages/statistics/statistics',
          imgNormal: imgBasePath.IMG_INDEX_MIDDLE_DEFAULT,
          imgClick: imgBasePath.IMG_INDEX_MIDDLE_ACTIVE
        },
        {
          text: 'tabbar.mine',
          url: '/pages/mine/mine',
          imgNormal: imgBasePath.IMG_INDEX_MINE_DEFAULT,
          imgClick: imgBasePath.IMG_INDEX_MINE_ACTIVE
        }
      ],
      tabBarCustomStyle: {}
    }
  },
  created() {
    console.log("custom-tab-bar created ");
    this.checkPhone()
  },
  methods: {
    navgetTo(item, index) {
      if (this.currentPage !== index) {
        uni.switchTab({
          url: item.url
        })
      }
    },
    /**
     * 检测手机型号
     */
    checkPhone() {
      // #ifdef MP-WEIXIN || MP-ALIPAY
      getSystemInfo().then(res => {
        var model = res.model
        console.log("model", model);
        if (model.indexOf("iPhone") !== -1) {
          // 苹果手机
          this.tabBarCustomStyle = { paddingBottom: 'env(safe-area-inset-bottom)' }
        }
      })
      // #endif
    }
  }
}
</script>

<style lang="scss" scoped>
.tabbar {
  z-index: 11000;
  position: fixed;
  bottom: 0rpx;
  left: 50%;
  transform: translate(-50%);
  width: 100%;
  min-height: 100rpx;
  background: #ffffff;
  // border-radius: 30rpx 30rpx 0rpx 0rpx;
  box-shadow: 0px -2rpx 6rpx 0rpx rgba(0, 0, 0, 0.06);
  // padding-bottom: env(safe-area-inset-bottom);

  .tabbarContent {
    display: flex;
    justify-content: space-around;
    margin-top: 20rpx;

    .barItem {
      width: 20%;
      height: 74rpx;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-around;

      .barImage {
        width: 100%;
        height: 44rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        image {
          width: 36rpx;
          height: 36rpx;
        }
      }

      .barText {
        height: 24rpx;
        width:100%;
        font-size: 18rpx;
        color: #babcbc;
        line-height: 22rpx;
        text-align: center;
        margin-top: 4rpx;
      }
    }
  }
}

.standView {
  width: 100%;
  height: 100rpx;
  padding-bottom: env(safe-area-inset-bottom);
}
</style>

{"pages.login.text": "Welcome to the merchant applet", "pages.login.toptexts": "In order to prevent others from misoperation, please contact the administrator to log in to the account", "pages.login.butzh": "Account Password Login", "pages.login.butsjh": "Sign in with your phone verification code", "pages.login.xieyi": "By logging in, you agree", "pages.login.xieyis": "Information Protection Protocol", "pages.login.qita": "Other ways to sign in", "pages.login.login": "login", "pages.login.hello": "hello", "pages.login.texer": "Welcome to the merchant side", "pages.login.zhanghao": "Mobile phone or account ", "pages.login.zhanghaos": "Please enter your account number/mobile phone number", "pages.login.passwords": "password", "pages.login.password": "Please enter a password", "pages.login.passworder": "Forgot password", "pages.login.sj": "phone", "pages.login.noword": "The passwords do not match, please re-enter", "pages.login.zhanghs": "Please enter your mobile phone number", "pages.login.Captcha": "<PERSON><PERSON>", "pages.login.Pleaseerw": "Please enter the verification code", "pages.login.ouracco": "Log in with your account password", "pages.login.yanz": "Please fill in the 6-digit verification code", "pages.login.Next": "Next", "pages.login.newpass": "newpassword", "pages.login.confirm": "Confirm the password", "pages.login.finish": "finish", "pages.login.back": "backpassword", "login.phone": "The mobile phone number is incorrect", "login.numbers": "Password length of 6 to 20 digits, combination of characters and number", "login.getverificationcodgite": "Get a verification code", "login.passert": "The verification passed", "login.Gettingverification": "Getting verification code", "login.verificationcode": "The verification code has been sent", "login.afterthecountdown": "Send after the countdown ends", "login.phonenumberncorrect": "The phone number is incorrect", "login.phonenumber": "The phone number or verification code is incorrect", "login.Captchaerror": "<PERSON><PERSON> error", "login.characters": "You can enter up to 20 characters", "login.failed": "Validation failed", "login.between": "Be between 6-8 characters long", "pages.index.organization": "Select Organization", "pages.index.technology": "PuShi technology", "pages.firm.santeen": "East Park Smart Canteen", "pages.firm.xanteen": "West Park Smart Canteen", "pages.firm.southteen": "South Park Smart Canteen", "pages.firm.ccanteen": "Canteen manager", "index.science": "Pushi Science and technology smart canteen", "index.role": "Account role", "index.accounts": "Switch accounts", "index.siness": "Business Data", "index.more": "See more", "index.edit": "Go to edit", "index.trends": "Turnover trends", "index.commonly": "There are no commonly used functions", "index.frequently": "Frequently used functions", "index.todo": "To do", "index.dispose": "dispose", "index.Confirm": "Confirm", "index.logInFirst": "Please log in first", "index.organization": "Please select an organization", "That.day": "That day", "That.Week": "Week", "That.Month": "Month", "That.Today": "Today", "That.now": "Today", "That.Tomorrow": "Tomorrow", "That.sevendays": "Last 7 days", "That.thirtydays": "Last 30 days", "That.definition": "definition", "statistics.revenue.monday": "Monday", "statistics.revenue.tuesday": "Tuesday", "statistics.revenue.wednesday": "Wednesday", "statistics.revenue.thursday": "Thursday", "statistics.revenue.friday": "Friday", "statistics.revenue.saturday": "Saturday", "statistics.revenue.sunday": "Sunday", "tabbar.indexer": "Home", "tabbar.statistics": "statistics", "tabbar.mine": "mine", "tabbar.Suggesteedback": "Suggest feedback", "tabbar.forwardfeedback": "Looking forward to your feedback", "tabbar.Signout": "Sign out", "tabbar.completefunction": "Please look forward to the complete function", "tabbar.ai_assistant": "AI assistant", "business.reports": "Business reports", "business.Situation": "Business Situation", "business.Revenue": "Revenue", "business.turnover": "turnover", "business.Turnover.analysis": "Turnover analysis", "business.Organizational": "Organizational comparison", "business.Revenue.composition": "Revenue composition", "business.Analysis.transactions": "Analysis of the number of transactions", "business.Analysis.Usage": "Usage of wallet type", "business.Analysis.Wallet": "Wallet analysis", "business.Revenue.details": "Revenue details", "business.Refund.Summary": "Refund Summary", "business.Segment.statistics": "Segment statistics", "business.Attendance.overview": "Attendance overview", "business.Sector.consumption": "Sector consumption", "business.Statistical.overview": "Statistical overview", "business.payment": "Postal savings payment", "business.Alipay": "Alipay", "business.Device.statistics": "Device statistics", "business.Wechat": "Wechat", "business.WeChat.Pay": "WeChat Pay", "business.Total.consumption": "Total consumption", "business.preferential.amount": "preferential amount", "business.number.consumption": "number of consumption", "business.how.many.consumed": "how many consumed", "business.Cash.payments": "Cash payments", "business.number.of.consumption": "number of consumption", "business.Refund.amount": "Refund amount", "business.actual.amount.received": "actual amount received", "business.ABC.pays": "ABC pays", "business.daily": "Business daily", "business.equipment": "equipment", "business.device.consumption": "Device consumption", "business.Turnover.refund": "Turnover - refund amount = amount received", "business.Amount.paid": "Turnover = Amount paid by the customer for the consumer order (turnover without subtracting the amount of consumption refund)", "business.Collection.Daily": "Collection Code Daily", "business.collection.received": "The amount of the collection received", "RMB": "RMB", "Meal": "Meal segments", "Meal.Breakfast": "Breakfast", "Meal.lunch": "lunch", "Meal.tea": "afternoon tea", "Meal.dinner": "dinner", "Meal.supper": "supper", "Meal.early": "early morning meal ", "Attendance.sign": "Sign in", "Attendance.out": "sign out", "Attendance.late": "be late", "Attendance.early": "leave early", "Attendance.leave": "ask for leave", "Attendance.card": " lack of card", "Turnover.day": "Turnover for the day", "See.more": "See more", "Collapse": "Collapse", "manner.Payment.type": "Payment type", "manner.How.to.eat": "How to eat", "manner.Dishes": "Dishes", "announcement": "Announcement management", "announcement.name": "Announcement name", "announcement.content": "Announcement content", "announcement.new": "Make new announcements", "announcement.Not.published": "Not published", "announcement.Published": "Published", "announcement.publish": "publish", "announcement.revise": "revise", "announcement.Delete": "Delete", "announcement.Please.enter": "Please enter", "announcement.Publish.now": "Publish now", "announcement.Click.Upload.image": "Click Upload image", "announcement.Please.content": "Please enter the content of the announcement", "services.Homeapp": "Home app", "services.Commonfunctionality": "Common functionality", "services.Canteenfunction": "Canteen function", "services.Allservices": "All services", "services.edit": "edit", "services.Cancel": "Cancel", "services.Save": "Save", "Compared.yesterday": "Compared yesterday", "Compared.yesterweek": "Compared last week", "Compared.yestermonth": "Compared  previous month", "Number.of.refunds": "Number of refunds", "statistics.financial.statement": "financial statement", "Access.Control": "Access Control Attendance Data", "Access.overview": "Access overview", "passenger.flow": "Overview of passenger flow", "passenger.day": "Total number of customers for the day", "passenger.analysis": "Segment customer count analysis", "Upload.image": "Upload an image", "Total.Consumer": "Consumer machines", "Total.Settlement": "Settlement desk ", "Total.Order": "Order machine", "Recipe.management": "Recipe management", "Recipe.Add.dishes": "Add dishes", "Recipe.Takedown": "Takedown", "Recipe.prompt": "After the dishes are removed, only the meal segment at that time is no longer sold to the public, are you sure to remove it?", "Recipe.currently": "There is currently no menu information, please add the dish first", "Select.all": "Select all", "page.user.userManager": "user management", "page.user.userDepartment": "user department", "page.user.searchHolder": "Please enter your name/mobile number/user code", "page.user.name": "name", "page.user.sex": "sex", "page.user.birthday": "birthday", "page.user.mobile": "mobile", "page.user.userId": "userId", "page.user.userCardNo": "userCardNumber", "page.user.facePhoto": "facePhoto", "page.user.department": "department", "page.user.ground": "ground", "page.user.freeze.account": "Freeze Account", "page.user.card.loss.reporting": "Loss Reporting", "page.user.upload.face": "Upload Face", "page.user.upload.pic": "Upload Picture", "page.user.view.detail": "View Details", "page.user.card.withdrawal": "Card withdrawal", "page.user.card.cancel.loss": "Cancel card loss", "page.user.card.activation.code": "Activation Code", "page.user.card.set.up": "Set Up", "page.btn.confirm": "confirm", "page.btn.cancel": "cancel", "page.tip.holder.please.choose": "Please Choose", "page.tip.holder.please.enter": "Please enter", "page.message.news.detail": "News Detail", "page.approval.application.time": "Application Time", "page.approval.approval.time": "Approval Time", "page.approval.remaining.processing.time": "remaining processing time", "page.approval.processing.time": "processing time", "page.approval.leave.start.time": "leave start time", "page.approval.leave.end.time": "leave end time", "page.approval.leave.type": "leave type", "page.approval.leave.reason": "leave reason", "page.approval.visitors": "visitors", "page.approval.visitor.type": "visitor type", "page.approval.visiting.time": "visiting time", "page.approval.audit.time": "Audit Time", "page.approval.order.type": "Order Type", "page.approval.payment.items": "Payment items", "page.approval.refund.type": "Refund Type", "page.approval.refund.amount": "Refund Amount", "page.approval.refund.reason": "Refund Reason", "page.approval.lack.of.cards.time": "lack of cards time", "page.approval.card.replacement.reason": "card replacement reason", "page.approval.btn.agree": "Agree", "page.approval.btn.refuse": "Refuse", "page.approval.order.pending.approval": "pending approval", "page.approval.order.rejected": "order rejecte", "page.approval.order.has.been.agreed": "order has been agreed", "page.approval.order.refund.request": "refund request", "page.approval.order.personnel.information": "personnel information", "page.approval.order.user.name": "user name", "page.approval.order.personnel.no": "personnel no", "page.approval.order.payment.order": "payment order", "page.approval.order.payment.type": "payment type", "page.approval.order.payment.amount": "payment amount", "page.approval.order.order.time": "order time", "page.approval.order.payment.time": "payment time", "page.approval.order.payment.date": "payment date", "page.approval.order.approval.results": "approval results", "page.approval.order.approval.content": "approval content", "page.approval.order.information": "order information", "page.approval.appointment.time": "time of appointment", "page.approval.modify.refund.amount": "modify refund amount", "page.approval.refundable.amount": "refundable amount", "page.approval.actual.refund.amount": "actual refund amount", "page.approval.whole.order.rejection": "whole order rejection", "page.approval.whole.order.refund": "whole order refund", "page.approval.complete.processing": "complete processing", "page.approval.order.rejection": "order rejection", "page.approval.order.rejection.reason": "order rejection reason", "page.approval.card.replacement.type": "card replacement type", "page.approval.attendance.group": "attendance group", "page.invitation.submit": "submit", "page.invitation.required": "required", "page.invitation.optional": "optional", "page.invitation.visitor.information": "visitor information", "page.invitation.access.information": "access information", "page.invitation.reason.for.visit": "reason for visit", "page.invitation.idCard.number": "idCard", "page.invitation.vistor.type": "vistor type", "page.invitation.policy.type": "policy type", "page.invitation.access.organization": "access organization", "page.invitation.remarks": "remarks", "tip.please.enter.the.visitors.name": "please enter the visitor's name", "tip.please.enter.the.visitors.mobile": "please enter the visitor's mobile phone number", "tip.please.enter.the.visitors.idcard": "please enter the visitor's idcard number", "tip.please.select.a.vistor.type": "please select a vistor type", "tip.please.select.a.policy.type": "please select a policy type", "tip.please.select.the.organization.to.visit": "please select the organization to visit", "tip.enter.the.reason": "please enter the reason for the visit or other comments", "page.order.create.time": "create time", "page.order.the.user.name": "user name", "page.order.order.amount": "order amount", "page.order.failure.reason": "failure reason", "page.order.order.number": "order number", "page.order.btn.cancel.order": "cancel order", "page.order.btn.deduction.from.original.price": "deduction from original price", "page.order.btn.rededuction": "rededuction", "tip.order.please.enter.name.mobile.no": "please enter your name/phone/user number", "tip.order.please.enter.name.mobile": "please enter your name/phone", "page.recommend.reply": "reply", "page.recommend.appraise": "appraise", "page.recommend.suggest": "suggest", "page.recommend.replied": "replied", "page.recommend.no.reply": "no reply", "page.recommend.taste": "taste", "page.recommend.price": "price", "page.recommend.service": "service", "page.recommend.hygienism": "hygienism", "page.recommend.evaluation.content": "evaluation content", "page.recommend.feedback.content": "feedback content", "page.recommend.dishes": "Dishes", "page.recommend.all": "all", "page.recommend.dishes.picture": "dishes picture", "page.recommend.reply.content": "reply content", "page.recommend.reply.pic": "reply picture", "page.recommend.food.storage.code": "food storage code", "page.recommend.number.of.free.cabinets": "number of free cabinets", "page.recommend.confirm.reply": "confirm reply", "page.recommend.reply.type": "reply type", "page.recommend.view.result": "view result", "tip.no.search.content": "No relevant content was searched", "page.search.history": "history", "page.search.clear.history": "clear history", "page.search.no.options.available": "no options available", "title.application.approval": "application approval", "title.smart.warning": "smart warning", "title.visitor.invitation": "visitor invitation", "title.food.storage.management": "food storage management", "title.evaluation.recommendations": "evaluation recommendations", "title.message.notification": "message notification", "title.user.department": "user department", "title.department.name": "department name", "title.personnel.information": "personnel information", "title.device.management": "device management", "title.details": "detail", "title.order.supplementary.deduction": "order supplementary deduction", "title.bright.kitchen.bright.stove": "bright kitchen bright stove", "title.add.dishes": "add dishes", "title.daily.inspection": "daily inspection", "tip.please.enter.a.keyword.search": "please enter a keyword search", "page.add.dishes.listing.Menu": "listing Menu", "page.add.dishes.btn.add": "add", "page.add.dishes.btn.add.already": "added", "page.user.manager.user.grouping": "user grouping", "tip.loading": "loading...", "title.search": "Search", "tip.prompt": "prompt", "tip.prompt.card.withdrawal": "Attention: This operation requires caution! After card refund, the card will no longer be associated with the account and will be a non master card.", "tip.prompt.card.freeze.account": "Confirm if the user still has pending/offline orders。 After freezing, the user cannot perform consumption/recharge/withdrawal operations until the frozen status is lifted。", "tip.prompt.card.loss.reporting": "Are you sure to report the loss of the current user's card? After reporting the loss, changing the card will no longer allow for card swiping consumption! Until the loss reporting status is lifted!", "tip.prompt.card.cancel.loss": "Are you sure to cancel the loss reporting of the current card?", "tip.list.empty": "no data available", "dic.date.within.three.day": "within three day", "dic.date.within.seven.day": "within seven day", "dic.date.within.a.week": "within a week", "dic.date.within.one.week": "within one week", "dic.date.within.three.week": "within three week", "dic.date.within.three.month": "Last 3 months", "dic.date.within.six.month": "Last 6 months", "dic.date.all.time": "all time", "dic.date.select.time": "time select", "dic.date.warning.type": "type", "dic.date.all.type": "all type", "dic.type.refund.appeal": "refund appeal", "dic.type.visitor.application": "visitor application", "dic.type.payment.refund": "payment refund", "dic.type.card.replacement.application": "card replacement application", "dic.type.leave.application": "leave application", "dic.type.profit.margin.loss": "profit margin loss", "dic.type.profit.margin.earnings": "profit margin earnings", "dic.type.proportion.of.raw.materials": "proportion of raw materials", "dic.type.get.materials": "Get materials out of the warehouse", "dic.type.allot.out": "Transfer out of the warehouse", "dic.type.wastage": "Loss out of storage", "dic.type.return.goods": "Return goods to warehouse", "dic.type.other.out": "Other outbound", "dic.type.purchase": "Purchase warehousing", "dic.type.allot.in": "Transfer to the warehouse", "dic.type.other.in": "Other warehousing", "dic.type.purchase.order": "Purchase order", "dic.type.catering.service.license": "Catering service license", "dic.type.food.business.license": "Food business license", "dic.type.food.production.license": "Food production license", "dic.type.sanitary.permit": "Sanitary permit", "dic.type.health.certificate": "Health certificate", "dic.type.supplier.entry.certificate": "Supplier shortlisting contract", "title.picture": "picture", "tip.whether.or.not": "whether.or.not", "tip.are.you.sure.you.agree": "Are you sure you agree to the approval of this order?", "tip.are.you.sure.you.agree.to.the.leave.application": "Are you sure to agree the leave application?", "tip.are.you.sure.you.reject.to.the.leave.application": "Are you sure to reject the leave application?", "tip.are.you.sure.you.agree.to.the.replacemen.card.application": "Are you sure you agree to the replacemen card application?", "tip.are.you.sure.you.reject.to.the.replacemen.card.application": "Are you sure you reject to the replacemen card application?", "pages.info.face_photograph": "face acquisition", "tip.show.only.last.three.month.order": "only the first three months of supplementary deduction orders will be displayed. If you need to view more supplementary deduction orders, please log in to the computer to check!", "tip.update.prompt": "update prompt", "tip.new.version.ready": "The new version is ready. Do you want to restart the application?", "tip.manager.log.in.pc": "Overmanagement personnel, please log in on the PC?", "tip.success": "success", "tip.fail": "fail", "tip.page.announce.public.announce": "Are you sure you want to publish an announcement? Once published, it cannot be revoked。", "tip.page.announce.delete.announce": "Are you sure you want to delete the announcement? It cannot be restored after deletion。", "tip.view.all": "view all", "page.login.ver.code.error": "Incorrect verification code", "page.menu.inventory": "inventory", "page.menu.listing.dishes": "listing.dishes", "page.menu.listing": "listing", "page.menu.tip.listing.first": "Confirm the dishes are listed on", "page.menu.tip.listing.second": "the menu", "page.menu.dishes": "dishes", "page.menu.tip.warn": "Note: Please enter '-1' for unlimited inventory", "tip.page.login.code": "please enter verification code", "page.service.menu.other.function": "other function", "page.service.menu.supervise.function": "supervise function", "page.service.menu.smart.warning": "smart warning", "page.service.menu.purchase.sales.inventory": "purchase sales inventory", "title.inventory.take.delivery": "take delivery", "page.service.menu.order.input.tip": "Enter document number to query", "tip.order.please.enter.purchase.name": "please enter name of the material", "tip.take.photo.warning": "Please face the camera and keep your facial features clear", "page.inventory.penson.verification.personnel": "verification personnel", "page.inventory.penson.verification.add": "penson verification add", "page.inventory.harvest.live.facial.recognition": "harvest live facial recognition", "tip.goods.please.enter.purchase.name": "please enter purchase name", "page.inventory.goods.price": "price", "page.inventory.goods.number": "quantity received", "tip.please.upload.purchase.voucher": "please upload purchase voucher", "tip.click.upload.image": "click upload image", "page.inventory.voucher.delivery.information": "delivery information", "page.inventory.voucher.delivery.type": "delivery type", "page.inventory.voucher.driver.name": "driver name", "page.inventory.voucher.driver.phone": "driver phone", "page.inventory.voucher.driver.certificate": "driver certificate", "page.inventory.voucher.delivery.temperature": "delivery temperature", "page.inventory.voucher.car.information": "car information", "page.inventory.voucher.car.number": "car number", "page.inventory.voucher.car.pic": "car pic", "tip.please.enter.remark": "please enter remark", "btn.close": "btn close", "page.inventory.voucher.car.type": "car type", "page.inventory.material.warehouse": "material warehouse", "page.inventory.material.selection": "material selection", "page.inventory.goods.specifications": "specifications", "page.service.menu.accompanying.meal.record": "accompanying meal record", "page.service.menu.accompanying.meal.history": "accompanying meal history", "page.meal.accompanying.dining.cafeteria": "dining cafeteria", "page.meal.take.photo": "take photo"}
{"pages.login.text": "欢迎登录商户端小程序", "pages.login.toptexts": "为防止他人误操作，登录账号请联系管理员获取", "pages.login.butzh": "账号密码登录", "pages.login.butsjh": "手机验证码登录", "pages.login.xieyi": "我已阅读并同意", "pages.login.xieyis": "信息保护协议", "pages.login.qita": "其他方式登录", "pages.login.login": "登录", "pages.login.hello": "你好", "pages.login.texer": "欢迎使用商户端", "pages.login.zhanghao": "手机号或账号", "pages.login.zhanghaos": "请输入账号/手机号", "pages.login.passwords": "密码", "pages.login.password": "请输入密码", "pages.login.passworder": "忘记密码", "pages.login.noword": "密码不一致，请重新输入", "pages.login.sj": "手机号", "pages.login.zhanghs": "请输入手机号", "pages.login.Captcha": "验证码", "pages.login.Pleaseerw": "请输入验证码", "pages.login.ouracco": "账号密码登录", "pages.login.yanz": "请填写6位验证码", "pages.login.Next": "下一步", "pages.login.newpass": "新密码", "pages.login.confirm": "确认密码", "pages.login.finish": "完成", "pages.login.back": "找回密码", "login.phone": "手机号码不正确", "login.numbers": "密码长度8~20位，英文加数字加特殊字符", "login.Captchaerror": "验证码错误", "login.Gettingverification": "正在获取验证码", "login.verificationcode": "验证码已发送", "login.phonenumber": "手机号或验证码错误", "login.between": "长度在8-20个字符之间", "login.characters": "最多可输入 20 位字符", "login.getverificationcodgite": "获取验证码", "login.passert": "登录成功", "login.failed": "账号密码验证失败", "login.afterthecountdown": "倒计时结束后再发送", "login.phonenumberncorrect": "手机号不正确", "pages.index.organization": "选择组织", "pages.index.technology": "朴食科技", "pages.firm.santeen": "东园智慧食堂", "pages.firm.xanteen": "西园智慧食堂", "pages.firm.southteen": "南园智慧食堂", "pages.firm.ccanteen": "食堂经理", "index.science": "朴食科技智慧食堂", "index.role": "账户角色", "index.accounts": "切换账号", "index.siness": "营业数据", "index.more": "查看更多", "index.trends": "营业额走势", "index.frequently": "常用功能", "index.todo": "待办事项", "index.edit": "去编辑", "index.commonly": "暂无常用功能", "index.dispose": "去处理", "index.Confirm": "确认", "index.logInFirst": "请先登录", "index.organization": "请选择组织", "That.day": "当天", "That.Week": "本周", "That.Month": "本月", "That.Today": "今天", "That.now": "今", "That.Tomorrow": "明", "That.sevendays": "最近7天", "That.thirtydays": "最近30天", "That.definition": "自定义", "statistics.revenue.monday": "周一", "statistics.revenue.tuesday": "周二", "statistics.revenue.wednesday": "周三", "statistics.revenue.thursday": "周四", "statistics.revenue.friday": "周五", "statistics.revenue.saturday": "周六", "statistics.revenue.sunday": "周日", "tabbar.indexer": "首页", "tabbar.statistics": "统计", "tabbar.mine": "我的", "tabbar.Suggesteedback": "建议反馈", "tabbar.forwardfeedback": "期待您的反馈", "tabbar.Signout": "退出登录", "tabbar.completefunction": "功能完善中，请期待", "tabbar.ai_assistant": "AI助手", "business.reports": "营业报表", "business.Situation": "经营情况", "business.Revenue": "营收额", "business.turnover": "营业额", "business.Turnover.analysis": "营业额分析", "business.Organizational": "组织对比", "business.Revenue.composition": "营收构成", "business.Analysis.transactions": "交易笔数分析", "business.Analysis.Usage": "钱包类型使用情况", "business.Analysis.Wallet": "钱包分析", "business.Revenue.details": "营收详情", "business.Refund.Summary": "退款汇总", "business.Segment.statistics": "餐段统计", "business.Attendance.overview": "考勤概况", "business.Sector.consumption": "部门消费", "business.Statistical.overview": "统计概况", "business.Collection.Daily": "收款码日报", "business.collection.received": "收款金额", "business.Device.statistics": "设备统计", "business.payment": "邮储支付", "business.Alipay": "支付宝", "business.Wechat": "微信", "business.WeChat.Pay": "微信支付", "business.Cash.payments": "现金支付", "business.ABC.pays": "农行支付", "business.Total.consumption": "消费总额", "business.preferential.amount": "优惠金额", "business.number.consumption": "消费次数", "business.how.many.consumed": "消费笔数", "business.number.of.consumption": "消费人数", "business.See.more": "查看更多", "business.Refund.amount": "退款金额", "business.Number.of.refunds": "退款笔数", "business.actual.amount.received": "实收金额", "business.daily": "营业报表", "business.Collapse": "收起", "business.equipment": "设备", "business.Access.Control": "门禁考勤数据", "business.device.consumption": "设备消费", "business.Access.overview": "门禁概况", "business.Amount.paid": "营业额 = 消费订单客户支付金额（营业额没有减去消费退款金额）", "business.Turnover.refund": "营业额-退款金额=实收金额", "RMB": "元", "Meal": "餐段", "Meal.Breakfast": "早餐", "Meal.lunch": "午餐", "Meal.tea": "下午茶", "Meal.dinner": "晚餐", "Meal.supper": "夜宵", "Meal.early": "凌晨餐", "Attendance.sign": "签到", "Attendance.out": "签退", "Attendance.late": "迟到", "Attendance.early": "早退", "Attendance.leave": "请假", "Attendance.card": "缺卡", "Turnover.day": "当天营业额", "manner.Payment.type": "支付类型", "manner.How.to.eat": "就餐方式", "manner.Dishes": "菜品", "announcement": "公告管理", "announcement.name": "公告名称", "announcement.content": "公告内容", "announcement.new": "发布新公告", "announcement.Not.published": "未发布", "announcement.Published": "已发布", "announcement.publish": "发布", "announcement.Delete": "删除", "announcement.revise": "修改", "announcement.Click.Upload.image": "点击上传图片", "announcement.Please.content": "请输入公告的内容", "announcement.Please.enter": "请输入公告名称", "announcement.Publish.now": "立即发布", "announcement.Upload.image": "上传图片", "services.Homeapp": "首页应用", "services.Commonfunctionality": "通用功能", "services.Canteenfunction": "食堂功能", "services.Allservices": "全部服务", "services.edit": "编辑", "services.Cancel": "取消", "services.Save": "保存", "Compared.yesterday": "较昨日", "Compared.yesterweek": "较上周", "Compared.yestermonth": "较上月", "statistics.financial.statement": "财务报表", "Select.all": "全选", "Total.Consumer": "消费机", "Total.Settlement": "结算台", "Total.Order": "点餐机", "Recipe.management": "菜谱管理", "Recipe.currently": "当前无菜品信息，请先添加菜品", "Recipe.Takedown": "下架", "Recipe.Add.dishes": "添加菜品", "Recipe.prompt": "菜品下架后，仅该时间的餐段不再对外出售，确定要下架？", "passenger.flow": "客流概况", "passenger.day": "当天总顾客数", "passenger.analysis": "餐段顾客数分析", "page.user.userManager": "用户管理", "page.user.userDepartment": "用户部门", "page.user.searchHolder": "请输入姓名/手机号/用户编号", "page.user.name": "姓名", "page.user.sex": "性别", "page.user.birthday": "出生日期", "page.user.mobile": "手机号码", "page.user.userId": "用户编号", "page.user.userCardNo": "卡号", "page.user.facePhoto": "人脸照片", "page.user.department": "部门", "page.user.ground": "分组", "page.user.freeze.account": "冻结账号", "page.user.card.loss.reporting": "挂失卡", "page.user.upload.face": "上传人脸", "page.user.upload.pic": "上传图片", "page.user.view.detail": "查看详情", "page.user.card.withdrawal": "退卡", "page.user.card.cancel.loss": "取消挂失", "page.user.card.activation.code": "激活码", "page.user.card.set.up": "设置", "page.btn.confirm": "确认", "page.btn.cancel": "取消", "page.tip.holder.please.choose": "请选择", "page.tip.holder.please.enter": "请输入", "page.message.news.detail": "消息详情", "page.approval.application.time": "申请时间", "page.approval.approval.time": "审批时间", "page.approval.audit.time": "审核时间", "page.approval.remaining.processing.time": "剩余处理时间", "page.approval.processing.time": "处理时间", "page.approval.leave.start.time": "请假开始时间", "page.approval.leave.end.time": "请假结束时间", "page.approval.leave.type": "请假类型", "page.approval.leave.reason": "请假原因", "page.approval.visitors": "来访人员", "page.approval.visitor.type": "访客类型", "page.approval.visiting.time": "来访时间", "page.approval.order.type": "订单类型", "page.approval.payment.items": "缴费项目", "page.approval.refund.type": "退款类型", "page.approval.refund.amount": "退款金额", "page.approval.refund.reason": "退款原因", "page.approval.lack.of.cards.time": "缺卡时间", "page.approval.card.replacement.reason": "补卡原因", "page.approval.btn.agree": "同意", "page.approval.btn.refuse": "拒绝", "page.approval.order.pending.approval": "订单待审批", "page.approval.order.rejected": "订单已拒绝", "page.approval.order.has.been.agreed": "订单已同意", "page.approval.order.refund.request": "退款申请", "page.approval.order.personnel.information": "人员信息", "page.approval.order.user.name": "用户姓名", "page.approval.order.personnel.no": "人员编号", "page.approval.order.payment.order": "缴费订单", "page.approval.order.payment.type": "缴费类型", "page.approval.order.payment.amount": "缴费金额", "page.approval.order.order.time": "下单时间", "page.approval.order.payment.time": "支付时间", "page.approval.order.payment.date": "到账时间", "page.approval.order.approval.results": "审批结果", "page.approval.order.approval.content": "审批内容", "page.approval.order.information": "订单信息", "page.approval.appointment.time": "预约时间", "page.approval.modify.refund.amount": "修改退款金额", "page.approval.refundable.amount": "可退金额", "page.approval.actual.refund.amount": "实际退款金额", "page.approval.whole.order.rejection": "整单驳回", "page.approval.whole.order.refund": "整单退款", "page.approval.complete.processing": "完成处理", "page.approval.order.rejection": "驳回", "page.approval.order.rejection.reason": "驳回原因", "page.approval.card.replacement.type": "补卡类型", "page.approval.attendance.group": "所属考勤组", "page.invitation.submit": "提交", "page.invitation.required": "必填", "page.invitation.optional": "选填", "page.invitation.visitor.information": "访客信息", "page.invitation.access.information": "访问信息", "page.invitation.reason.for.visit": "来访理由", "page.invitation.idCard.number": "身份证号", "page.invitation.vistor.type": "访客类型", "page.invitation.policy.type": "策略类型", "page.invitation.access.organization": "访问组织", "page.invitation.remarks": "备注", "tip.please.enter.the.visitors.name": "请输入来访人姓名", "tip.please.enter.the.visitors.mobile": "请输入来访人手机号", "tip.please.enter.the.visitors.idcard": "请输入来访人身份证号", "tip.please.select.a.vistor.type": "请选择访客类型", "tip.please.select.a.policy.type": "请选择策略类型", "tip.please.select.the.organization.to.visit": "请选择访问的组织", "tip.enter.the.reason": "请输入访问缘由或其他备注", "page.order.create.time": "创建时间", "page.order.the.user.name": "用户名", "page.order.order.amount": "订单金额", "page.order.failure.reason": "失败原因", "page.order.order.number": "订单号", "page.order.btn.cancel.order": "取消订单", "page.order.btn.deduction.from.original.price": "原价扣款", "page.order.btn.rededuction": "重新扣款", "tip.order.please.enter.name.mobile.no": "请输入姓名/手机号/用户编号", "tip.order.please.enter.name.mobile": "请输入姓名/手机号", "page.recommend.reply": "回复", "page.recommend.appraise": "评价", "page.recommend.suggest": "建议", "page.recommend.replied": "已回复", "page.recommend.no.reply": "未回复", "page.recommend.taste": "口味", "page.recommend.price": "价格", "page.recommend.service": "服务", "page.recommend.hygienism": "卫生", "page.recommend.evaluation.content": "评价内容", "page.recommend.feedback.content": "反馈内容", "page.recommend.dishes": "菜品", "page.recommend.all": "全部", "page.recommend.dishes.picture": "菜品图片", "page.recommend.reply.content": "回复内容", "page.recommend.reply.pic": "回复图片", "page.recommend.food.storage.code": "存餐码", "page.recommend.number.of.free.cabinets": "空闲柜子数", "page.recommend.confirm.reply": "确认回复", "page.recommend.reply.type": "反馈类型", "page.recommend.view.result": "查看结果", "tip.no.search.content": "没有搜索到相关内容", "page.search.history": "历史记录", "page.search.clear.history": "清除记录", "page.search.no.options.available": "暂无选项", "title.application.approval": "申请审批", "title.smart.warning": "智能预警", "title.visitor.invitation": "访客邀约", "title.food.storage.management": "存餐管理", "title.evaluation.recommendations": "评价建议", "title.message.notification": "消息通知", "title.user.department": "用户部门", "title.department.name": "部门名称", "title.personnel.information": "人员信息", "title.device.management": "设备管理", "title.details": "详情", "title.order.supplementary.deduction": "订单补扣", "title.bright.kitchen.bright.stove": "明厨亮灶", "title.add.dishes": "添加菜品", "title.daily.inspection": "每日巡查", "tip.please.enter.a.keyword.search": "请输入关键字搜索", "page.add.dishes.listing.Menu": "上架到菜谱", "page.add.dishes.btn.add": "添加", "page.add.dishes.btn.add.already": "已添加", "page.user.manager.user.grouping": "用户分组", "tip.loading": "加载中...", "title.search": "搜索", "tip.prompt": "提示", "tip.prompt.card.withdrawal": "注意：该操作需谨慎！退卡后，该卡片将不再与账户关联，该卡将为无主卡。", "tip.prompt.card.freeze.account": "确认用户是否还有待支付/离线订单，冻结后，该用户无法进行消费/充值/取款操作，直到解除冻结状态", "tip.prompt.card.loss.reporting": "确认把当前用户的卡进行挂失吗？挂失后，改卡将无法再进行刷卡消费！直到解除挂失状态！", "tip.prompt.card.cancel.loss": "确认取消当前卡的挂失吗？", "tip.list.empty": "暂无数据", "dic.date.within.three.day": "3天内", "dic.date.within.seven.day": "7天内", "dic.date.within.a.week": "一周内", "dic.date.within.one.week": "一个月内", "dic.date.within.three.week": "三个月内", "dic.date.within.three.month": "最近3个月", "dic.date.within.six.month": "最近6个月", "dic.date.all.time": "全部时间", "dic.date.select.time": "时间筛选", "dic.date.warning.type": "类型", "dic.date.all.type": "全部类型", "dic.type.refund.appeal": "退款申诉", "dic.type.visitor.application": "访客申请", "dic.type.payment.refund": "缴费退款", "dic.type.card.replacement.application": "补卡申请", "dic.type.leave.application": "请假申请", "dic.type.profit.margin.loss": "利润率亏损", "dic.type.profit.margin.earnings": "利润率盈余", "dic.type.proportion.of.raw.materials": "原材料占比", "dic.type.get.materials": "领料出库", "dic.type.allot.out": "调拨出库", "dic.type.wastage": "损耗出库", "dic.type.return.goods": "退货出库", "dic.type.other.out": "其他出库", "dic.type.purchase": "采购入库", "dic.type.allot.in": "调拨入库", "dic.type.other.in": "其他入库", "dic.type.purchase.order": "采购下单", "dic.type.catering.service.license": "餐饮服务许可证", "dic.type.food.business.license": "食品经营许可证", "dic.type.food.production.license": "食品生产许可证", "dic.type.sanitary.permit": "卫生许可证", "dic.type.health.certificate": "健康证", "dic.type.supplier.entry.certificate": "供应商入围合同", "title.picture": "图片", "tip.whether.or.not": "是否", "tip.are.you.sure.you.agree": "确定同意该笔订单的审批吗?", "tip.please.fill.in.the.reason.for.rejection": "请填写拒绝原因", "tip.are.you.sure.you.agree.to.the.leave.application": "确认同意该请假申请吗?", "tip.are.you.sure.you.reject.to.the.leave.application": "确认拒绝该请假申请吗?", "tip.are.you.sure.you.agree.to.the.replacemen.card.application": "确认同意该补卡申请么?", "tip.are.you.sure.you.reject.to.the.replacemen.card.application": "确认拒绝该补卡申请么?", "pages.info.face_photograph": "图片采集", "tip.show.only.last.three.month.order": "只显示前三个月的补扣订单，如果需要查看更多的补扣订单，请登录电脑端查询！", "tip.update.prompt": "更新提示", "tip.new.version.ready": "新版本已经准备好，是否重启应用?", "tip.manager.log.in.pc": "超管人员请在PC端登录?", "tip.success": "成功", "tip.fail": "失败", "tip.page.announce.public.announce": "确定要发布公告吗，发布后不可撤回。", "tip.page.announce.delete.announce": "确定要删除公告吗，删除后不可恢复。", "tip.view.all": "查看全部", "page.login.ver.code.error": "验证码不正确", "page.menu.inventory": "库存", "page.menu.listing.dishes": "上架菜品", "page.menu.listing": "上架", "page.menu.tip.listing.first": "确认菜品上架到", "page.menu.tip.listing.second": "的菜单", "page.menu.dishes": "菜品", "page.menu.tip.warn": "注意：库存无限制请输入'-1'", "tip.page.login.code": "请输入验证码", "page.service.menu.other.function": "其他功能", "page.service.menu.supervise.function": "监管功能", "page.service.menu.smart.warning": "智能预警", "page.service.menu.purchase.sales.inventory": "进销存", "title.inventory.take.delivery": "收货", "page.service.menu.order.input.tip": "输入单据编号查询", "tip.order.please.enter.purchase.name": "输入物资名称查询", "page.inventory.penson.verification.personnel": "核验人员", "page.inventory.penson.verification.add": "添加核验人员", "page.inventory.harvest.live.facial.recognition": "收获实况人脸", "tip.goods.please.enter.purchase.name": "请输入物资名称", "page.inventory.goods.price": "单价", "page.inventory.goods.number": "收货数量", "tip.please.upload.purchase.voucher": "请上传本次采购凭证", "tip.click.upload.image": "点击上传图片（如发票等证明材料）", "page.inventory.voucher.delivery.information": "配送信息", "page.inventory.voucher.delivery.type": "配送方式", "page.inventory.voucher.driver.name": "司机姓名", "page.inventory.voucher.driver.phone": "联系方式", "page.inventory.voucher.driver.certificate": "证件信息", "page.inventory.voucher.delivery.temperature": "配送温度", "page.inventory.voucher.car.information": "车辆信息", "page.inventory.voucher.car.number": "车辆号", "page.inventory.voucher.car.pic": "车辆图片", "tip.please.enter.remark": "请输入备注", "btn.close": "关闭", "page.inventory.voucher.car.type": "车辆类型", "page.inventory.material.warehouse": "物资库", "page.inventory.material.selection": "物资选购", "page.inventory.goods.specifications": "规格", "page.service.menu.accompanying.meal.record": "陪餐记录", "page.service.menu.accompanying.meal.history": "陪餐历史", "page.meal.accompanying.dining.cafeteria": "就餐食堂", "page.meal.take.photo": "拍照"}
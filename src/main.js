import Vue from 'vue'
import App from './App'
// import store from '@/store'
// 引入ui库 2.0
import uView from "uview-ui";

// uni-simple-router 导航守卫
import { router, RouterMount } from './router.js'
import {
  toast,
  showLoading,
  deleteEmptyKey,
  to
} from '@/utils/util'
// 封装跳转
import Message from '@/components/Message/index.js'
import { setNavBarTitle, i18n, $t } from '@/utils/i18n.js'
// 引入过滤器
import * as filter from '@/filter/filter.js'
// 百度统计
import '@/utils/hm_baidu'

/**
 * vuePrototype方法
 */
import vuePrototype from './utils/vuePrototype.js'
/**
 * 路由
 */
import { miRouter } from '@/utils/router_utils'
/**
 * 常量
 */
import common from '@/common/common'
/**
 * 图片路径
 */
import imgBasePath from '@/common/imgBasePath'
/**
 * 内部存储
 */
import store from './store'
/**
 * 弹窗
 */
import CustomDialogComponent from './components/CustomDialogComponent/index';
/**
 * 权限
 */
import { hasPermissionKey } from '@/utils/userUtil'
// #ifdef H5
import VConsole from 'vconsole'
let showVconsole = sessionStorage.getItem('showVconsole') || 0
if (
  location.href.indexOf('debug') > -1 ||
  process.env.NODE_ENV === 'development' ||
  showVconsole === 1
) {
  new VConsole()
}
// #endif
/**
 * 正式环境 清理一下console.log
 */
if (process.env.NODE_ENV === 'production') {
  console.log = () => {}
}
Vue.use(CustomDialogComponent)
Vue.use(uView, {
  i18n: (key, value) => i18n.t(key, value)
});
Vue.use(router)
uni.$u.config.unit = 'rpx'
// vuex
Vue.prototype.$store = store
Vue.prototype.$pmessage = Message
Vue.prototype.$toast = toast // 轻提示
Vue.prototype.$showLoading = showLoading
Vue.prototype.$deleteEmptyKey = deleteEmptyKey
Vue.prototype.$to = to
Vue.prototype.$miRouter = miRouter
Vue.prototype.$setNavBarTitle = setNavBarTitle
Vue.prototype.$common = common
Vue.prototype.$imgPath = imgBasePath
Vue.prototype.$t = $t
Vue.prototype.$hasPermissionKey = hasPermissionKey
// Vue.prototype.cancelAxios = []

// 全局注册过滤器
Object.keys(filter).forEach(key => {
  Vue.filter(key, filter[key])
})

Object.keys(vuePrototype).forEach(k => {
  Vue.prototype[k] = vuePrototype[k]
})

Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App,
  store,
  i18n
})

// v1.3.5起 H5端 你应该去除原有的app.$mount();使用路由自带的渲染方式
// #ifdef H5
RouterMount(app, router, '#app')
// #endif

// #ifndef H5
app.$mount();
// #endif

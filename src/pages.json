{"easycom": {"^u-(.*)": "uview-ui/components/u-$1/u-$1.vue", "^s-(.*)": "@/components/s-$1/s-$1.vue"}, "pages": [{"path": "pages/index/index", "style": {"navigationStyle": "custom", "mp-alipay": {"transparentTitle": "always", "titlePenetrate": "YES", "navigationBarTitleText": "", "defaultTitle": "", "barButtonTheme": "light"}}}, {"path": "pages/login/index", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false, "navigationStyle": "custom", "mp-alipay": {"transparentTitle": "always", "titlePenetrate": "YES", "barButtonTheme": "light"}}}, {"path": "pages/login/login", "style": {"enablePullDownRefresh": false, "navigationStyle": "custom", "mp-alipay": {"transparentTitle": "always", "titlePenetrate": "YES", "navigationBarTitleText": "登录", "barButtonTheme": "light"}}}, {"path": "pages/login/organ", "style": {"navigationBarTitleText": "选择组织", "enablePullDownRefresh": false, "navigationStyle": "custom", "mp-alipay": {"transparentTitle": "none"}}, "meta": {"auth": true}}, {"path": "pages/login/backpass", "style": {"navigationBarTitleText": "找回密码", "enablePullDownRefresh": false, "navigationStyle": "custom", "mp-alipay": {"transparentTitle": "none"}}}, {"path": "pages/login/newpass", "style": {"navigationBarTitleText": "新密码", "navigationStyle": "custom", "mp-alipay": {"transparentTitle": "none"}}}, {"path": "pages/statistics/statistics", "style": {"navigationBarTitleText": "统计", "enablePullDownRefresh": false, "navigationStyle": "custom"}}, {"path": "pages/all_services/all_services", "style": {"navigationBarTitleText": "全部服务", "enablePullDownRefresh": false, "navigationStyle": "custom", "mp-alipay": {"transparentTitle": "none"}}}, {"path": "pages/mine/mine", "style": {"enablePullDownRefresh": false, "navigationStyle": "custom", "mp-alipay": {"transparentTitle": "always", "titlePenetrate": "YES", "navigationBarTitleText": "", "barButtonTheme": "light"}}}, {"path": "pages/ai_assistant/index", "style": {"enablePullDownRefresh": false, "navigationStyle": "custom", "navigationBarTitleText": "AI助手"}, "meta": {"auth": true}}], "tabBar": {"custom": true, "list": [{"pagePath": "pages/index/index", "text": "首页"}, {"pagePath": "pages/ai_assistant/index", "text": "AI助手"}, {"pagePath": "pages/statistics/statistics", "text": "统计"}, {"pagePath": "pages/mine/mine", "text": "我的"}]}, "subPackages": [{"root": "pages_statistics", "pages": [{"path": "business_reports/business", "style": {"navigationBarTitleText": "营业报表", "enablePullDownRefresh": false, "navigationStyle": "custom"}, "meta": {"auth": true}}, {"path": "attendance_overview/attendance_overview", "style": {"navigationBarTitleText": "考勤概况", "enablePullDownRefresh": false, "navigationStyle": "custom"}, "meta": {"auth": true}}, {"path": "revenue/revenue", "style": {"navigationBarTitleText": "经营情况", "enablePullDownRefresh": false, "navigationStyle": "custom"}, "meta": {"auth": true}}, {"path": "sector_consumption/sector_consumption", "style": {"navigationBarTitleText": "部门消费", "enablePullDownRefresh": false, "navigationStyle": "custom"}, "meta": {"auth": true}}, {"path": "device_consumption/device_consumption", "style": {"navigationBarTitleText": "设备消费", "enablePullDownRefresh": false, "navigationStyle": "custom"}, "meta": {"auth": true}}, {"path": "passenger_flow/passenger_flow", "style": {"navigationBarTitleText": "客流概括", "enablePullDownRefresh": false, "navigationStyle": "custom"}, "meta": {"auth": true}}, {"path": "collection_code_daily/collection_code_daily", "style": {"navigationBarTitleText": "收款码日报", "enablePullDownRefresh": false, "navigationStyle": "custom"}, "meta": {"auth": true}}]}, {"root": "pages_common_function", "pages": [{"path": "user/user_manager", "style": {"navigationBarTitleText": "用户管理", "navigationStyle": "custom"}, "meta": {"auth": true}}, {"path": "user/user_department", "style": {"navigationBarTitleText": "用户部门", "navigationStyle": "custom"}, "meta": {"auth": true}}, {"path": "user/department_manager", "style": {"navigationBarTitleText": "", "navigationStyle": "custom", "app-plus": {"bounce": "none"}}, "meta": {"auth": true}}, {"path": "announcement_management/make_new_announcements", "style": {"navigationBarTitleText": "发布新公告", "navigationStyle": "custom", "mp-alipay": {"transparentTitle": "none"}}, "meta": {"auth": true}}, {"path": "announcement_management/announcement_management", "style": {"navigationBarTitleText": "公告管理", "navigationStyle": "custom"}, "meta": {"auth": true}}, {"path": "announcement_management/canteen", "style": {"navigationBarTitleText": "发布新公告", "navigationStyle": "custom", "mp-alipay": {"transparentTitle": "none"}}, "meta": {"auth": true}}, {"path": "user/user_detail", "style": {"navigationBarTitleText": "人员信息", "navigationStyle": "custom"}, "meta": {"auth": true}}, {"path": "device/device_manage", "style": {"navigationBarTitleText": "设备管理", "navigationStyle": "custom", "app-plus": {"bounce": "none"}}, "meta": {"auth": true}}, {"path": "message/message", "style": {"navigationBarTitleText": "消息通知", "navigationStyle": "custom"}, "meta": {"auth": true}}, {"path": "message/message_detail", "style": {"navigationBarTitleText": "消息详情", "navigationStyle": "custom"}, "meta": {"auth": true}}, {"path": "approval/approval", "style": {"navigationBarTitleText": "申请审批", "navigationStyle": "custom", "app-plus": {"bounce": "none"}}, "meta": {"auth": true}}, {"path": "approval/approval_detail", "style": {"navigationBarTitleText": "详情", "navigationStyle": "custom"}, "meta": {"auth": true}}, {"path": "invitation/invitation", "style": {"navigationBarTitleText": "访客邀约", "navigationStyle": "custom"}, "meta": {"auth": true}}, {"path": "user/face_photo_graph", "style": {"navigationBarTitleText": ""}, "meta": {"auth": false}}, {"path": "user/face_photograph_result", "style": {"navigationBarTitleText": ""}, "meta": {"auth": false}}]}, {"root": "pages_dining_room", "pages": [{"path": "menu/menu_manage", "style": {"navigationStyle": "custom", "mp-alipay": {"transparentTitle": "always", "titlePenetrate": "YES", "navigationBarTitleText": "", "barButtonTheme": "light"}}, "meta": {"auth": true}}, {"path": "menu/add_menu", "style": {"navigationStyle": "custom", "mp-alipay": {"transparentTitle": "none", "navigationBarTitleText": "添加菜品"}}, "meta": {"auth": true}}, {"path": "food/food_storage_manage", "style": {"navigationBarTitleText": "存餐管理", "navigationStyle": "custom"}, "meta": {"auth": true}}, {"path": "recommend/recommend", "style": {"navigationBarTitleText": "评价建议", "navigationStyle": "custom"}, "meta": {"auth": true}}, {"path": "recommend/recommend_detail", "style": {"navigationBarTitleText": "详情", "navigationStyle": "custom"}, "meta": {"auth": true}}, {"path": "recommend/recommend_edit", "style": {"navigationBarTitleText": "回复", "navigationStyle": "custom"}, "meta": {"auth": true}}, {"path": "order/order_deduction", "style": {"navigationBarTitleText": "订单补扣", "navigationStyle": "custom", "mp-alipay": {"transparentTitle": "none"}}, "meta": {"auth": true}}, {"path": "dailyInspection/detail", "style": {"navigationBarTitleText": "添加巡查记录", "navigationStyle": "custom"}, "meta": {"auth": true}}, {"path": "dailyInspection/index", "style": {"navigationBarTitleText": "每日巡查", "navigationStyle": "custom"}, "meta": {"auth": true}}]}, {"root": "pages_others", "pages": [{"path": "inventory/purchase_inventory", "style": {"navigationStyle": "custom", "mp-alipay": {"transparentTitle": "always", "titlePenetrate": "YES", "navigationBarTitleText": "", "barButtonTheme": "light"}}, "meta": {"auth": true}}, {"path": "inventory/purchase_inventory_order", "style": {"navigationStyle": "custom", "mp-alipay": {"transparentTitle": "always", "titlePenetrate": "YES", "navigationBarTitleText": "", "barButtonTheme": "light"}}, "meta": {"auth": true}}, {"path": "inventory/purchase_inventory_detail", "style": {"navigationStyle": "custom", "mp-alipay": {"transparentTitle": "always", "titlePenetrate": "YES", "navigationBarTitleText": "", "barButtonTheme": "light"}}, "meta": {"auth": true}}, {"path": "inventory/purchase_inventory_voucher", "style": {"navigationStyle": "custom", "mp-alipay": {"transparentTitle": "always", "titlePenetrate": "YES", "navigationBarTitleText": "", "barButtonTheme": "light"}}, "meta": {"auth": true}}, {"path": "inventory/purchase_inventory_goods", "style": {"navigationStyle": "custom", "mp-alipay": {"transparentTitle": "always", "titlePenetrate": "YES", "navigationBarTitleText": "", "barButtonTheme": "light"}}, "meta": {"auth": true}}, {"path": "inventory/purchase_inventory_camera", "style": {"navigationStyle": "custom", "mp-alipay": {"transparentTitle": "always", "titlePenetrate": "YES", "navigationBarTitleText": "", "barButtonTheme": "light"}}, "meta": {"auth": true}}, {"path": "inventory/purchase_inventory_camera_confirm", "style": {"navigationStyle": "custom", "mp-alipay": {"transparentTitle": "always", "titlePenetrate": "YES", "navigationBarTitleText": "", "barButtonTheme": "light"}}, "meta": {"auth": true}}, {"path": "inventory/purchase_inventory_person", "style": {"navigationStyle": "custom", "mp-alipay": {"transparentTitle": "always", "titlePenetrate": "YES", "navigationBarTitleText": "", "barButtonTheme": "light"}}, "meta": {"auth": true}}, {"path": "warehouse/material_warehouse", "style": {"navigationStyle": "custom", "mp-alipay": {"transparentTitle": "always", "titlePenetrate": "YES", "navigationBarTitleText": "", "barButtonTheme": "light"}}, "meta": {"auth": true}}, {"path": "warehouse/web", "style": {"navigationStyle": "custom", "mp-alipay": {"transparentTitle": "always", "titlePenetrate": "YES", "navigationBarTitleText": "", "barButtonTheme": "light"}}, "meta": {"auth": true}}, {"path": "inventory/purchase_inventory_specifications", "style": {"navigationStyle": "custom", "mp-alipay": {"transparentTitle": "always", "titlePenetrate": "YES", "navigationBarTitleText": "", "barButtonTheme": "light"}}, "meta": {"auth": true}}, {"path": "meal/accompanying_meal_record", "style": {"navigationStyle": "custom", "mp-alipay": {"transparentTitle": "always", "titlePenetrate": "YES", "navigationBarTitleText": "", "barButtonTheme": "light"}}, "meta": {"auth": true}}, {"path": "meal/accompanying_meal_detail", "style": {"navigationStyle": "custom", "mp-alipay": {"transparentTitle": "always", "titlePenetrate": "YES", "navigationBarTitleText": "", "barButtonTheme": "light"}}, "meta": {"auth": true}}, {"path": "meal/sign", "style": {"navigationStyle": "custom", "mp-alipay": {"transparentTitle": "always", "titlePenetrate": "YES", "navigationBarTitleText": "", "barButtonTheme": "light"}}, "meta": {"auth": true}}, {"path": "meal/accompanying_photo", "style": {"navigationStyle": "custom", "mp-alipay": {"transparentTitle": "always", "titlePenetrate": "YES", "navigationBarTitleText": "", "barButtonTheme": "light"}}, "meta": {"auth": true}}]}, {"root": "pages_supervise", "pages": [{"path": "smart_warning/smart_warning", "style": {"navigationStyle": "custom", "mp-alipay": {"transparentTitle": "always", "titlePenetrate": "YES", "navigationBarTitleText": "", "barButtonTheme": "light"}}, "meta": {"auth": true}}]}], "globalStyle": {"backgroundColor": "#F8F8F8", "backgroundColorTop": "#FFFFFF", "mp-weixin": {"navigationBarBackgroundColor": "#F8F8F8", "navigationBarTextStyle": "black"}, "mp-alipay": {"titleBarColor": "#FFFFFF", "allowsBounceVertical": "NO"}, "usingComponents": {}}, "condition": {"current": 0, "list": [{"name": "", "path": "", "query": ""}]}}
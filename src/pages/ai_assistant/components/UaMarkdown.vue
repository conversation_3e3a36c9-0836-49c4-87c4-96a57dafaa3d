<!-- uniapp vue2 markdown解析 -->
<template>
  <view class="ua__markdown">
    <u-parse class="markdown_body" :content="parseNodes(source)" :tagStyle="style" />
    <view v-if="commandInfo.type === 'jump_path'" class="jump_box" @click="onJump">
      <text>{{ commandInfo.title }}</text>
      <img
        style="width: 10rpx; height: 20rpx; margin-left: 10rpx"
        src="/static/image/ai_assistant/ai_arrow.png"
        mode="aspectFill"
      />
    </view>
  </view>
</template>

<script>
import MarkdownIt from '@/uni_modules/markdown/lib/markdown-it.min.js'
import { mapGetters } from 'vuex'
export default {
  name: 'UaMarkdown',
  props: {
    source: String,
    showLine: { type: [<PERSON><PERSON><PERSON>, String], default: true }
  },
  computed: {
    ...mapGetters(['color'])
  },
  data() {
    return {
      markdown: null,
      copyCodeData: [],
      style: {
        h1: 'font-size: 42rpx;padding-top:24rpx;margin-bottom:24rpx',
        h2: 'font-size: 38rpx;padding-top:24rpx;margin-bottom:24rpx',
        h3: 'font-size: 34rpx;padding-top:24rpx;margin-bottom:24rpx',
        h4: 'font-size: 30rpx;padding-top:24rpx;margin-bottom:24rpx',
        h5: 'font-size: 26rpx;padding-top:24rpx;margin-bottom:24rpx',
        strong: 'font-size:30rpx;font-weight: 700;'
      },
      // 之后扩展 cmd_key 实现不同前端交互效果
      commandInfo: {
        cmd_key: '',
        title: '',
        path: '/'
      },
      lastParsedSource: '',
      lastParsedHtml: ''
    }
  },
  mounted() {
    this.markdown = MarkdownIt({
      html: true,
      typographer: true, // 启用智能标点
      linkify: true // 自动识别链接
    })
  },
  methods: {
    onJump() {
      this.$miRouter.push({
        path: this.commandInfo.path
      })
    },
    parseNodes(value) {
      if (!value) return ''
      if (!this.markdown) return ''

      if (value === this.lastParsedSource) {
        return this.lastParsedHtml
      }

      value = value.replace(/<br>|<br\/>|<br \/>/g, '\n')
      value = value.replace(/&nbsp;/g, ' ')
      let htmlString = ''
      if (value.split('```').length % 2) {
        let mdtext = value
        if (mdtext[mdtext.length - 1] !== '\n') {
          mdtext += '\n'
        }
        htmlString = this.markdown.render(mdtext)
      } else {
        htmlString = this.markdown.render(value)
      }
      // 解决小程序表格边框型失效问题
      htmlString = htmlString.replace(/<table/g, `<table class="table"`)
      htmlString = htmlString.replace(/<tr/g, `<tr class="tr"`)
      htmlString = htmlString.replace(/<th>/g, `<th class="th">`)
      htmlString = htmlString.replace(/<td/g, `<td class="td"`)
      htmlString = htmlString.replace(/<hr>|<hr\/>|<hr \/>/g, `<hr class="hr">`)

      // 解析 command 标签
      const regex = /<command(?:\s+[^>]*)?>([\s\S]*?)<\/command>/g
      let match

      while ((match = regex.exec(htmlString)) !== null) {
        // 修复 JSON 格式
        const fixedJson = match[1]
          .replace(/“/g, '"') // 替换中文引号 “
          .replace(/”/g, '"') // 替换中文引号 ”

        try {
          const newCommandInfo = JSON.parse(fixedJson.trim()) || {}
          // 只要最后一个common标签
          if (JSON.stringify(newCommandInfo) !== JSON.stringify(this.commandInfo)) {
            this.commandInfo = newCommandInfo
          }
        } catch (error) {
          console.error('JSON parsing error:', error)
        }
      }
      htmlString = htmlString.replace(regex, '')

      console.log('htmlString----', htmlString)

      this.lastParsedSource = value
      this.lastParsedHtml = htmlString
      return htmlString
    }
  }
}
</script>

<style lang="scss" scoped>
.jump_box {
  display: flex;
  width: 100%;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  margin: 10rpx 0;
  text {
    color: #fd953c;
    font-size: 28rpx;
  }
}

.markdown_body {
  background: #fff;
  font-size: 32rpx;
  padding: 20rpx 24rpx;
  border-radius: 16rpx 16rpx 16rpx 0;
  display: inline-block;
  max-width: 100%;
  word-break: break-word;
}
</style>

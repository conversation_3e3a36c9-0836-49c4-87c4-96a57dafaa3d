<template>
  <view class="allservices">
    <!-- 导航栏 -->
    <!-- #ifdef H5 -->
    <u-navbar
      :title="$t('services.Allservices')"
      safeAreaInsetTop
      :leftIconColor="color.navigation"
      placeholder
      :autoBack="false"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    >
      <view class="u-nav-left" slot="left" v-if="edit" @click="goBackOneLevel">
        <u-icon name="arrow-left"></u-icon>
      </view>
      <view class="u-nav-slot" slot="right" @click="clickEdit" v-if="edit">
        {{ $t('services.edit') }}
      </view>
      <view class="u-nav-cancel" slot="left" @click="cancel" v-if="!edit">
        {{ $t('services.Cancel') }}
      </view>
      <view class="u-nav-save" slot="right" @click="clickSave" v-if="!edit">
        {{ $t('services.Save') }}
      </view>
    </u-navbar>
    <!-- #endif -->
    <!--小程序的保存编辑按钮等放标题栏下面 -->
    <!-- #ifdef MP-WEIXIN || MP-ALIPAY -->
    <!--#ifdef MP-WEIXIN-->
    <u-navbar
      :title="$t('services.Allservices')"
      safeAreaInsetTop
      :leftIconColor="color.navigation"
      placeholder
      :autoBack="false"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    >
      <view class="u-nav-left" slot="left" @click="goBackOneLevel">
        <u-icon name="arrow-left"></u-icon>
      </view>
    </u-navbar>
    <!--#endif-->
    <view class="allservices_nav_title">
      <view class="allservices_nav_title_btn_cancel" @click="cancel" v-show="!edit">
        {{ $t('services.Cancel') }}
      </view>
      <view class="allservices_nav_title_right_btn">
        <view class="allservices_nav_title_btn_edit" @click="clickEdit" v-show="edit">
          {{ $t('services.edit') }}
        </view>
        <view class="allservices_nav_title_btn_save" @click="clickSave" v-show="!edit">
          {{ $t('services.Save') }}
        </view>
      </view>
    </view>
    <!-- #endif -->
    <!-- 首页应用 -->
    <view class="home_app">
      <view class="text">{{ $t('services.Homeapp') }}</view>
      <view class="app_ul">
        <view class="app_li" v-for="item in newHomeerrList" :key="item.id" @click.stop="goPageJumpHomeerr(item.id)">
          <view>
            <image :src="item.image" mode="scaleToFill"></image>
            <image
              :src="item.icon"
              v-if="!edit"
              @click.stop="removeApp(item.id)"
              mode="scaleToFill"
            ></image>
          </view>
          <text>{{ item.text }}</text>
        </view>
      </view>
    </view>
    <!-- 通用功能 -->
    <view class="common-functionality">
      <view class="text">{{ $t('services.Commonfunctionality') }}</view>
      <view class="app_ul">
        <view class="app_li"
          v-for="item in newCommonFunctionAlityList"
          :key="item.id"
          @click.stop="goPageJumpGeneral(item.id)"
        >
          <view>
            <image :src="item.image" mode="scaleToFill"></image>
            <image
              :src="item.icon"
              v-if="!edit && item.judgment"
              @click.stop="addGeneral(item.id)"
              mode="scaleToFill"
            ></image>
          </view>
          <text>{{ item.text }}</text>
        </view>
      </view>
    </view>
    <!-- 食堂功能 -->
    <view class="canteen-function">
      <view class="text">{{ $t('services.Canteenfunction') }}</view>
      <view class="app_ul">
        <view class="app_li"
          v-for="item in newCanteenFunctionList"
          :key="item.id"
          @click.stop="goPageJumpCanteen(item.id)"
        >
          <view>
            <image :src="item.image" mode="scaleToFill"></image>
            <image
              :src="item.icon"
              v-if="!edit && item.judgment"
              @click.stop="addCafeteria(item.id)"
              mode="scaleToFill"
            ></image>
          </view>
          <text>{{ item.text }}</text>
        </view>
      </view>
    </view>
    <!-- 其他功能 -->
    <view class="other-function">
      <view class="text">{{ $t('page.service.menu.other.function') }}</view>
      <view class="app_ul">
        <view class="app_li"
          v-for="item in newOtherFunctionList"
          :key="item.id"
          @click.stop="goPageJumpOther(item.id)"
        >
          <view>
            <image :src="item.image" mode="scaleToFill"></image>
            <image
              :src="item.icon"
              v-if="!edit && item.judgment"
              @click.stop="addOther(item.id)"
              mode="scaleToFill"
            ></image>
          </view>
          <text>{{ item.text }}</text>
        </view>
      </view>
    </view>
    <!-- 监管功能 -->
    <view class="supervise-function">
      <view class="text">{{ $t('page.service.menu.supervise.function') }}</view>
      <view class="app_ul">
        <view class="app_li"
          v-for="item in newSuperviseFunctionList"
          :key="item.id"
          @click.stop="goPageJumpSupervise(item.id)"
        >
          <view>
            <image :src="item.image" mode="scaleToFill"></image>
            <image
              :src="item.icon"
              v-if="!edit && item.judgment"
              @click.stop="addSupervise(item.id)"
              mode="scaleToFill"
            ></image>
          </view>
          <text>{{ item.text }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
import comDic from '@/common/comDic'
// import imgPath from '@/common/imgBasePath'
import cache from '@/utils/cache'
import common from '@/common/common'
import { getTabbarStatistics, addHomeAppListData } from '@/api/index.js'
import { showMsgDeveloping } from '@/utils/userUtil'
import { deepClone } from '@/utils/util'

export default {
  computed: {
    ...mapGetters(['img', 'color'])
  },
  data() {
    return {
      edit: true, // 点击编辑切换开关
      /**
       * 首页应用列表
       */
      homeerrList: deepClone(comDic.HOME_DATA_LIST),
      newHomeerrList: [],
      /**
       * 通用功能
       */
      commonFunctionAlityList: deepClone(comDic.GENERAL_DATA_LIST),
      newCommonFunctionAlityList: [],
      /**
       * 食堂功能
       */
      canteenFunctionList: deepClone(comDic.CANTEEN_DATA_LIST),
      /**
       * 其他功能
       */
      otherFunctionList: deepClone(comDic.OTHERS_DATA_LIST),
      superviseFunctionList: deepClone(comDic.SUPERVISE_DATA_LIST),
      newCanteenFunctionList: [],
      newOtherFunctionList: [], // 新的食堂功能列表
      newSuperviseFunctionList: []
    }
  },
  mounted() {
    this.getTabbarStatisticsList()
  },
  methods: {
    /**
     * 点击导航栏返回上一级
     */
    goBackOneLevel() {
      uni.navigateBack({})
    },
    /**
     * 点击导航栏取消
     */
    cancel() {
      this.edit = true
    },
    /**
     * 点击导航栏编辑保存
     */
    clickSave() {
      this.edit = true
      let userInfo = cache.get(common.KEY_USER_INFO)
      let homeappdata = []
      this.newHomeerrList.forEach(item => {
        homeappdata.push(item.key)
      })
      let params = { id: userInfo.role_id, select_permission: homeappdata }
      console.log(params.select_permission)
      addHomeAppListData(params)
        .then(res => {
          if (res.code === 0) {
            console.log(res)
            uni.$emit(this.$common.MSG_UPDATE_MENU_SUCCESS, true)
          } else {
            uni.$u.toast(res.msg)
            console.log(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
          console.log(err)
        })
    },
    /**
     * 点击导航栏编辑
     */
    clickEdit() {
      this.edit = false
      // 如果首页应用列表为空，下面两栏显示添加 有的不显示

      if (this.newHomeerrList.length > 0) {
        // console.log(this.newHomeerrList)
        this.newHomeerrList.forEach(item => {
          var index = this.newCommonFunctionAlityList.findIndex(items => {
            return item.id === items.id
          })
          // console.log(index)
          if (index >= 0) {
            this.newCommonFunctionAlityList[index].judgment = false
          }
        })
        this.newHomeerrList.forEach(item => {
          var indexs = this.newCanteenFunctionList.findIndex(items => {
            return item.id === items.id
          })
          if (indexs >= 0) {
            this.newCanteenFunctionList[indexs].judgment = false
          }
        })
        this.newHomeerrList.forEach(item => {
          var indexss = this.newOtherFunctionList.findIndex(items => {
            return item.id === items.id
          })
          if (indexss >= 0) {
            this.newOtherFunctionList[indexss].judgment = false
          }
        })
        this.newHomeerrList.forEach(item => {
          var indexss = this.newSuperviseFunctionList.findIndex(items => {
            return item.id === items.id
          })
          if (indexss >= 0) {
            this.newSuperviseFunctionList[indexss].judgment = false
          }
        })
      } else {
        this.newCommonFunctionAlityList.forEach(item => {
          item.judgment = true
        })
        this.newCanteenFunctionList.forEach(item => {
          item.judgment = true
        })
        this.newOtherFunctionList.forEach(item => {
          item.judgment = true
        })
        this.newSuperviseFunctionList.forEach(item => {
          item.judgment = true
        })
      }
    },
    /**
     * 根据权限获取应用列表
     */
    getTabbarStatisticsList() {
      let userInfo = cache.get(common.KEY_USER_INFO)
      let params = { id: userInfo.role_id }
      getTabbarStatistics(params)
        .then(res => {
          //  console.log(res)
          if (res.code === 0) {
            if (res.data.merchant_app_permission_keys.length > 1) {
              // 返回的权限列表不为空
              this.commonFunctionAlityList.forEach((item, index) => {
                let add = res.data.merchant_app_permission_keys.indexOf(item.key)
                if (add > 0) {
                  this.newCommonFunctionAlityList.push(item)
                }
              })
              this.canteenFunctionList.forEach((item, index) => {
                let add = res.data.merchant_app_permission_keys.indexOf(item.key)
                if (add > 0) {
                  this.newCanteenFunctionList.push(item)
                }
              })
              this.otherFunctionList.forEach((item, index) => {
                let add = res.data.merchant_app_permission_keys.indexOf(item.key)
                if (add > 0) {
                  this.newOtherFunctionList.push(item)
                }
              })
              this.superviseFunctionList.forEach((item, index) => {
                let add = res.data.merchant_app_permission_keys.indexOf(item.key)
                if (add > 0) {
                  this.newSuperviseFunctionList.push(item)
                }
              })
            }
            if (res.data.merchant_app_select_permission_keys.length > 0) {
              // 返回的权限列表不为空
              // console.log(res.data.merchant_app_select_permission_keys)
              this.homeerrList.forEach((item, index) => {
                let add = res.data.merchant_app_select_permission_keys.indexOf(item.key)
                if (add >= 0) {
                  this.newHomeerrList.push(item)
                }
              })
            } else {
              this.newHomeerrList = []
            }
          } else {
            uni.$u.toast(res.msg)
            // console.log(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
          // console.log(err)
        })
    },
    /**
     * 点击添加首页应用
     * 在通用功能和食堂功能查找点击的那一项改变图片和judgment值如果首页应用小于8个的话就添加进去
     */
    addGeneral(id) {
      let addone = this.newCommonFunctionAlityList.filter(item => {
        return item.id === id
      })
      // console.log(addone)
      if (addone && addone.length > 0) {
        if (this.newHomeerrList.length < 8) {
          addone[0].judgment = false
          addone[0].icon = this.$imgPath.IMG_REMOVE_GRAY
          this.newHomeerrList.push(addone[0])
        }
      }
    },
    addCafeteria(id) {
      let addtwo = this.newCanteenFunctionList.filter(item => {
        return item.id === id
      })
      if (addtwo && addtwo.length > 0) {
        if (this.newHomeerrList.length < 8) {
          addtwo[0].judgment = false
          addtwo[0].icon = this.$imgPath.IMG_REMOVE_GRAY
          this.newHomeerrList.push(addtwo[0])
        }
      }
    },
    // 添加其他功能
    addOther(id) {
      let addthree = this.newOtherFunctionList.filter(item => {
        return item.id === id
      })
      if (addthree && addthree.length > 0) {
        if (this.newHomeerrList.length < 8) {
          addthree[0].judgment = false
          addthree[0].icon = this.$imgPath.IMG_REMOVE_GRAY
          this.newHomeerrList.push(addthree[0])
        }
      }
    },
    // 添加监管功能
    addSupervise(id) {
      let addthree = this.newSuperviseFunctionList.filter(item => {
        return item.id === id
      })
      if (addthree && addthree.length > 0) {
        if (this.newHomeerrList.length < 8) {
          addthree[0].judgment = false
          addthree[0].icon = this.$imgPath.IMG_REMOVE_GRAY
          this.newHomeerrList.push(addthree[0])
        }
      }
    },
    /**
     * 点击去除首页应用
     */
    removeApp(id) {
      if (this.newHomeerrList.length > 0) {
        var index = this.newHomeerrList.findIndex(item => {
          return item.id === id
        })
        this.newHomeerrList[index].icon = this.$imgPath.IMG_ADD_ORANGE
        this.newHomeerrList[index].judgment = true
        // 根据索引删除数据
        var arr = this.newHomeerrList.splice(index, 1)
        // 通用功能和食堂功能对比首页列表去除的哪一项 首页列表没有的  judgment改为true
        var one = this.newCommonFunctionAlityList.findIndex(role => role.id === arr[0].id)
        if (one !== -1) {
          this.newCommonFunctionAlityList[one].judgment = true
        }
        var two = this.newCanteenFunctionList.findIndex(role => role.id === arr[0].id)
        if (two !== -1) {
          this.newCanteenFunctionList[two].judgment = true
        }
        var three = this.newOtherFunctionList.findIndex(role => role.id === arr[0].id)
        if (three !== -1) {
          this.newOtherFunctionList[three].judgment = true
        }
        var four = this.newSuperviseFunctionList.findIndex(role => role.id === arr[0].id)
        if (four !== -1) {
          this.newSuperviseFunctionList[four].judgment = true
        }
      }
    },
    /**
     *编辑状态下不允许页面跳转
     * @param {*} id
     */
    goPageJumpGeneral(id) {
      if (this.edit) {
        let index = this.newCommonFunctionAlityList.findIndex(item => {
          return item.id === id
        })
        // 拦截开发中项目
        if (this.showDeveloping(this.newCommonFunctionAlityList[index].key)) {
          return
        }
        if (this.newCommonFunctionAlityList[index].jumpPath) {
          this.$miRouter.push({ path: this.newCommonFunctionAlityList[index].jumpPath })
        }
      }
    },
    goPageJumpCanteen(id) {
      if (this.edit) {
        let index = this.newCanteenFunctionList.findIndex(item => {
          return item.id === id
        })
        // 拦截开发中项目
        if (this.showDeveloping(this.newCanteenFunctionList[index].key)) {
          return
        }
        if (this.newCanteenFunctionList[index].jumpPath) {
          this.$miRouter.push({ path: this.newCanteenFunctionList[index].jumpPath })
        }
      }
    },
    // 跳转其他
    goPageJumpOther(id) {
      if (this.edit) {
        let index = this.newOtherFunctionList.findIndex(item => {
          return item.id === id
        })
        // 拦截开发中项目
        if (this.showDeveloping(this.newOtherFunctionList[index].key)) {
          return
        }
        if (this.newOtherFunctionList[index].jumpPath) {
          this.$miRouter.push({ path: this.newOtherFunctionList[index].jumpPath })
        }
      }
    },
    // 跳转监管功能
    goPageJumpSupervise(id) {
      if (this.edit) {
        let index = this.newSuperviseFunctionList.findIndex(item => {
          return item.id === id
        })
        // 拦截开发中项目
        if (this.showDeveloping(this.newSuperviseFunctionList[index].key)) {
          return
        }
        if (this.newSuperviseFunctionList[index].jumpPath) {
          this.$miRouter.push({ path: this.newSuperviseFunctionList[index].jumpPath })
        }
      }
    },
    /**
     * 头部图标跳转
     * @param {*} id
     */
    goPageJumpHomeerr(id) {
      if (this.edit) {
        let index = this.newHomeerrList.findIndex(item => {
          return item.id === id
        })
        // 拦截开发中项目
        if (this.showDeveloping(this.newHomeerrList[index].key)) {
          return
        }
        if (this.newHomeerrList[index].jumpPath) {
          this.$miRouter.push({ path: this.newHomeerrList[index].jumpPath })
        }
      }
    },
    /**
     * 显示开发中弹窗
     * @param {*} key
     */
    showDeveloping(key) {
      var developFunctionTxt = "mingchuliangzao attendance_dverview visitor_invitation_management   messages_management passenger_flow"
      if (developFunctionTxt.indexOf(key) !== -1) {
        showMsgDeveloping()
        return true
      }
      return false
    }
  }
}
</script>

<style lang="scss" scoped>
@mixin topButtomStyle {
  color: $but;
  font-size: 28rpx;
}
@mixin saveBtnStyle {
  width: 114rpx;
  height: 48rpx;
  background-color: $but;
  color: #fff;
  border-radius: 24rpx;
  font-size: 24rpx;
  text-align: center;
  line-height: 48rpx;
}

.allservices {
  padding-bottom: 20px;
  .u-nav-slot {
    @include topButtomStyle;
  }
  .u-nav-cancel {
    @include topButtomStyle;
  }
  .u-nav-save {
    @include saveBtnStyle;
  }
  .allservices_nav_title {
    display: flex;
    justify-content: space-between;
    padding: 30rpx 30rpx 0;
    font-size: 28rpx;
    .allservices_nav_title_btn_cancel {
      @include topButtomStyle;
      width: 100%;
      text-align: left;
    }
    .allservices_nav_title_right_btn {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      .allservices_nav_title_btn_edit {
        @include topButtomStyle;
        text-align: right;
      }
      .allservices_nav_title_btn_save {
        @include saveBtnStyle;
      }
    }
  }
  .home_app,
  .common-functionality,
  .other-function,
  .supervise-function,
  .canteen-function {
    width: 670rpx;
    background-color: #ffffff;
    border-radius: 20rpx;
    margin: 40rpx auto;
    overflow: hidden;
    .text {
      margin: 30rpx;
      font-size: 30rpx;
      color: #1d1e20;
    }
    .app_ul {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-items: center;
      align-content: space-between;
      .app_li {
        width: 25%;
        height: 170rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-evenly;
        view {
          position: relative;
          image:nth-child(1) {
            width: 91rpx;
            height: 92rpx;
          }
          image:nth-child(2) {
            position: absolute;
            top: 0;
            right: 0;
            width: 28rpx;
            height: 28rpx;
            background-color: #fff;
            border-radius: 50%;
          }
        }
        text {
          font-size: 24rpx;
        }
      }
    }
  }
}
</style>

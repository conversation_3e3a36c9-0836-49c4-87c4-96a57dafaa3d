<template>
  <view class="home_wrap">
    <view class="home_header">
      <u-navbar
        :title="$t('tabbar.indexer')"
        placeholder
        :fixed="false"
        :bg-color="bgColor"
        :title-style="{ color: '#fff', fontSize: '37rpx' }"
      >
        <!--设置自定义左侧就能将左侧箭头隐藏掉 -->
        <view class="u-nav-slot" slot="left"></view>
      </u-navbar>
      <!-- 信息 切换账号 -->
      <view class="home_header_Information">
        <image class="home_header_Information_img" :src="imgPath.IMG_AGENT_03" mode="scaleToFill" />
        <view class="home_header_Information_organ">
          <view class="t1 h-80">{{ userorgan }}</view>
          <!-- <view class="t2">{{ $t('index.role') }}:{{ Information.role_name }}</view> -->
        </view>
        <view class="home_header_seemore" @click="selectOrganization">
          {{ $t('index.accounts') }}
        </view>
      </view>
      <!-- 营业数据 -->
      <view class="home_banner" v-if="isShowBusiness">
        <view class="home_banner_text">
          <view class="t1">{{ $t('index.siness') }}</view>
          <view
            class="t2"
            @click="$miRouter.push({ path: '/pages_statistics/business_reports/business' })"
          >
            {{ $t('index.more') }} >
          </view>
        </view>
        <view class="home_banner_value">
          <view v-for="(item, index) in businessSummery" class="home_banner_value_li" :key="index">
            <view class="t1">{{ item.value }}</view>
            <view class="t2">{{ item.label }}</view>
          </view>
        </view>
      </view>
    </view>
    <image class="home_head_img" :src="imgPath.IMG_INDEX_BG_ORANGE" mode="scaleToFill" />
    <!-- 营业额走势 -->
    <view class="home_content_chart_wrap" v-if="isShowRevenue">
      <view class="home_banner_text">
        <view class="t1">{{ $t('index.trends') }}</view>
        <view class="t2" @click="$miRouter.push({ path: '/pages_statistics/revenue/revenue' })">
          {{ $t('index.more') }} >
        </view>
      </view>
      <view class="home_content_chart">
        <canvas
          canvas-id="canvasLine"
          id="canvasLine"
          class="charts"
          @touchstart="chartItemClick"
        />
      </view>
    </view>
    <!-- 常用功能   -->
    <view class="home_content_function">
      <view class="home_content_function_title">
        <view class="t1">{{ $t('index.frequently') }}</view>
        <view class="t2" @click="$miRouter.push({ path: '/pages/all_services/all_services' })">
          {{ $t('index.more') }}>
        </view>
      </view>
      <view class="home_content_function_list">
        <view v-if="seiqtype" class="home_content_function_center">
          <view
            class="li"
            v-for="item in newHomeerrList"
            :key="item.id"
            @click="jumpToPage(item)"
          >
            <image :src="item.image" mode="scaleToFill" />
            <view>{{ item.text }}</view>
          </view>
        </view>
        <view v-else class="home_content_function_center">
          <view class="home_content_function_null">{{ $t('index.commonly') }}</view>
          <view
            class="home_content_function_add"
            @click="$miRouter.push({ path: '/pages/all_services/all_services' })"
          >
            +{{ $t('index.edit') }}
          </view>
        </view>
      </view>
    </view>

    <view class="home_footer" v-if="($hasPermissionKey('message_notification')&&this.unReadMsgNum>0)||($hasPermissionKey('apply_approval')&&this.unReadOrderNum>0)">
      <text class="home_footer_title">{{ $t('index.todo') }}</text>
      <view class="home_footer_first" v-if="$hasPermissionKey('message_notification')&&this.unReadMsgNum>0">
        <image :src="imgPath.IMG_AGENT_01" mode="scaleToFill" />
        <view class="home_footer_text">
          <view>你有</view>
          <view>{{ unReadMsgNum }}</view>
          <view>个待阅读信息需查看</view>
        </view>
        <view class="home_footer_jump_layout">
          <view class="home_footer_jump" @click="handlerMessageClick(1)">
            {{ $t('index.dispose') }}
          </view>
        </view>
      </view>
      <view class="home_footer_second" v-if="$hasPermissionKey('apply_approval')&&this.unReadOrderNum>0">
        <image :src="imgPath.IMG_AGENT_02" mode="scaleToFill" />
        <view class="home_footer_text">
          <view>你有</view>
          <view>{{unReadOrderNum}}</view>
          <view>个待审批事项需处理</view>
        </view>
        <view class="home_footer_jump_layout">
          <view class="home_footer_jump" @click="handlerMessageClick(2)">
            {{ $t('index.dispose') }}
          </view>
        </view>
      </view>
    </view>
    <tabbar :current-page="0" />
  </view>
</template>

<script>
import tabbar from '@/custom-tab-bar/index.vue'
import { mapGetters } from 'vuex'
import comDic from '@/common/comDic'
import cache from '@/utils/cache'
import common from '@/common/common'
import { numberToThousands, divide } from '@/utils/util'
import { getTabbarStatistics } from '@/api/index.js'
import { apiBackgroundMessagesMessagesGetMsgNum } from '@/api/message'
import { apiBusinessReportsList, apiBusinessSituationList } from '@/api/report.js'
import { apiMerchantMobileApprovalGetApprovalCount } from '@/api/order.js'
import UCharts from '@/utils/charts/u-charts.js';
import { showMsgDeveloping } from '@/utils/userUtil'
import { checkUpdate } from '@/utils/update'
var uChartsInstance = {}; // 放置uChart实体
export default {
  computed: {
    ...mapGetters(['color', 'img', 'orgs'])
  },
  components: {
    tabbar
  },
  data() {
    return {
      homeerrList: comDic.HOME_DATA_LIST,
      newHomeerrList: [],
      org_ids: cache.get(this.$common.KEY_USER_ORGAN), // 所选组织
      organ: '',
      date_type: '1', // 时间类型
      selectedTime: 'day', // 所选时间段
      imgPath: this.$imgPath,
      chartValue: comDic.BUSINESS_TRENDS_DATA_LISTS,
      businessSummery: comDic.BUSINESS_DATA_LISTS,
      mealSegments: [], // 表单餐段
      mealSegmentsData: [], // 表单餐段数据
      Information: {
        name: ''
      },
      queryDateList: [
        this.$t('Meal.Breakfast'),
        this.$t('Meal.lunch'),
        this.$t('Meal.tea'),
        this.$t('Meal.dinner'),
        this.$t('Meal.supper'),
        this.$t('Meal.early')
      ], // 时间列表
      startDate: NaN, // 开始时间
      endDate: NaN, // 结束时间
      userorgan: '',
      seiqtype: false, // 常用功能开关 数据为空false
      bgColor: 'rgba(0, 0, 0, 0)',
      chartData: {},
      // 您可以通过修改 config-ucharts.js 文件中下标为 ['area'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。
      opts: {
        color: ['#fd953c'],

        legend: {
          show: false
        },
        padding: [25, 30, 0, 25],
        enableScroll: false,

        xAxis: {
          disableGrid: true,
          boundaryGap: 'justify'
        },
        yAxis: {
          disabled: true,
          gridType: 'dash',
          axisline: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        extra: {
          area: {
            type: 'straight',
            opacity: 0.2,
            addLine: true,
            width: 2,
            gradient: false
            //   activeType: "hollow",
            // activeType:'none'
          }
        }
      },
      res: {
        categories: [
          this.$t('Meal.Breakfast'),
          this.$t('Meal.lunch'),
          this.$t('Meal.tea'),
          this.$t('Meal.dinner'),
          this.$t('Meal.supper'),
          this.$t('Meal.early')
        ],
        series: [
          {
            name: '',
            data: [0, 0, 0, 0, 0, 0]
          }
        ]
      },
      unReadMsgNum: 0, // 未读消息个数
      unReadOrderNum: 0, // 未读代办
      isShowBusiness: false, // 是否显示营业数据
      isShowRevenue: false, //  是否显示营业额走势
      timeOut: null
    }
  },
  onShow() {
    console.log('onShow')
    // 检测更新
    checkUpdate()
  },
  onUnload() {
    console.log('onUnload')
    this.removeIndexMsg()
    this.timeOut = null
  },
  mounted() {
    console.log("mounted");
  },
  /**
   * 页面创建
   */
  created() {
    console.log("created");
    this.initData()
    this.setIndexMsg()
  },
  destroyed() {
    this.removeIndexMsg()
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      console.log("initData")
      if (!this.informationfz()) {
        return
      }
      this.getTabbarStatisticsList()
      this.getBusinessReportsList()
      this.getapiBusinessSituationList()
      // 获取未读信息
      this.getMsgUnRead()
      // 获取未读订单
      this.getOrderUnRead()
    },
    /**
     * 信息监听
     */
    setIndexMsg() {
      var that = this
      uni.$on(this.$common.MSG_CHOOSE_ORGN_SUCCESS, data => {
        console.log("收到信息", data);
        that.initData()
      })
      uni.$on(this.$common.MSG_UPDATE_MENU_SUCCESS, data => {
        console.log("更新menu成功", data);
        that.getTabbarStatisticsList()
      })
      uni.$on(this.$common.MSG_UPDATE_UNREAD_BACK, data => {
        console.log("消息与代办申请返回更新", data);
        that.getMsgUnRead()
        that.getOrderUnRead()
      })
    },
    /**
      * 销毁注销信息
      */
    removeIndexMsg() {
      uni.$off(this.$common.MSG_CHOOSE_ORGN_SUCCESS)
      uni.$off(this.$common.MSG_UPDATE_MENU_SUCCESS)
      uni.$off(this.$common.MSG_UPDATE_UNREAD_BACK)
    },
    /**
     * 获取经营情况数据
     */
    getapiBusinessSituationList() {
      this.org_ids = cache.get(this.$common.KEY_USER_ORGAN)
      let params = {
        start_date: this.startDate,
        time_type: this.selectedTime,
        end_date: this.endDate,
        org_id: [this.org_ids]
      }
      console.log(params);
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      apiBusinessSituationList(params)
        .then(res => {
          if (res.code === 0) {
            uni.hideLoading()
            console.log(res)
            let data = res.data
            var newList = []
            this.getData = []
            if (data.turnover_statistic.length > 0) {
              this.queryDateList.map(item => {
                let value = 0
                data.turnover_statistic.forEach(v => {
                  if (item === v.x_axis) {
                    value = divide(v.total_amount)
                    newList.push(item)
                    this.getData.push(Number(value))
                  }
                })
                return { value, item }
              })
              console.log("dataList", newList, this.getData);

              this.chartData = JSON.parse(
                JSON.stringify({
                  categories: newList,
                  series: [
                    {
                      name: '',
                      data: this.getData
                    }
                  ]
                })
              )
            } else {
              this.getData = [0, 0, 0, 0, 0, 0]
              // console.log(56)
              this.chartData = JSON.parse(
                JSON.stringify({
                  categories: this.queryDateList,
                  series: [
                    {
                      name: '',
                      data: this.getData
                    }
                  ]
                })
              )
            }
            this.drawCharts('canvasLine', this.chartData)
            // this.staging = res.data
            // this.initTurnoverTrendLine(res.data)
          } else {
            uni.$u.toast(res.msg)
            uni.hideLoading()
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
          console.log(err)
        })
    },
    /**
     * 获取常用功能数据
     */
    getTabbarStatisticsList() {
      let userInfo = cache.get(common.KEY_USER_INFO)
      let params = { id: userInfo.role_id }
      getTabbarStatistics(params)
        .then(res => {
          if (res.code === 0) {
            this.newHomeerrList = []
            var data = res.data || {}
            // 保存用户的页面授权列表
            if (data && Reflect.has(data, 'merchant_app_permission_keys')) {
              cache.set(
                this.$common.KEY_MERCHANT_APP_PERMISSION_KEYS,
                data.merchant_app_permission_keys
              )
              //
              var list = data.merchant_app_permission_keys || []
              if (Array.isArray(list) && list.length > 0) {
                console.log("list", list.join(','));
                // 有经营管理的权限
                this.isShowBusiness = list.join(',').indexOf("business_report") !== -1
                this.isShowRevenue = list.join(',').indexOf("business_management") !== -1
              }
            }
            if (res.data.merchant_app_select_permission_keys.length > 0) {
              this.seiqtype = true
              this.homeerrList.forEach((item, index) => {
                let add = res.data.merchant_app_select_permission_keys.indexOf(item.key)
                if (add >= 0) {
                  this.newHomeerrList.push(item)
                }
              })
              console.log("this.newHomeerrList", this.newHomeerrList);
            } else {
              this.seiqtype = false
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          console.log("err getTabbarStatisticsList", err);
          uni.$u.toast(err.message)
        })
    },
    /**
     * 从本地储存获取个人信息展示顶部
     * 判断从本地取得值是否为空为空跳转登录页
     */
    informationfz() {
      this.Information = cache.get(common.KEY_USER_INFO)
      this.organ = cache.get(this.$common.KEY_USER_ORGAN)
      console.log("informationfz", this.Information, this.organ);
      if (this.Information && this.organ) {
        this.userorgan = this.Information.orgs[this.organ]
        console.log("this.userorgan", this.userorgan);
        if (!this.userorgan) {
          uni.$u.toast(this.$t('index.organization'))
          this.$miRouter.push({
            path: '/pages/login/organ'
          })
        }
        return true
      } else {
        // 没有登录，返回登录页
        uni.$u.toast(this.$t('index.logInFirst'))
        this.$nextTick(() => {
          this.$miRouter.push({
            path: '/pages/login/index'
          })
        })
        return false
      }
    },
    /**
     * 获取今日营业数据
     */
    getBusinessReportsList() {
      let params = {
        time_type: this.selectedTime,
        date_type: this.date_type,
        org_id: [this.organ]
      }
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      apiBusinessReportsList(params)
        .then(res => {
          if (res.code === 0) {
            uni.hideLoading()
            this.businessSummery.forEach(item => {
              item.value = res.data[item.key]
            })
            let index0 = this.businessSummery.findIndex(item => item.key === 'turnover')
            this.businessSummery[index0].value =
              '￥' + numberToThousands(divide(this.businessSummery[index0].value))
            let index1 = this.businessSummery.findIndex(item => item.key === 'consume_order_count')
            this.businessSummery[index1].value = numberToThousands(
              this.businessSummery[index1].value
            )
            let index2 = this.businessSummery.findIndex(item => item.key === 'consume_person_count')

            this.businessSummery[index2].value = numberToThousands(
              this.businessSummery[index2].value
            )
          } else {
            uni.$u.toast(res.msg)
            uni.hideLoading()
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },
    /**
     * 点击选择账号时判断角色是否关联多个组织，如果只有一个不进行跳转
     */
    selectOrganization() {
      var userInfo = cache.get(this.$common.KEY_USER_INFO) || {}
      if (userInfo) {
        var orgs = userInfo.orgs ? userInfo.orgs : {}
        // 判断当前用户是否只有一个组织 如果只有一个直接跳到首页不需要选择
        if (orgs) {
          if (Object.keys(orgs).length <= 1) {
            uni.$u.toast('目前用户只绑定一个组织')
          } else {
            this.$miRouter.push({ path: '/pages/login/organ' })
          }
        }
      }
    },
    /**
     *底部代办事项
     *@params type 类型，消息与申请审批，根据用户的权限来跳转
     */
    handlerMessageClick(type) {
      var urlPath =
        type === 1
          ? '/pages_common_function/message/message'
          : '/pages_common_function/approval/approval'
      if (urlPath) {
        this.$miRouter.push({
          path: urlPath
        })
      }
    },
    /**
     * 获取未读内容
     */
    async getMsgUnRead() {
      const [error, res] = await this.$to(apiBackgroundMessagesMessagesGetMsgNum())
      // console.log(res)
      if (!error && Reflect.has(res, 'code') && res.code === 0) {
        // console.log('getMsgUnRead', res)
        var data = (Reflect.has(res, 'data') && res.data !== null) ? res.data : {}
        this.unReadMsgNum = data.unread_count || ''
      }
    },
    /**
     * 绘画首页图
     * @param {*} id
     * @param {*} data
     */
    drawCharts(id, data) {
      console.log('drawCharts', id, data)
      var cWidth = uni.upx2px(670)
      var cHeight = uni.upx2px(300)
      const ctx = uni.createCanvasContext(id, this)
      uChartsInstance[id] = new UCharts({
        color: ['#fd953c'],
        legend: {
          show: false
        },
        type: 'area',
        context: ctx,
        width: cWidth,
        height: cHeight,
        categories: data.categories,
        series: data.series,
        animation: true,
        enableScroll: false,
        background: '#FFFFFF',
        padding: [25, 30, 0, 25],
        xAxis: {
          disableGrid: true,
          boundaryGap: 'justify'
        },
        yAxis: {
          isabled: true,
          disabled: true,
          gridType: 'dash',
          axisline: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        extra: {
          area: {
            type: 'curve',
            opacity: 0.4,
            addLine: true,
            width: 2,
            gradient: true,
            activeType: 'hollow'
          }
        }
      })
    },
    /**
     * 图表点击
     * @param {} e
     */
    chartItemClick(e) {
      // uChartsInstance[e.target.id].touchLegend(e);
      console.log("e", e, this.chartData);
      uChartsInstance[e.target.id].showToolTip(e, {
        formatter: (item, category, index, opts) => {
          console.log("formatter", item, category, index, opts);
          if (category) {
            let data = item.data
            if (typeof item.data === 'object') {
              data = item.data.value
            }
            return category + '' + item.name + '营业额:¥' + data + "元"
          } else {
            if (item.properties && item.properties.name) {
              return item.properties.name
            } else {
              return item.name + '营业额:¥' + item.data + "元"
            }
          }
        }

      });
    },
    /**
     * 获取未读订单
     */
    async getOrderUnRead() {
      const [error, res] = await this.$to(apiMerchantMobileApprovalGetApprovalCount())
      if (!error && Reflect.has(res, 'code') && res.code === 0) {
        console.log("getOrderUnread", error, res);
        var data = res.data ? res.data : {}
        if (Reflect.has(data, 'not_approved_count')) {
          this.unReadOrderNum = data.not_approved_count || 0
          console.log(" this.unReadOrderNum", this.unReadOrderNum);
        }
      }
    },
    /**
     * 跳转到指定页面
     * @param {*} e
     */
    jumpToPage(item) {
      console.log("jumpToPage", item);
      var developFunctionTxt = "mingchuliangzao attendance_dverview visitor_invitation_management  messages_management passenger_flow"
      if (developFunctionTxt.indexOf(item.key) !== -1) {
        return showMsgDeveloping()
      }
      this.$miRouter.push({ path: item.jumpPath })
    }
  }
}
</script>

<style lang="scss" scoped>
* {
  margin: 0;
  padding: 0;
  list-style: none;
}

.home_wrap {
  width: 100%;
  height: 100%;
  padding-bottom: 110rpx;
  position: relative;
  .home_head_img {
    width: 100%;
    height: 508rpx;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
    border-radius: 0 0 30rpx 30rpx;
  }

  .home_header {
    // width: 750rpx;
    position: relative;
    height: 508rpx;
    overflow: hidden;
    z-index: 9999;

    .home_banner {
      width: 670rpx;
      height: 240rpx;
      border: solid 2rpx #ffffff;
      margin: auto;
      background-color: #ffecdb;
      border-radius: 30rpx 30rpx 0 0;
      position: absolute;
      bottom: 0;
      left: 40rpx;
      right: 40rpx;

      .home_banner_value {
        width: 589rpx;
        height: 82rpx;
        margin: auto;
        margin-top: 54rpx;
        display: flex;
        justify-content: space-around;

        .home_banner_value_li {
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          align-items: center;

          .t1 {
            font-family: DIN-Bold;
            font-size: 48rpx;
            font-weight: normal;
            font-stretch: normal;
            letter-spacing: 0rpx;
            color: #0e2435;
          }

          .t2 {
            font-family: PingFang-SC-Medium;
            font-size: 24rpx;
            font-weight: normal;
            font-stretch: normal;
            letter-spacing: 0rpx;
            color: $color-text-a;
          }
        }
      }

      .home_banner_text {
        width: 608rpx;
        height: 29rpx;

        margin: auto;
        margin-top: 29rpx;
        display: flex;
        justify-content: space-between;
        line-height: 29rpx;

        .t1 {
          font-family: PingFang-SC-Bold;
          font-size: 30rpx;
          color: $color-text;
        }

        .t2 {
          font-family: PingFang-SC-Medium;
          font-size: 24rpx;
          color: $color-text-a;
        }
      }
    }

    .top {
      width: 750rpx;
      height: 128rpx;
      display: flex;
      color: $color-text-b;
      font-size: 35rpx;
      font-weight: bold;
      justify-content: center;
      line-height: 128rpx;
      font-family: PingFang-SC-Medium;
    }

    .home_header_Information {
      width: 670rpx;
      height: 80rpx;
      margin: auto;
      display: flex;
      align-items: center;

      .home_header_seemore {
        width: 136rpx;
        height: 48rpx;
        background-color: $color-white;
        font-size: 24rpx;
        border-radius: 24rpx;
        text-align: center;
        line-height: 48rpx;
        color: $but;
        margin-left: 80rpx;
      }

      .home_header_Information_img {
        width: 80rpx;
        height: 80rpx;
        background-color: $color-white;
        border-radius: 50%;
      }

      .home_header_Information_organ {
        margin-left: 16rpx;
        width: 380rpx;
        display: flex;
        align-content: center;
        flex-direction: column;

        .t1 {
          font-family: PingFang-SC-Bold;
          font-size: 30rpx;
          font-weight: 600;
          color: $color-white;
        }
        .h-80{
          height: 80rpx;
          line-height: 80rpx;
        }

        .t2 {
          font-family: PingFang-SC-Regular;
          font-size: 20rpx;
          font-weight: normal;
          font-stretch: normal;
          letter-spacing: 0rpx;
          color: $color-white;
          opacity: 0.8;
          zoom: 0.8;
        }
      }
    }
  }

  .home_content_chart_wrap {
    width: 670rpx;
    height: 370rpx;
    background-color: $color-white;
    border-radius: 12rpx;
    margin: auto;
    margin-top: 30rpx;
    overflow: hidden;
    .home_banner_text {
      width: 608rpx;
      height: 29rpx;

      margin: auto;
      margin-top: 29rpx;
      display: flex;
      justify-content: space-between;
      line-height: 29rpx;

      .t1 {
        font-family: PingFang-SC-Bold;
        font-size: 30rpx;
        color: $color-text;
      }

      .t2 {
        font-family: PingFang-SC-Medium;
        font-size: 24rpx;
        color: $color-text-a;
      }
    }
    .home_content_chart_title {
      width: 220rpx;
      height: 30rpx;
      font-size: 30rpx;
      color: $color-text;
      margin-top: 29rpx;
      margin-left: 31rpx;
    }

    .home_content_chart {
      width: 670rpx;
      height: 300rpx;
      .charts {
        width: 670rpx;
        height: 300rpx;
      }
    }
  }

  .home_content_function {
    width: 670rpx;
    background-color: $color-text-b;
    border-radius: 12rpx;
    margin: auto;
    margin-top: 30rpx;
    overflow: hidden;

    .home_content_function_list {
      width: 573rpx;
      background-color: $color-text-b;
      margin: auto;
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-items: center;
      align-content: space-between;

      .home_content_function_center {
        width: 573rpx;
        // background: yellow;
        margin: 40rpx 0 40rpx 0;
        display: flex;
        justify-content: space-start;
        align-items: center;
        flex-wrap: wrap;

        .home_content_function_null {
          width: 100%;
          text-align: center;
          margin-top: 69rpx;
          font-size: 24rpx;
          color: $color-text-a;
        }
        .home_content_function_add {
          width: 164rpx;
          height: 50rpx;
          background-color: $but;
          color: $color-text-b;
          border-radius: 25rpx;
          margin: 14rpx auto;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .li {
          // width: 91rpx;
          width: 25%;
          height: 142rpx;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-between;
          margin-top: 10rpx;

          view {
            font-family: PingFang-SC-Medium;
            font-size: 24rpx;
            font-weight: normal;
            font-stretch: normal;
            letter-spacing: 0rpx;
            color: $color-text;
            zoom: 0.9;
          }

          image {
            width: 91rpx;
            height: 92rpx;
          }
        }
      }
    }

    .home_content_function_title {
      width: 608rpx;
      height: 29rpx;

      margin: auto;
      margin-top: 30rpx;
      display: flex;
      justify-content: space-between;
      line-height: 29rpx;

      .t1 {
        font-family: PingFang-SC-Bold;
        font-size: 30rpx;
        color: $color-text;
        font-weight: bold;
      }

      .t2 {
        font-family: PingFang-SC-Medium;
        font-size: 24rpx;
        color: $color-text-a;
      }
    }
  }

  .home_footer {
    width: 670rpx;
    height: 287rpx;
    background-color: $color-text-b;
    border-radius: 12rpx;
    margin: 30rpx 40rpx;
    overflow: hidden;

    .home_footer_title {
      display: block;
      margin-top: 20rpx;
      margin-left: 30rpx;
      font-family: PingFang-SC-Bold;
      font-size: 30rpx;
      font-weight: bold;
      font-stretch: normal;
      letter-spacing: 0rpx;
      color: #1d1e20;
    }

    .home_footer_first,
    .home_footer_second {
      width: 610rpx;
      height: 64rpx;
      display: flex;
      margin: auto;
      margin-top: 31rpx;
      align-items: center;

      .home_footer_jump_layout {
        .home_footer_jump {
          width: 100rpx;
          height: 40rpx;
          background-color: $but;
          border-radius: 20rpx;
          font-family: PingFang-SC-Medium;
          font-size: 20rpx;
          font-weight: normal;
          font-stretch: normal;
          letter-spacing: 0rpx;
          color: $color-text-b;
          text-align: center;
          line-height: 40rpx;
        }
      }

      .home_footer_text {
        display: flex;
        flex: 1;
        align-items: center;
        view:nth-child(1) {
          font-family: PingFang-SC-Medium;
          font-size: 24rpx;
          font-weight: normal;
          font-stretch: normal;
          letter-spacing: 0rpx;
          color: $color-text-a;
          margin-left: 19rpx;
        }

        view:nth-child(2) {
          font-family: DIN-Medium;
          font-size: 30rpx;
          font-stretch: normal;
          letter-spacing: 0rpx;
          color: $color-text;
          font-weight: bold;
          margin-left: 10rpx;
        }

        view:nth-child(3) {
          font-family: PingFang-SC-Medium;
          font-size: 24rpx;
          font-weight: normal;
          font-stretch: normal;
          letter-spacing: 0rpx;
          color: $color-text-a;
          margin-left: 10rpx;
        }
      }

      image {
        width: 64rpx;
        height: 64rpx;
        background-color: #ffead8;
        border-radius: 14rpx;
      }
    }
  }
}
</style>

<template>
  <view class="recover">
    <!-- 找回密码 -->
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('pages.login.back')"
      placeholder
      :autoBack="true"
      :leftIconColor="color.navigation"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    ></u-navbar>
    <!--#endif-->
    <view class="recover_wrap">
      <view class="recover_content"></view>
      <u-form :model="forndate" :rules="ruleser" ref="shouji">
        <view class="recover_content_input">{{ $t('pages.login.sj') }}</view>
        <u-form-item class="item" prop="phones">
          <!-- 手机号输入框 -->
          <u-input
            v-model="forndate.phones"
            border="bottom"
            :placeholder="$t('pages.login.zhanghs')"
          ></u-input>
        </u-form-item>
        <view class="recover_content_input">{{ $t('pages.login.Captcha') }}</view>
        <u-form-item prop="code">
          <!-- 验证码输入框 -->
          <u-input
            v-model="forndate.code"
            border="bottom"
            :placeholder="$t('pages.login.Pleaseerw')"
          >
            <template v-slot:suffix>
              <u-code
                ref="uCode"
                @change="codeChange"
                seconds="60"
                changeText="X秒重新获取"
              ></u-code>
              <u-button
                :color="color.themeColor"
                @tap="handlerRefreshCode"
                :text="Getverificationcode"
                type="success"
                size="mini"
                plain
              ></u-button>
            </template>
          </u-input>
        </u-form-item>
      </u-form>
    </view>
    <view class="recover_bottom_next">
      <u-button :color="color.themeColor" @click="next()">{{ $t('pages.login.Next') }}</u-button>
    </view>
    <verify-code ref="verifyCode" :is-number="true" @success="verifyCodeSuccess" @refresh="verifyCodeRefresh"></verify-code>
    <!--完了，薛博华小老弟都没有弄选择项目点的功能，就登录弄了，只能从登录页面拷贝过来试试能不能用了，重做是没有时间重做的了-->
    <CustomDialogComponent ref="customDialogRef" customDialogTitle="选择项目点" @handlerCustomConfirm="pointsDialogConfirm"
        @handlerCustomCancel="pointsDialogCancel" dialogType="custom" :isCloseOverlay="false">
        <template #custom>
          <view class="content-container">
            <scroll-view style="height: 450rpx" scroll-y>
              <u-radio-group v-model="radioValue1" placement="column" @change="radioGroupChange">
                <!-- 单个项目点 start -->
                <view class="radio-card" v-for="items in userPointsData" :key="items.company_id">
                  <view class="uni-h6">
                    <text class="uni-h6-text">{{ items.company_name }}</text>
                    <u-radio :customStyle="{ width: '40rpx', height: '40rpx' }" :name="items.company_id" size="36rpx"
                      activeColor="orange" iconSize="26rpx"></u-radio>
                  </view>
                </view>
                <!-- 单个项目点 end -->
              </u-radio-group>
            </scroll-view>
          </view>
        </template>
      </CustomDialogComponent>
  </view>
</template>
<script>
import { mapGetters } from 'vuex'
import Cache from '@/utils/cache'
import { loginVerification, getfindpasswoad, getLoginVerifyCode, apiBackgroundGetPhoneCompanyInfo } from '@/api/user.js'
import Base64 from 'base-64'

export default {
  computed: {
    ...mapGetters(['color'])
  },
  data() {
    return {
      Getverificationcode: '获取验证码',
      forndate: {
        phones: '',
        code: ''
      },
      ruleser: {
        // 手机号验证码
        phones: [
          {
            required: true,
            message: this.$t('pages.login.zhanghs'),
            trigger: ['blur', 'change']
          },
          {
            validator: (rule, value, callback) => {
              return uni.$u.test.mobile(value)
            },
            message: this.$t('login.phonenumberncorrect'),
            trigger: ['change', 'blur']
          }
        ],
        code: [
          {
            type: 'string',
            required: true,
            len: 6,
            message: this.$t('pages.login.yanz'),
            trigger: ['change', 'blur']
          }
        ]
      },
      resultTxtList: [], // 验证码数组
      vercodeBase64: '', // 验证码base64
      userPointsData: [],
      radioValue1: ''
    }
  },
  methods: {

    next() {
      // 手机号验证码点击下一步找回密码
      this.$refs.shouji
        .validate()
        .then(res => {
          // 这里改成获取项目点信息
          this.getAllPoints({
            phone: this.forndate.phones,
            sms_code: this.forndate.code,
            code: this.vercodeBase64,
            sms_type: 2
          })
        })
        .catch(errors => {
          uni.$u.toast(this.$t('login.phonenumber'))
        })
    },
    // 抽离原有的校验 --原来冯建涛写的下一步逻辑抽离出来
    getVerify() {
      let params = {
        phone: this.forndate.phones,
        sms_code: this.forndate.code,
        choices: 0,
        code: this.vercodeBase64
      }
      getfindpasswoad(params)
        .then(res => {
          if (res.code === 0) {
            console.log(res)
            this.forndate.baseCode = this.vercodeBase64
            Cache.set('userphone', JSON.stringify(this.forndate))
            // 跳转页面
            setTimeout(() => {
              this.$miRouter.push({
                path: '/pages/login/newpass'
              })
            }, 1000)
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },
    // 获取项目点信息---小老弟代码
    async getAllPoints(params) {
      const [error, res] = await this.$to(apiBackgroundGetPhoneCompanyInfo(params))
      if (error) {
        uni.$u.toast(error.message)
      }
      if (res && res.code === 0) {
        let data = res.data || []
        if (data) {
          this.userPointsData = data.filter((items) => {
            return items.company_id !== 1
          })
          if (this.userPointsData && this.userPointsData.length > 1) {
            this.$refs.customDialogRef.showCustomDialog()
            // 当项目点大于1时，先默认让单选框选中第一项
          } else {
            this.getVerify()
          }
        } else {
          this.getVerify()
        }
      }
    },
    codeChange(e) {
      this.Getverificationcode = e
    },
    // 获取验证码
    getCode() {
      if (uni.$u.test.mobile(this.forndate.phones)) {
        if (this.$refs.uCode.canGetCode) {
          let params = { phone: this.forndate.phones, choices: 2, code: this.vercodeBase64 }
          loginVerification(params)
            .then(res => {
              if (res.code === 0) {
                console.log(res)
                this.$refs.uCode.start() // 开启倒计时
              } else {
                console.log(res)
                uni.$u.toast(res.msg)
              }
            })
            .catch(err => {
              uni.$u.toast(err.message)
            })
        } else {
          // eslint-disable-next-line no-undef
          uni.$u.toast(this.$t('login.afterthecountdown'))
        }
      } else {
        uni.$u.toast(this.$t('login.phonenumberncorrect'))
      }
    },
    // 验证成功
    verifyCodeSuccess(value) {
      console.log("verifyCodeSuccess", value);
      this.getCode()
    },
    // 图形验证码的刷新按钮
    verifyCodeRefresh() {
      this.getVerCode(this.forndate.phones, true)
    },
    // 刷新图形验证码
    handlerRefreshCode() {
      if (!this.$refs.uCode.canGetCode) return
      this.getVerCode(this.forndate.phones, false)
    },
    // 获取图形验证码
    getVerCode(phone, flag) {
      console.log("phone", phone);
      if (!phone || !/^1[3456789]\d{9}$/.test(phone)) {
        return this.$u.toast('请输入正确的手机号码')
      }
      getLoginVerifyCode({ phone }).then(res => {
        if (res && res.code === 0) {
          let data = res.data || ''
          if (data) {
            let keys = Base64.decode(data.key) ? JSON.parse(Base64.decode(data.key)) : ''
            this.vercodeBase64 = data.key
            console.log("getLoginVerifyCode", keys);
            this.resultTxtList = []
            if (keys && typeof keys === 'object') {
              for (let keyName in keys) {
                this.resultTxtList.push(keys[keyName])
              }
            }
            if (this.$refs.verifyCode) {
              this.$refs.verifyCode.setResultTxt(this.resultTxtList)
              if (flag) {
                this.$refs.verifyCode.init()
              } else {
                this.$refs.verifyCode.open()
              }
            }
          }
        } else {
          uni.$u.toast(res.msg)
        }
      }).catch(() => {})
    },
    // 新增代码 --- 监听对话框确认---小老弟代码
    pointsDialogConfirm() {
      if (!this.radioValue1) {
        this.$u.toast("请先选择项目点")
        return
      }
      this.forndate.company_id = this.radioValue1
      if (this.$refs.customDialogRef) {
        this.$refs.customDialogRef.hideCustomDialog()
      }
      this.getVerify()
    },
    // 新增代码 --- 监听对话框取消---小老弟代码
    pointsDialogCancel() {
      this.$refs.customDialogRef.handlerCustomCancel()
      return
    }
  }
}
</script>
<style lang="scss" scoped>
.recover {
  height: 100vh;
  background-size: 100% 785rpx;
  background-repeat: no-repeat;

  .recover_wrap {
    width: 670rpx;
    // height: 425rpx;
    background-color: $background;
    border-radius: 12rpx;
    margin: 40rpx;
    padding: 40rpx;

    .recover_content_input {
      font-size: $font-size-xl;
      // width: 214rpx;
      height: 35rpx;
      color: $color-text;
      margin-top: 20rpx;
      margin-left: 20rpx;
    }
  }

  .recover_bottom_next {
    width: 670rpx;
    height: 70rpx;
    position: absolute;
    bottom: 54rpx;
    left: 40rpx;
  }
}
.content-container {
  ::v-deep .radio-card {
    display: flex;
    justify-content: center;
    margin-bottom: 15rpx;
  }
}
.uni-h6 {
  display: flex;
  justify-content: space-between;
  width: 90%;
  border: 2rpx solid #ebeaea;
  padding: 30rpx;
  border-radius: 14rpx;

  .uni-h6-text {
    font-size: 30rpx;
  }

  .u-radio__icon-wrap--circle {
    ::v-deep .uicon-checkbox-mark {
      font-weight: bold !important;
    }
  }
}
</style>

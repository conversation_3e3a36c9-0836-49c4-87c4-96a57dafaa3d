<template>
  <view class="selectsign">
    <!-- #ifdef MP-WEIXIN ||MP-ALIPAY -->
    <view class="selectsign_margin_top"></view>
    <!-- #endif-->
    <view class="selectsign_wrap">
      <view class="selectsign_toptext">{{ $t('pages.login.text') }}</view>
      <view class="selectsign_toptext_mini">{{ $t('pages.login.toptexts') }}</view>
      <view class="selectsign_img">
        <image :src="imgPath.IMG_INDEX_BG_WHITE" mode="widthFix"></image>
      </view>
      <u-button :customStyle="customStyleBtn" :color="color.themeColor" class="selectsign_account" @click="jumpAccount">
        {{ $t('pages.login.butzh') }}
      </u-button>
      <u-button :customStyle="customStyleBtn" :color="color.twoColor" class="selectsign_phone" @click="jumpPhone">
        {{ $t('pages.login.butsjh') }}
      </u-button>
      <view class="selectsign_agreement">
        <u-checkbox shape="circle" name="login" size="22rpx" :activeColor="color.themeColor" :checked="agreementCheck"
          @change="agreementChange"></u-checkbox>
        {{ $t('pages.login.xieyi') }}
        <text @click="popup = true">《{{ $t('pages.login.xieyis') }}》</text>
      </view>
      <u-popup :show="popup" mode="center" @close="ejectPopup" round="40rpx">
        <view class="selectsign_agreement_popup">
          <view class="span">
            <view>{{ agreement.agreement_name }}</view>
            <u-parse :content="agreement.content"></u-parse>
            <view>{{ agreement.update_time }}</view>
          </view>
        </view>
      </u-popup>
      <!-- <view class="selectsign_bottom_text">{{ $t('pages.login.qita') }}</view>
      <view class="selectsign_bottom_logo" @click="gowx">
        <image :src="imgPath.IMG_WEIXIN_GREEN" mode="scaleToFill"></image>
      </view> -->
    </view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
import { getbookingAgreement } from '@/api/user.js'
import { unescapeHTML } from '@/utils/util'
export default {
  computed: {
    ...mapGetters(['color'])
  },
  data() {
    return {
      imgPath: this.$imgPath,
      customStyleBtn: {
        width: '610rpx',
        height: '81rpx',
        backgroundColor: '#fd953c',
        borderRadius: ' 40px',
        margin: '30rpx 70rpx'
      },
      popup: false, // 弹窗开关
      agreement: {},
      time: '',
      agreementCheck: false // 协议是否阅读
    }
  },
  onload() {
    // #ifdef MP-ALIPAY
    // eslint-disable-next-line no-undef
    my.hideBackHome()
    // #endif
  },
  mounted() {
    this.getbookingAgreementget()
  },
  methods: {
    getbookingAgreementget() {
      let params = { agreement_type: 'IPA' }
      getbookingAgreement(params)
        .then(res => {
          if (res.code === 0) {
            // console.log(res);
            var agreement = res.data.results
            if (Reflect.has(agreement, "content") && agreement.content != null) {
              agreement.content = unescapeHTML(agreement.content)
            }
            this.agreement = agreement
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },

    ejectPopup() {
      this.popup = false
      // console.log('close');
    },
    jumpAccount() {
      if (!this.checkAgreementIsRead()) {
        return
      }
      this.$miRouter.push({
        path: '/pages/login/login',
        query: {
          type: 1
        }
      })
    },
    jumpPhone() {
      if (!this.checkAgreementIsRead()) {
        return
      }
      this.$miRouter.push({
        path: '/pages/login/login',
        query: {
          type: 2
        }
      })
    },
    /**
     * 检查协议是否阅读
     */
    checkAgreementIsRead() {
      if (!this.agreementCheck) {
        uni.$u.toast('请先阅读并同意相关协议')
        return false
      }
      return true
    },
    /**
     * 阅读协议监听
     */
    agreementChange(value) {
      this.agreementCheck = value
    }
  }
}
</script>
<style lang="scss" scoped>

.selectsign {
  background-color: $background;
  height: 100vh;
  background-size: 100% 785rpx;
  background-repeat: no-repeat;

  .selectsign_margin_top {
    padding-top: 120rpx;
  }

  .selectsign_agreement_popup {
    // position: relative;
    width: 600rpx;
    max-height: 40rpx;
    min-height: 800rpx;
    overflow-y: auto;
    border-radius: 40rpx;
    background-color: $background;
    margin: 40rpx;
    padding: 50rpx;

    .span {
      view:nth-child(1) {
        font-size: 40rpx;
        margin: 20rpx;
      }

      view:nth-child(2) {
        font-size: 24rpx;
      }

      view:nth-child(3) {
        font-size: 20rpx;
        position: relative;
        left: 300rpx;
        bottom: -30rpx;
      }
    }
  }

  .selectsign_wrap {
    background-color: $background;
    overflow: hidden;

    .selectsign_toptext {
      width: 700rpx;
      // height: 46rpx;
      font-size: 46rpx;
      color: $color-text-f;
      // margin-top: 37rpx;
      margin: 37rpx 69rpx;
    }

    .selectsign_toptext_mini {
      width: 630rpx;
      // height: 60rpx;
      font-size: 28rpx;
      color: #93989e;
      margin: 25rpx 69rpx;
    }

    .selectsign_img {
      width: 651rpx;
      height: 492rpx;
      margin-top: 56rpx;
      margin: auto;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .selectsign_account {
      width: 610rpx;
      height: 81rpx;
      background-color: $but;
      border-radius: 40rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      color: $color-text-b;
      font-size: 28rpx;
      margin: 62rpx auto;
    }

    .selectsign_phone {
      width: 610rpx;
      height: 81rpx;
      background-color: $butwo;
      border-radius: 40rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      color: $color-text-b;
      font-size: $font-size-nr;
      margin: 29rpx auto;
    }

    .selectsign_agreement {
      width: 100%;
      height: 23rpx;
      font-size: $font-size-xs;
      color: $color-text-a;
      margin: 31rpx auto;
      display: flex;
      justify-content: center;

      text {
        font-size: $font-size-xs;
        font-weight: normal;
        color: $but;
      }
    }

    .selectsign_bottom_text {
      width: 300rpx;
      height: 20rpx;
      font-family: PingFang-SC-Regular;
      font-size: 20rpx;
      font-weight: normal;
      font-stretch: normal;
      letter-spacing: 1rpx;
      text-align: center;
      color: $color-text-a;
      margin: 40rpx auto;
    }

    .selectsign_bottom_logo {
      width: 78rpx;
      height: 78rpx;
      // background-color: #11e69e;
      margin: 28rpx auto;

      image {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>

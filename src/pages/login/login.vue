<template>
  <view class="mobile_sign">
    <view class="mobile_sign_wrap">
      <view class="mobile_sign_top">
        <!--#ifdef MP-WEIXIN || H5 -->
        <u-navbar
          :title="$t('pages.login.login')"
          :bgColor="bgColor"
          :autoBack="true"
          :leftIconColor="color.color1"
          leftIconSize="37rpx"
          :titleStyle="{ color: color.color1, fontSize: '37rpx' }"
        ></u-navbar>
        <!--#endif-->
        <view :color="color.themeColor" class="mobile_sign_title">
          {{ $t('pages.login.hello') }},
        </view>
        <view class="mobile_sign_title_second">{{ $t('pages.login.texer') }}</view>
      </view>
      <image class="mobile_sign_img" :src="imgPath.IMG_INDEX_BG_ORANGE" mode="scaleToFill"></image>
      <view class="mobile_sign_center">
        <login-pass :type="type"></login-pass>
      </view>
    </view>
  </view>
</template>

<script>
import LoginPass from '@/components/LoginPass.vue'
import { mapGetters } from 'vuex'
export default {
  computed: {
    ...mapGetters(['color'])
  },
  components: {
    LoginPass
  },

  data() {
    return {
      imgPath: this.$imgPath,
      bgColor: 'rgba(0,0,0,0)',
      type: 1
    }
  },
  onLoad(options) {
    this.type = Number(options.type ? options.type : 1)
  },
  /**
   * 页面创建
   */
  created() {
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.mobile_sign {
  background-color: $background;
  height: 100vh;
  background-size: 100% 785rpx;
  background-repeat: no-repeat;

  .mobile_sign_wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    .mobile_sign_img {
      width: 750rpx;
      height: 508rpx;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 10;
    }

    .mobile_sign_top {
      position: relative;
      width: 750rpx;
      height: 508rpx;
      background-repeat: no-repeat;
      background-size: 100%;
      z-index: 9999;

      .mobile_sign_title {
        width: 120rpx;
        height: 50rpx;
        font-size: $font-size-xxxl;
        color: $background;
        margin-top: 135rpx;
        margin-left: 69rpx;
      }

      .mobile_sign_title_second {
        width: 350rpx;
        height: 46rpx;
        font-size: $font-size-xxxl;
        color: $background;
        margin: 32rpx 69rpx;
      }
    }

    .mobile_sign_center {
      width: 750rpx;
      height: 921rpx;
      background-color: $background;
      border-radius: 40rpx 40rpx 0rpx 0rpx;
      margin-top: -94rpx;
      z-index: 9999;
    }
  }
}
</style>

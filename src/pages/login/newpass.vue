<template>
  <view class="new_password">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('pages.login.newpass')"
      placeholder
      :autoBack="true"
      :leftIconColor="color.navigation"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    ></u-navbar>
    <!--#endif-->
    <view class="new_password_wrap">
      <view class="new_password_content"></view>
      <u-form :model="fornpass" :rules="ruleser" ref="fornpasserr">
        <view class="new_password_content_input">{{ $t('pages.login.newpass') }}</view>
        <u-form-item prop="password1">
          <u-input
            password
            v-model="fornpass.password1"
            border="bottom"
            autocomplete="off"
            :placeholder="$t('pages.login.password')"
          ></u-input>
        </u-form-item>
        <view class="new_password_content_input">{{ $t('pages.login.confirm') }}</view>
        <u-form-item prop="password2">
          <u-input
            password
            v-model="fornpass.password2"
            border="bottom"
            autocomplete="off"
            :placeholder="$t('pages.login.password')"
          ></u-input>
        </u-form-item>
      </u-form>
    </view>
    <view class="new_password_buttom_finish">
      <u-button :color="color.themeColor" @click="finish">{{ $t('pages.login.finish') }}</u-button>
    </view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
import md5 from 'js-md5'
import Cache from '@/utils/cache'
import { getfindpasswoad } from '@/api/user.js'
export default {
  computed: {
    ...mapGetters(['color'])
  },
  data() {
    return {
      userphone: {},
      fornpass: {
        password1: '',
        password2: ''
      },
      ruleser: {
        password1: [
          {
            required: true,
            message: this.$t('pages.login.password'),
            trigger: ['blur', 'change']
          },
          {
            pattern: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,20}$/g,
            // 正则检验前先将值转为字符串
            transform(value) {
              return value.toString()
            },
            message: this.$t('login.numbers'),
            trigger: ['blur', 'change']
          }
        ]
      }
    }
  },
  onReady() {
    this.$refs.fornpasserr.setRules(this.ruleser)
  },
  methods: {
    finish() {
      // 点击修改密码
      this.$refs.fornpasserr
        .validate()
        .then(res => {
          if (this.fornpass.password2 === this.fornpass.password1) {
            this.userphone = JSON.parse(Cache.get('userphone'))
            console.log(this.fornpass.password1, this.fornpass.password2, this.userphone)
            let params = {
              phone: this.userphone.phones,
              sms_code: this.userphone.code,
              new_password: md5(this.fornpass.password1),
              choices: 1,
              code: this.userphone.baseCode
            }
            if (this.userphone.company_id) {
              params.company_id = this.userphone.company_id
            }
            getfindpasswoad(params)
              .then(res => {
                if (res.code === 0) {
                  uni.$u.toast(res.msg)
                  this.$miRouter.push({
                    path: '/pages/login/login',
                    query: {
                      type: 1
                    }
                  })
                } else {
                  uni.$u.toast(res.msg)
                }
              })
              .catch(err => {
                uni.$u.toast(err.message)
              })
          } else {
            // 两次密码不相等
            uni.$u.toast(this.$t('pages.login.noword'))
          }
        })
        .catch(errors => {
          uni.$u.toast(this.$t('login.numbers'))
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.new_password {
  height: 100vh;
  background-size: 100% 785rpx;
  background-repeat: no-repeat;

  .new_password_wrap {
    width: 670rpx;
    // height: 425rpx;
    background-color: $background;
    border-radius: 12rpx;
    margin: 40rpx;
    padding: 40rpx;

    .new_password_content_input {
      font-size: $font-size-xl;
      // width: 214rpx;
      height: 35rpx;
      color: $color-text;
      margin-top: 20rpx;
      margin-left: 20rpx;
    }
  }

  .new_password_buttom_finish {
    width: 670rpx;
    height: 70rpx;
    position: absolute;
    bottom: 54rpx;
    left: 40rpx;
  }
}
</style>

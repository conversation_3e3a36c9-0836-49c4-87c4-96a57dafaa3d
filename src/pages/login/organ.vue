<template>
  <view class="select_organization">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('pages.index.organization')"
      placeholder
      :autoBack="true"
      :leftIconColor="color.navigation"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    ></u-navbar>
    <!--#endif-->
    <view class="select_organization_nav_text">{{ orgnTitle }}</view>
    <view class="select_organization_content_list">
      <u-radio-group placement="column" v-model="value" @change="groupChange">
        <view
          class="select_organization_content"
          v-for="(item, index) in radiolist1"
          :key="index"
          @click="groupClick(item)"
        >
          <view class="select_organization_content_text">
            <view class="select_organization_content_writing">
              <text>{{ item.scr }}</text>
              <!-- <text>({{ item.scr }})</text> -->
            </view>
          </view>
          <view class="select_organization_content_radio">
            <u-radio
              size="36rpx"
              shape="circle"
              :activeColor="color.themeColor"
              :name="item.id"
            ></u-radio>
          </view>
        </view>
      </u-radio-group>
    </view>
    <view class="select_organization_confirm">
      <u-button :customStyle="customStyleBtn" :color="color.themeColor" @click="confirm">{{ $t('index.Confirm') }}</u-button>
    </view>
  </view>
</template>

<script>
import { getbindOrg } from '@/api/user.js'
import { mapGetters } from 'vuex'
// import common from '@/common/common'
import cache from '@/utils/cache'
export default {
  computed: {
    ...mapGetters(['img', 'color'])
  },
  data() {
    return {
      value: '',
      radiolist1: [],
      userorgan: 0,
      orgnTitle: '', // 项目点名称
      customStyleBtn: { // 按钮自定义样式这里为了兼容微信与支付宝
        width: '670rpx',
        height: '70rpx',
        margin: '20rpx auto'

      }
    }
  },
  onload() {
    // #ifdef MP-WEIXIN || MP_ALIPY
    this.initData()
    // #endif
  },
  created() {
    // 初始化数据
    this.initData()
  },

  methods: {
    // ...mapMutations(['SET_ORGS']),
    /**
     * 初始化数据
     */
    initData() {
      var userInfo = cache.get(this.$common.KEY_USER_INFO) || {}

      if (userInfo) {
        // eslint-disable-next-line camelcase
        var companyName = userInfo.company_name ? userInfo.company_name : {}
        this.orgnTitle = companyName
        var orgs = userInfo.orgs ? userInfo.orgs : {}
        console.log('orgs', orgs, userInfo)
        // 判断当前用户是否只有一个组织 如果只有一个直接跳到首页不需要选择
        if (orgs) {
          if (Object.keys(orgs).length <= 1) {
            console.log(Object.keys(orgs))
            this.$store.commit('user/SET_ORGS', Object.keys(orgs))
            cache.set(this.$common.KEY_USER_ORGAN, Object.keys(orgs)[0])
            uni.$u.toast('目前用户只关联一个组织')
            uni.$emit(this.$common.MSG_CHOOSE_ORGN_SUCCESS, this.userorgan)
            this.$miRouter.pushTab({ path: '/pages/index/index' })
          }
        }
        for (let item in orgs) {
          this.radiolist1.push({ name: companyName, scr: orgs[item], id: item })
        }
        var id = cache.get(this.$common.KEY_USER_ORGAN)
        if (id) {
          this.value = id
          this.userorgan = id
        }
      }
    },
    groupChange(n) {
      console.log(n)
      this.userorgan = n
    },
    groupClick(item) {
      console.log(item)
      this.value = item.id
      this.userorgan = item.id
    },
    /**
     * 点击确认
     */
    confirm() {
      let params = { org_no: this.userorgan }
      getbindOrg(params)
        .then(res => {
          console.log(res)
          if (res.code === 0) {
            this.$store.commit('user/SET_ORGS', this.userorgan)
            cache.set(this.$common.KEY_USER_ORGAN, this.userorgan)
            uni.$emit(this.$common.MSG_CHOOSE_ORGN_SUCCESS, this.userorgan)
            this.$miRouter.pushTab({ path: '/pages/index/index' })
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.select_organization {
  height: 100vh;

  .select_organization_nav_text {
    margin: 40rpx;
  }

  .select_organization_content_list {
    display: flex;
    flex-wrap: wrap;
    padding-bottom: 140rpx;
  }

  .select_organization_content {
    width: 670rpx;
    height: 140rpx;
    background-color: $background;
    border-radius: 12rpx;
    margin: 20rpx 40rpx ;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .select_organization_content_text {
      margin-left: 30rpx;

      .select_organization_content_writing text:nth-child(1) {
        height: 31rpx;
        font-size: $font-size-lg;
        color: $color-text;
      }

      .select_organization_content_writing text:nth-child(2) {
        height: 31rpx;
        font-size: $font-size-lg;
        color: $color-text-a;
      }
    }
  }
  .select_organization_confirm {
    width: 100%;
    height: 110rpx;
    position: fixed;
    bottom: 0;
    left: 0;
    background-color:#fff;
  }
}
</style>

<template>
  <view class="information">
    <image class="information_banner" :src="imgPath.IMG_INDEX_BG_ORANGE" mode="scaleToFill"></image>
    <view class="information_nav"  @longpress="showVconsoleHandle">
      <u-navbar :title="$t('tabbar.mine')" :title-style="{ fontSize: '37rpx', color: '#fff' }" placeholder
        @leftClick="$miRouter.push({ path: 'pages/index/index' })" :bg-color="bgColor" left-icon=""
       >
        <!--#ifdef H5-->
        <view slot="left" class="returner_left">
          <image :src="imgPath.IMG_EXIT_WHITE" mode="scaleToFill" />
        </view>
        <!--#endif-->
        <!--#ifdef MP-WEIXIN-->
        <view slot="left"></view>
        <!--#endif-->
        <view slot="right" class="returner_right">
          <image :src="imgPath.IMG_MORE_WHITE" mode="" />
        </view>
      </u-navbar>
      <view class="information_avatar">
        <view class="information_avatar_img">
          <image :src="imgPath.IMG_HEAD_DEFAULT" alt="" mode="scaleToFill" />
        </view>
        <view class="information_mine">
          <text class="information_mine_name">{{ userinfolist.username }}</text>
          <text class="information_mine_phone">
            {{ userinfolist.mobile | formatMobile }}
          </text>
        </view>
      </view>
    </view>
    <view class="information_bunner">
      <view class="information_bunner_suggestion">
        <view class="information_bunner_suggestion_img">
          <view class="img"><img :src="imgPath.IMG_MORE_BLACK" alt="" mode="scaleToFill" /></view>
          <view>{{ $t('tabbar.Suggesteedback') }}</view>
        </view>
        <view class="information_bunner_suggestion_eject" @click="showToast">
          {{ $t('tabbar.forwardfeedback') }} >
        </view>
        <u-toast ref="uToast"></u-toast>
      </view>
      <view class="information_loginout" @click="loginOut">{{ $t('tabbar.Signout') }}</view>
    </view>
    <tabbar :current-page="3" />
  </view>
</template>

<script>
import tabbar from '@/custom-tab-bar/index.vue'
import { mapActions, mapGetters } from 'vuex'
import cache from '@/utils/cache'
import common from '@/common/common'
// #ifdef H5
import VConsole from 'vconsole'
// #endif
export default {
  computed: {
    ...mapGetters(['img'])
  },
  components: {
    tabbar
  },
  data() {
    return {
      imgPath: this.$imgPath,
      show: false,
      bgColor: 'rgba(0, 0, 0, 0)',
      userinfolist: {}
    }
  },
  mounted() {
    this.userinfo()
  },
  filters: {
    formatMobile(value) {
      console.log("formatMobile", value);
      if (value === null || isNaN(value)) {
        return ''
      }
      return value
    }
  },
  methods: {
    ...mapActions({
      logOut: 'user/logout',
      setVconsoleOpts: 'setVconsoleOpts'
    }),
    showToast() {
      this.$refs.uToast.show({
        message: this.$t('tabbar.completefunction')
      })
    },

    // 个人信息展示
    userinfo() {
      this.userinfolist = cache.get(common.KEY_USER_INFO)
    },
    // 退出登录
    async loginOut() {
      console.log(1)
      // this.$store.dispatch('user/logout')
      await this.logOut()
      this.$miRouter.replace({ path: '/pages/login/index' })
    },
    // 显示console 弹窗
    showVconsoleHandle() {
      // #ifdef H5
      let showVconsole = sessionStorage.getItem('showVconsole')
      if (showVconsole === 1) return
      uni.showModal({
        title: '提示',
        content: `确定打开日志打印吗？`,
        confirmText: '确定',
        cancelText: '取消',
        success: async (res) => {
          if (res.confirm) {
            await this.setVconsoleOpts(true)
            new VConsole()
          }
        }
      })
      // #endif
    }
  }
}
</script>

<style lang="scss" scoped>
.information {
  position: relative;

  .information_banner {
    position: absolute;
    z-index: 10;
    top: 0;
    left: 0;
    width: 750rpx;
    height: 440rpx;

    &image {
      width: 100%;
      height: 100%;
    }
  }
}

.u-page {
  padding: 0;
}

.u-cell-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 8rpx;
}

.u-cell-group__title__text {
  font-weight: bold;
}

.information_nav {
  width: 750rpx;
  height: 440rpx;
  position: relative;
  z-index: 10;

  .information_avatar {
    width: 750rpx;
    height: 130rpx;
    margin-top: 66rpx;
    display: flex;

    .information_avatar_img {
      width: 113rpx;
      height: 124rpx;
      border-radius: 50%;
      border: 3rpx solid #fff;
      margin-left: 46rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .information_mine {
      width: 200rpx;
      height: 100rpx;
      margin: 20rpx 30rpx;
      display: flex;
      flex-wrap: wrap;
      align-items: space-around;

      .information_mine_name {
        height: 34rpx;
        font-size: 36rpx;
        color: #ffffff;
      }

      .information_mine_phone {
        height: 21rpx;
        font-size: 28rpx;
        color: #ffffff;
      }
    }
  }

  .returner_left image {
    width: 40rpx;
    height: 40rpx;
  }

  .returner_right image {
    width: 40rpx;
    height: 40rpx;
  }
}

.information_bunner {
  position: relative;
  width: 750rpx;
  height: 750rpx;
  background-color: #f0f3f5;
  border-radius: 30rpx 30rpx 0 0;
  margin-top: -60rpx;
  overflow: hidden;
  z-index: 11;

  .information_bunner_suggestion {
    width: 670rpx;
    height: 110rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    margin: 50rpx 40rpx;
    padding: 40rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .information_bunner_suggestion_img {
      width: 180rpx;
      display: flex;
      justify-content: space-around;
      align-items: center;

      img {
        width: 34rpx;
        height: 34rpx;
      }
    }

    .information_bunner_suggestion_eject {
      color: #c2c5c8;
      width: 460rpx;
      display: flex;
      justify-content: flex-end;
    }
  }

  .information_loginout {
    width: 670rpx;
    height: 90rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    margin: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28rpx;
    color: #ff6c6c;
  }
}</style>

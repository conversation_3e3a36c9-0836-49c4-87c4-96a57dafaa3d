<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <view>
    <!-- #ifdef MP-WEIXIN || H5  -->
    <u-navbar
      :title="$t('tabbar.statistics')"
      :title-style="{ fontSize: '37rpx', color: color.navigation }"
      left-icon-size="20px"
      :left-icon-color="color.navigation"
      @leftClick="leftClick"
      placeholder
    />
    <!--#endif-->
    <view class="statistics">
      <view class="statistics_content">{{ $t('statistics.financial.statement') }}</view>
      <view class="statistics_ul">
        <view
          v-for="item in newFinaceList"
          :key="item.id"
          class="statistics_li"
          @click="jumpToPage(item)"
        >
          <view class="statistics_content_img">
            <image :src="item.img" mode="scaleToFill" />
          </view>
          <view class="statistics_content_text">{{ item.name }}</view>
        </view>
      </view>
    </view>
    <view class="statistics">
      <view class="statistics_content">{{ $t('business.Access.Control') }}</view>
      <view class="statistics_ul">
        <view
          class="statistics_li"
          v-for="item in newOverviewList"
          :key="item.id"
          @click="jumpToPage(item)"
        >
          <view class="statistics_content_img">
            <image :src="item.img" mode="scaleToFill" />
          </view>
          <view class="statistics_content_text">{{ item.name }}</view>
        </view>
      </view>
    </view>

    <tabbar :current-page="2" />
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
import tabbar from '@/custom-tab-bar/index.vue'
import comDic from '@/common/comDic'
import cache from '@/utils/cache'
import common from '@/common/common'
import { getTabbarStatistics } from '@/api/index.js'
import { showMsgDeveloping } from '@/utils/userUtil'
export default {
  computed: {
    ...mapGetters(['img', 'color'])
  },
  components: {
    tabbar
  },
  data() {
    return {
      imgPath: this.$imgPath,
      finaceList: comDic.FINANCE_DATA_LIST,
      newFinaceList: [],
      overviewList: comDic.OVERVIEW_DATA_LIST,
      newOverviewList: []
    }
  },
  mounted() {
    this.getTabbarStatisticsList()
  },
  methods: {
    /**
     * 获取权限展示列表
     */
    getTabbarStatisticsList() {
      let userInfo = cache.get(common.KEY_USER_INFO)
      let params = { id: userInfo.role_id }
      getTabbarStatistics(params)
        .then(res => {
          if (res.code === 0) {
            // console.log(res.data.merchant_app_permission_keys);
            if (res.data.merchant_app_permission_keys.length > 1) {
              this.finaceList.forEach((item, index) => {
                let add = res.data.merchant_app_permission_keys.indexOf(item.key)
                if (add >= 0) {
                  this.newFinaceList.push(item)
                }
              })
              this.overviewList.forEach((item, index) => {
                let add = res.data.merchant_app_permission_keys.indexOf(item.key)
                if (add >= 0) {
                  this.newOverviewList.push(item)
                }
              })
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },
    /**
     * 跳转到指定页面
     * @param {*} e
     */
    jumpToPage(item) {
      console.log("jumpToPage", item);
      var developFunctionTxt = "mingchuliangzao attendance_dverview visitor_invitation_management messages_management passenger_flow"
      if (developFunctionTxt.indexOf(item.key) !== -1) {
        return showMsgDeveloping()
      }
      this.$miRouter.push({ path: item.jumpPath })
    },
    /**
     * 左侧按钮点击
     */
    leftClick() {
      this.$miRouter.pushTab({ path: '/pages/index/index' })
    }
  }
}
</script>

<style lang="scss" scoped>
.statistics {
  width: 670rpx;
  // height: 438rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  margin: 40rpx;
  .statistics_content {
    margin: 30rpx;
    font-size: 30rpx;
  }
  .statistics_ul {
    width: 574rpx;
    // height: 302rpx;
    margin: auto;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    align-content: space-between;
    flex-wrap: wrap;
    .statistics_li {
      width: 25%;
      height: 132rpx;
      margin-bottom: 40rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      align-content: space-between;
      flex-wrap: wrap;
      .statistics_content_img {
        width: 91rpx;
        height: 92rpx;
        image {
          width: 100%;
          height: 100%;
        }
      }
      .statistics_content_text {
        width: 120rpx;
        height: 30rpx;
        text-align: center;
        font-size: 24rpx;
      }
    }
  }
}
</style>

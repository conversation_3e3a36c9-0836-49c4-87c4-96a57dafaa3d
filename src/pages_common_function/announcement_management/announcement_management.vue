<template>
  <!-- 公告管理 -->
  <view class="announcement_warp">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('announcement')"
      placeholder
      :autoBack="true"
      :leftIconColor="color.navigation"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    ></u-navbar>
    <!--#endif-->
    <mescroll-uni ref="mescrollRef" :fixed="false" :safearea="true" :bottom="0" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="{ auto: false }" :up="{ auto: false }" >
      <view class="announcement" v-for="(item, index) in announcementList" :key="index">
        <view class="announcement_top">
          <image :src="item.image?item.image:imgPath.IMG_INDEX_04" mode="scaleToFill" ></image>
          <view class="announcement_top_information">
            <view class="announcement_top_data">
              <view class="announcement_top_name">{{ item.title }}</view>
              <view class="announcement_top_time">{{ item.post_time }}</view>
            </view>
            <!-- <view class="announcement_top_value">{{ item.sender_name }}</view> -->
          </view>
        </view>
        <view class="announcement_bottom">
          <!-- 判断是否发布 -->
          <view class="announcement_bottom_publish" v-if="item.status=='2'||item.status=='5'">
            <view class="announcement_bottom_publishs">#{{ item.status_alias }}</view>
            <view class="announcement_bottom_lock">
              <image :src="imgPath.IMG_EYE_GRAY" mode="scaleToFill" ></image>
              {{ item.read_count }}
            </view>
          </view>
          <view class="announcement_bottom_publish" v-if="item.status=='0'||item.status == '1'">
            <view class="announcement_bottom_unpublishs">#{{ item.status_alias }}</view>
          </view>
          <view class="announcement_bottom_buts" >
            <u-button
              class="announcement_bottom_custom_style"
              :customStyle="customStyleBtn"
              :plain="true"
              :color="color.themeColor"
              @click="deleteAnnounce(item.msg_no, index)">
              {{ $t('announcement.Delete') }}
            </u-button>
            <u-button class="announcement_bottom_custom_style" v-if="item.status=='1'"
            :customStyle="customStyleBtn" :color="color.themeColor" @click="publishAnnounce(item.msg_no, index)">
              {{ $t('announcement.publish') }}
            </u-button>
          </view>
        </view>
      </view>
    </mescroll-uni>
    <!-- 空白页 -->
    <emptyComponent :emptyContent="emptyContent" v-if="isShowEmptyView"></emptyComponent>
    <view class="botter-but">
      <u-button :color="color.themeColor" @click="announcements">
        {{ $t('announcement.new') }}
      </u-button>
    </view>
     <!--#ifdef MP-WEIXIN || MP-ALIPAY -->
     <CustomDialogComponent ref="customDialog"></CustomDialogComponent>
    <!--#endif-->
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
import { apiBackgroundMessagesMessagesList, apiBackgroundMessagesMessagesDelete, apiBackgroundMessagesMessagesBulkPush } from '@/api/message'
import { deepClone } from '../../utils/util'
import MescrollMixin from '@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js'
import emptyComponent from '@/components/EmptyComponent/EmptyComponent'

export default {
  computed: {
    ...mapGetters(['color'])
  },
  data() {
    return {
      imgPath: this.$imgPath,
      // publish: true, // 判断是否发布
      announcementList: [],
      customStyleBtn: {
        width: '120rpx',
        height: '48rpx',
        borderRadius: '6rpx'
      },
      pageNo: 1, // 页码
      pageSize: 10, // 每页显示数据
      parmas: {}, // 参数
      isShowEmptyView: false, // 是否显示空白内容
      emptyContent: this.$t('tip.list.empty')
    }
  },
  mixins: [MescrollMixin],
  components: { emptyComponent },
  /**
   * 页面加载
   */
  onload(e) {
    console.log('用户列表页面加载', e)
    this.initData()
  },
  created() {
    this.initData()
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      // 获取数据
      console.log('this.parmas', this.parmas)
      this.parmas.page = this.pageNo
      this.parmas.page_size = this.pageSize
      this.getAnnounceList(this.parmas)
    },
    /**
     * 下拉刷新返回
     */
    downCallback(page) {
      console.log(' downCallback page', page)
      this.pageNo = 1
      this.parmas.page = this.pageNo
      this.getAnnounceList(this.parmas)
    },
    /**
     * 上拉加载更多
     * @param {*} page
     */
    upCallback(page) {
      console.log(' upCallback page', page)
      this.pageNo++
      this.parmas.page = this.pageNo
      this.getAnnounceList(this.parmas)
    },
    /**
     * 获取公告列表
     * @param {*} parmas
     */
    async getAnnounceList(parmas) {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      const [err, res] = await this.$to(apiBackgroundMessagesMessagesList(parmas))
      uni.hideLoading()
      if (err) {
        this.mescroll.endErr()
        uni.$u.toast(err.message)
        return
      }
      if (res.code === 0) {
        var data = Reflect.has(res, 'data') ? res.data : {}
        var resultList = Reflect.has(data, 'results') ? data.results : []
        var count = data.count ? data.count : 0
        console.log('data', data, resultList)
        // 没有数据
        this.isShowEmptyView = this.pageNo === 1 && (!resultList || resultList.length === 0)
        if (this.pageNo === 1 && resultList && resultList.length > 0) {
          // 首次加载数据
          console.log('首次加载数据')
          this.announcementList = deepClone(resultList)
        } else if (this.pageNo !== 1 && resultList && resultList.length > 0) {
          // 加载更多数据
          console.log('加载更多数据')
          this.announcementList = this.announcementList.concat(resultList)
        } else {
          // 其他情况
          console.log('其他情况')
        }
        console.log(this.mescroll)
        this.mescroll.setPageNum(this.pageNo)
        this.mescroll.endBySize(this.pageSize, count)
      } else {
        uni.$u.toast(res.msg)
        this.mescroll.endErr()
      }
    },
    /**
     * 删除公告
     * @param  id  传入公告id
     */
    async deleteAnnounce(id) {
      // 点击删除
      this.showConfirmDialog(this.$t('tip.prompt'), this.$t('tip.page.announce.delete.announce'), 'delete', id)
    },
    /**
     * 发布公告
     */
    async publishAnnounce(id) {
      // 点击发布
      this.showConfirmDialog(this.$t('tip.prompt'), this.$t('tip.page.announce.public.announce'), 'public', id)
    },
    /**
     * 发布新公告
     */
    announcements() {
      this.$miRouter.push({
        path: '/pages_common_function/announcement_management/make_new_announcements'
      })
    },
    /**
     * 显示确认窗口
     */
    showConfirmDialog(title, content, type, id) {
      var that = this
      that.$confirm(
        {
          dialogTypeValue: "content", // 弹窗类型
          titleTxt: title, // 弹窗标题
          contentTxt: content,
          isShowDialog: true, // 是否显示弹窗
          cancelCallBack: function() {
            console.log("点击取消");
          },
          confirmCallBack: function(data) {
            console.log("confirmCallback", data);
            if (type === 'delete') {
              that.deleteAnnounceNet(id)
            } else {
              that.publicAnnounceNet(id)
            }
          }
        }, that)
    },
    /**
     * 联网删除公告
     * @param {*} id
     */
    async deleteAnnounceNet(id) {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      const [error, res] = await this.$to(apiBackgroundMessagesMessagesDelete({ msg_no: id }))
      uni.hideLoading()

      if (error) {
        uni.$u.toast(this.$t('announcement.Delete') + " " + this.$t('tip.fail') + error)
        return
      }

      if (res && res.code === 0) {
        console.log("apiBackgroundMessagesMessagesDelete", res)
        uni.$u.toast(this.$t('announcement.Delete') + " " + this.$t('tip.success'))
        setTimeout(() => {
          this.getAnnounceList(this.parmas)
        }, 500)
      } else {
        uni.$u.toast(this.$t('announcement.Delete') + " " + this.$t('tip.fail'))
      }
    },
    /**
     *联网发布公告
     * @param {*} id
     */
    async publicAnnounceNet(id) {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })

      const [error, res] = await this.$to(apiBackgroundMessagesMessagesBulkPush({ msg_nos: [id] }))
      uni.hideLoading()

      if (error) {
        uni.$u.toast(this.$t('announcement.publish') + " " + this.$t('tip.fail') + error)
        return
      }

      if (res && res.code === 0) {
        console.log("apiBackgroundMessagesMessagesBulkPush", res)
        uni.$u.toast(this.$t('announcement.publish') + " " + this.$t('tip.success'))
        setTimeout(() => {
          this.getAnnounceList(this.parmas)
        }, 500)
      } else {
        uni.$u.toast(this.$t('announcement.publish') + " " + this.$t('tip.fail'))
      }
    }

  }
}
</script>

<style lang="scss" scoped>
.announcement_warp {
  height: calc(100vh - 88rpx);
  .announcement {
    width: 670rpx;
    height: 228rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    margin: 40rpx auto;
    .announcement_top {
      width: 670rpx;
      height: 128rpx;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      image {
        width: 91rpx;
        height: 92rpx;
      }
      .announcement_top_information {
        width: 491rpx;
        height: 71rpx;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-evenly;
        align-items: center;
        align-content: space-between;
        .announcement_top_data {
          width: 491rpx;
          height: 30rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .announcement_top_name {
            font-size: 30rpx;
            line-height: 42rpx;
            color: #1d1e20;
          }
          .announcement_top_time {
            font-size: 24rpx;
            color: #93989e;
          }
        }
        .announcement_top_value {
          width: 491rpx;
          height: 24rpx;
          font-size: 24rpx;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          color: #8f9295;
        }
      }
    }
    .announcement_bottom {
      width: 600rpx;
      height: 100rpx;
      margin: auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .announcement_bottom_publish {
        width: 200rpx;
        height: 100rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .announcement_bottom_unpublishs {
          font-size: 24rpx;
          color: #fd953c;
        }
        .announcement_bottom_publishs {
          font-size: 24rpx;
          color: #8f9295;
        }
        .announcement_bottom_lock {
          width: 100rpx;
          font-size: 24rpx;
          color: #8f9295;
          display: flex;
          justify-content: space-evenly;
          align-items: center;
          image {
            width: 27rpx;
            height: 21rpx;
          }
        }
      }
      .announcement_bottom_but {
        width: 260rpx;
        height: 100rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .announcement_bottom_custom_style {
          width: 120rpx;
          height: 48rpx;
          border-radius: 6rpx;
        }
      }
      .announcement_bottom_buts {
        width: 120rpx;
        height: 100rpx;
        display: flex;
        justify-content: space-around;
        align-items: center;
        .announcement_bottom_custom_style {
          width: 120rpx;
          height: 48rpx;
          border-radius: 6rpx;
        }
      }
    }
  }
  .botter-but {
    width: 750rpx;
    height: 107rpx;
    background-color: #ffffff;
    padding: 19rpx 40rpx 18rpx 40rpx;
    position: absolute;
    bottom: 0;
  }
}
</style>

<template>
  <view class="canteen">
    <!-- 自定义导航栏 -->
    <u-navbar
      :title="$t('announcement.new')"
      placeholder
      :autoBack="true"
      :leftIconColor="color.navigation"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    >
      <view class="u-nav-slot" slot="right" @click="rightClick">{{ $t('Select.all') }}</view>
    </u-navbar>
    <!-- 选择区域 -->
    <view class="announcement_selectinput" v-for="(items, index) in dater" :key="items.id">
      <u-collapse>
        <!-- disabled=false  :isLink=false-->
        <u-collapse-item
          :isLink="items.empty === true ? true : false"
          :disabled="items.empty === true ? false : true"
        >
          <template slot="icon">
            <u-checkbox-group shape="circle" @change="checkboxChange(index)">
              <label class="announcement_changeAll">
                <u-checkbox
                  shape="circle"
                  :activeColor="color.themeColor"
                  size="30rpx"
                  :checked="items.checkedall"
                ></u-checkbox>
              </label>
            </u-checkbox-group>
            <view>{{ items.name }}</view>
          </template>
          <view class="announcement_content" v-for="(item, ind) in items.canteenfunctionlist" :key="ind">
            <u-checkbox-group shape="circle" @change="danxuan(index, ind)">
              <label class="announcement_changlist">
                <view class="announcement_changer">
                  <u-checkbox
                    :activeColor="color.themeColor"
                    :checked="item.checked"
                    size="30"
                  ></u-checkbox>
                  <text>{{ item.text }}</text>
                </view>
              </label>
            </u-checkbox-group>
            <!-- <text class="u-collapse-announcement_content">{{ item.text }}</text> -->
          </view>
        </u-collapse-item>
      </u-collapse>
    </view>
    <view class="announcement_bottom_buttem">
      <u-button :color="color.themeColor" @click="publishnow">{{ $t('announcement.Publish.now') }}</u-button>
    </view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  computed: {
    ...mapGetters(['color'])
  },
  data() {
    return {
      finalList: [],
      dater: [
        {
          id: 1,
          name: this.$t('pages.firm.santeen'),
          checkedall: false,
          empty: true,
          // 基本案列数据
          canteenfunctionlist: [
            {
              id: 1,
              checked: false,
              text: '菜谱管理'
            },
            {
              id: 6,
              checked: false,
              text: '存餐管理'
            },
            {
              id: 7,
              checked: false,
              text: '评价建议'
            }
          ]
        },
        {
          id: 2,
          name: this.$t('pages.firm.xanteen'),
          checkedall: false,
          empty: false,
          // 基本案列数据
          canteenfunctionlist: []
        },
        {
          id: 3,
          name: this.$t('pages.firm.southteen'),
          checkedall: false,
          empty: true,
          // 基本案列数据
          canteenfunctionlist: [
            {
              id: 1,
              checked: false,
              text: '菜谱管理'
            },
            {
              id: 6,
              checked: false,
              text: '存餐管理'
            }
          ]
        }
      ]
    }
  },
  mounted() {},
  methods: {
    rightClick() {
      // 点击导航栏全选
      this.dater.forEach(item => {
        item.checkedall = true
        item.canteenfunctionlist.forEach(items => (items.checked = item.checkedall))
      })
      this.computedFinalList()
    },
    //  全选
    checkboxChange(index) {
      this.dater[index].checkedall = !this.dater[index].checkedall
      this.dater[index].canteenfunctionlist.forEach(
        items => (items.checked = this.dater[index].checkedall)
      )
      this.computedFinalList()
    },
    // // 单选
    danxuan(index, ind) {
      this.dater[index].canteenfunctionlist[ind].checked =
        !this.dater[index].canteenfunctionlist[ind].checked
      this.dater[index].checkedall = this.dater[index].canteenfunctionlist.every(
        item => item.checked
      )
      this.computedFinalList()
    },
    publishnow() {
      // 点击发布
      console.log(this.finalList)
    },
    computedFinalList() {
      // 计算最终的值
      this.finalList = []
      this.dater.forEach(item => {
        if (item.canteenfunctionlist) {
          item.canteenfunctionlist.forEach(item2 => {
            if (item2.checked) {
              this.finalList.push({
                choosedValue: {
                  ...item2
                },
                originValue: {
                  ...item
                }
              })
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.canteen {
  .u-nav-slot {
    font-size: 28rpx;
    color: #fd953c;
  }
  .announcement_selectinput {
    width: 670rpx;
    // height: 110rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    margin: 20rpx auto;
    line-height: 80rpx;

    .announcement_changeAll {
      display: flex;
      padding: 30rpx 30rpx;
      font-size: 32rpx;
      color: #1d1e20;
    }
    .announcement_changlist {
      margin: 10rpx 0;
      width: 500rpx;
      font-size: 28rpx;
      color: #8f9295;
      .announcement_changer {
        width: 500rpx;
        margin-left: 48rpx;
        display: flex;
        flex-wrap: wrap;
      }
    }
  }
  .announcement_bottom_buttem {
    width: 750rpx;
    height: 107rpx;
    background-color: #ffffff;
    padding: 19rpx 40rpx 18rpx 40rpx;
    position: absolute;
    bottom: 0;
  }
}
</style>

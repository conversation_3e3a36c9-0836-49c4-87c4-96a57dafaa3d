<template>
  <view class="makenewannouncements">
    <u-navbar
      :title="$t('announcement.new')"
      placeholder
      :autoBack="true"
      :leftIconColor="color.navigation"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    ></u-navbar>
    <view class="makenewannouncements_name">
      <!-- <u-cell-group>
        <u-cell
          :title="$t('announcement.name')"
          :titleStyle="{ 'font-size': '28rpx', color: '#8f9295' }"
          :border="false"
          :isLink="true"
        >
          <text slot="value" class="u-slot-value">{{ $t('announcement.Please.enter') }}</text>
        </u-cell>
      </u-cell-group> -->
      <view class="makenewannouncements_name_name">公告名称</view>
      <view class="makenewannouncements_name_input"><u-input
        placeholder="请输入公告名称"
        border="none"
        :placeholderStyle="placeholderStyle"
        inputAlign="right"
        v-model="announcementName"
        suffixIcon="arrow-right"
      ></u-input></view>
    </view>
    <view class="makenewannouncements_value">
      <view class="makenewannouncements_value_name">{{ $t('announcement.content') }}</view>
      <view class="makenewannouncements_value_data">
        <u-textarea
          v-model="announcementContent"
          confirmType="done"
          :placeholder="$t('announcement.Please.content')"
          height="280rpx"
          ref="textarea"
          maxlength="-1"
        ></u-textarea>
      </view>
    </view>
    <view class="makenewannouncements_image">
      <view class="makenewannouncements_image_name">{{ $t('announcement.Upload.image') }}</view>
      <view class="makenewannouncements_image_data">
        <u-upload
          :fileList="fileList1"
          @afterRead="afterRead"
          @delete="deletePic"
          name="1"
          width="604rpx"
          height="340rpx"
          multiple
          :maxCount="10"
        >
          <view class="makenewannouncements_icon">
            <u-icon name="plus" color="#8f9295" size="128"></u-icon>
            <view>{{ $t('announcement.Click.Upload.image') }}</view>
          </view>
        </u-upload>
      </view>
    </view>
    <view class="makenewannouncements_botter_buoomt">
      <u-button :color="color.themeColor" @click="next">{{ $t('pages.login.Next') }}</u-button>
    </view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  computed: {
    ...mapGetters(['color'])
  },

  data() {
    return {
      announcementName: '', // 公告名称
      placeholderStyle: ' font-size:24rpx;',
      announcementContent: '', // 公告内容
      fileList1: [] // 图片数据
    }
  },
  onReady() {
    // 如果需要兼容微信小程序的话，需要用此写法 文本域
    this.$refs.textarea.setFormatter(this.formatter)
  },
  methods: {
    // 如果需要兼容微信小程序的话，需要用此写法 文本域
    formatter(announcementContent) {
      return announcementContent.replace()
    },
    // 删除图片
    deletePic(event) {
      this[`fileList${event.name}`].splice(event.index, 1)
    },
    // 新增图片
    async afterRead(event) {
      // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
      let lists = [].concat(event.file)
      let fileListLen = this[`fileList${event.name}`].length
      lists.map(item => {
        return this[`fileList${event.name}`].push({
          ...item
          // status: 'uploading',
          // message: '上传中'
        })
      })
      for (let i = 0; i < lists.length; i++) {
        const result = await this.uploadFilePromise(lists[i].url)
        let item = this[`fileList${event.name}`][fileListLen]
        this[`fileList${event.name}`].splice(
          fileListLen,
          1,
          Object.assign(item, {
            status: 'success',
            message: '',
            url: result
          })
        )
        fileListLen++
      }
    },
    uploadFilePromise(url) {
      return new Promise((resolve, reject) => {
        uni.uploadFile({
          url: 'http://**************:8080', // 仅为示例，非真实的接口地址
          filePath: url,
          name: 'file',
          formData: {
            user: 'test'
          },
          success: res => {
            setTimeout(() => {
              resolve(res.data.data)
            }, 1000)
          }
        })
      })
    },
    next() {
      // 点击下一步
      this.$miRouter.push({ path: '/pages_common_function/announcement_management/canteen' })
    }
  }
}
</script>

<style lang="scss" scoped>
.makenewannouncements_name {
  width: 670rpx;
  height: 90rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 40rpx auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx 0 30rpx;
  .makenewannouncements_name_name {
    color: #8f9295;
    font-size: 28rpx;
  }
}
.makenewannouncements_value {
  width: 670rpx;
  height: 438rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 20rpx auto;
  padding: 30rpx;
  overflow: hidden;
  .makenewannouncements_value_name {
    font-size: 28rpx;
    color: #8f9295;
  }
  .makenewannouncements_value_data {
    margin: 20rpx auto;
  }
}
.makenewannouncements_image {
  width: 670rpx;
  height: 451rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 20rpx auto;
  padding: 30rpx;
  overflow: hidden;
  .makenewannouncements_image_name {
    font-size: 28rpx;
    color: #8f9295;
  }
  .makenewannouncements_image_data {
    width: 610rpx;
    height: 344rpx;
    margin: 20rpx auto;
    border: 2rpx dashed #d7d7d7;
    vertical-align: text-bottom;
    .makenewannouncements_icon {
      width: 610rpx;
      height: 344rpx;
      color: #8f9295;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
  }
}
.makenewannouncements_botter_buoomt {
  width: 750rpx;
  height: 107rpx;
  background-color: #ffffff;
  padding: 19rpx 40rpx 18rpx 40rpx;
  position: absolute;
  bottom: 0;
}
</style>

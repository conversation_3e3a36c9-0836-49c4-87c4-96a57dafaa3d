<template>
  <view class="approval_container" >
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('title.application.approval')"
      placeholder
      :autoBack="true"
      :leftIconColor="color.navigation"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    ></u-navbar>
    <!--#endif-->
    <!--头部tab-->
    <view class="approval_tabs">
      <u-tabs lineHeight="5" :current="currentIndex" :list="tabList" @click="tabClick" lineWidth="60rpx" lineColor="#fd953c" :activeStyle="activeStyle" :inactiveStyle="inactiveStyle" :itemStyle="itemStyle"></u-tabs>
    </view>
    <!--中间筛选-->
    <view class="approval_middle_choose">
      <FilterLayoutComponent id="filterLayout" ref="filterLayout" :topHeight="topHeight" :filterDataLayoutList="filterDataList"  @handlerItemClick="handlerDropDownItemClick"></FilterLayoutComponent>

    </view>
    <!--底部列表-->
    <view class="approval_middle_content" v-if="!isShowEmptyView">
     <mescroll-uni ref="mescrollRef" :fixed="false" :safearea="false" :bottom="50" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="{ auto: false }" :up="{ auto: false }" >
      <view class="approval_middle_content_item"  v-for="(item,index) in orderList" :key="index">
        <HorizontalCellComponent :cellStyle="cellStyleTitle" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="item.approvalName" :valueTxt="statusTxt" arrow-direction="right" rightIcon="arrow-right"  />
        <view class="horizontal_line"></view>
        <!-- 每个项目根据不同的类型进行显示  退款申请：order_review  退款申诉：order_appeal 访客申请：visitor_apply 缴费退款 order_jiaofei 补卡申请attendance_supplement 请假申请 attendance_for_leave-->
        <!--申请时间 -->
        <HorizontalCellComponent :cellStyle="cellStyleContent" :titleStyle="titleSamllStyle" :valueStyle="valueSamllStyle" :titleTxt="userData.base.applicationTime" :valueTxt="item.apply_time" arrow-direction="right" rightIcon="arrow-right" />
        <!--剩余处理时间 -->
        <HorizontalCellComponent v-if="item.approval_type=='order_appeal'" :cellStyle="cellStyleContent" :titleStyle="titleSamllStyle" :valueStyle="valueSamllStyle" :titleTxt="userData.base.remainingProcessingTime" valueTxt="--" :isCountDown="item.isCountDown" :times="item.times" arrow-direction="right" rightIcon="arrow-right" />
        <!--处理时间 -->
        <HorizontalCellComponent v-if="item.approval_type=='order_appeal'" :cellStyle="cellStyleContent" :titleStyle="titleSamllStyle" :valueStyle="valueSamllStyle" :titleTxt="userData.base.processingTime" :valueTxt="item.finish_time" arrow-direction="right" rightIcon="arrow-right" />
        <!--审批时间-->
        <HorizontalCellComponent  v-if="item.approval_type=='order_jiaofei'||item.approval_type=='visitor_apply'||item.approval_type=='attendance_for_leave'" :cellStyle="cellStyleContent" :titleStyle="titleSamllStyle" :valueStyle="valueSamllStyle" :titleTxt="userData.base.approvalTime" :valueTxt="item.finish_time" arrow-direction="right" rightIcon="arrow-right" />
        <!--审核时间-->
        <HorizontalCellComponent  v-if="item.approval_type=='order_review'||item.approval_type=='attendance_supplement'" :cellStyle="cellStyleContent" :titleStyle="titleSamllStyle" :valueStyle="valueSamllStyle" :titleTxt="userData.base.auditTime" :valueTxt="item.finish_time" arrow-direction="right" rightIcon="arrow-right" />
        <!--订单类型-->
        <HorizontalCellComponent  v-if="item.approval_type=='order_review'||item.approval_type=='order_appeal'" :cellStyle="cellStyleContent" :titleStyle="titleSamllStyle" :valueStyle="valueSamllStyle" :titleTxt="userData.base.orderType" :valueTxt="item.order_type_alias" arrow-direction="right" rightIcon="arrow-right" />
        <!--缴费项目-->
        <HorizontalCellComponent  v-if="item.approval_type=='order_jiaofei'" :cellStyle="cellStyleContent" :titleStyle="titleSamllStyle" :valueStyle="valueSamllStyle" :titleTxt="userData.base.paymentItems" :valueTxt="item.jiaofei_type_alias" arrow-direction="right" rightIcon="arrow-right" />
        <!--退款类型-->
        <HorizontalCellComponent  v-if="item.approval_type=='order_jiaofei'" :cellStyle="cellStyleContent" :titleStyle="titleSamllStyle" :valueStyle="valueSamllStyle" :titleTxt="userData.base.refundType" :valueTxt="item.refund_type_alias" arrow-direction="right" rightIcon="arrow-right" />
        <!-- 退款申诉 退款类型-->
        <HorizontalCellComponent  v-if="item.approval_type=='order_appeal'" :cellStyle="cellStyleContent" :titleStyle="titleSamllStyle" :valueStyle="valueSamllStyle" :titleTxt="userData.base.refundType" :valueTxt="item.status_alias" arrow-direction="right" rightIcon="arrow-right" />
        <!--退款金额-->
        <HorizontalCellComponent  v-if="item.approval_type=='order_review'" :cellStyle="cellStyleContent" :titleStyle="titleSamllStyle" :valueStyle="valueSamllStyle" :titleTxt="userData.base.refundAmount" :valueTxt="item.real_fee" arrow-direction="right" rightIcon="arrow-right" />
        <!--缴费退款金额-->
        <HorizontalCellComponent  v-if="item.approval_type=='order_jiaofei'" :cellStyle="cellStyleContent" :titleStyle="titleSamllStyle" :valueStyle="valueSamllStyle" :titleTxt="userData.base.refundAmount" :valueTxt="item.refund_fee" arrow-direction="right" rightIcon="arrow-right" />
        <!--退款原因-->
        <HorizontalCellComponent v-if="item.approval_type=='order_review'||item.approval_type=='order_jiaofei'||item.approval_type=='order_appeal'" :cellStyle="cellStyleContent" :titleStyle="titleSamllStyle" :valueStyle="valueSamllStyle" :titleTxt="userData.base.refundReason" :valueTxt="item.reason" arrow-direction="right" rightIcon="arrow-right" />
        <!--请假开始时间-->
        <HorizontalCellComponent v-if="item.approval_type=='attendance_for_leave'" :cellStyle="cellStyleContent" :titleStyle="titleSamllStyle" :valueStyle="valueSamllStyle" :titleTxt="userData.base.leaveStartTime" :valueTxt="item.start_time" arrow-direction="right" rightIcon="arrow-right" />
        <!--请假结束时间-->
        <HorizontalCellComponent v-if="item.approval_type=='attendance_for_leave'" :cellStyle="cellStyleContent" :titleStyle="titleSamllStyle" :valueStyle="valueSamllStyle" :titleTxt="userData.base.leaveEndTime" :valueTxt="item.end_time" arrow-direction="right" rightIcon="arrow-right" />
        <!--请假类型-->
        <HorizontalCellComponent v-if="item.approval_type=='attendance_for_leave'" :cellStyle="cellStyleContent" :titleStyle="titleSamllStyle" :valueStyle="valueSamllStyle" :titleTxt="userData.base.LeaveType" :valueTxt="item.type_name" arrow-direction="right" rightIcon="arrow-right" />
        <!--请假原因-->
        <HorizontalCellComponent v-if="item.approval_type=='attendance_for_leave'" :cellStyle="cellStyleContent" :titleStyle="titleSamllStyle" :valueStyle="valueSamllStyle" :titleTxt="userData.base.leaveReason" :valueTxt="item.reason" arrow-direction="right" rightIcon="arrow-right" />
        <!--来访人员-->
        <HorizontalCellComponent v-if="item.approval_type=='visitor_apply'" :cellStyle="cellStyleContent" :titleStyle="titleSamllStyle" :valueStyle="valueSamllStyle" :titleTxt="userData.base.visiors" :valueTxt="userData.base.sexTxt" arrow-direction="right" rightIcon="arrow-right" />
        <!--访客类型-->
        <HorizontalCellComponent v-if="item.approval_type=='visitor_apply'" :cellStyle="cellStyleContent" :titleStyle="titleSamllStyle" :valueStyle="valueSamllStyle" :titleTxt="userData.base.visitorType" :valueTxt="userData.base.sexTxt" arrow-direction="right" rightIcon="arrow-right" />
        <!--来访时间-->
        <HorizontalCellComponent v-if="item.approval_type=='visitor_apply'" :cellStyle="cellStyleContent" :titleStyle="titleSamllStyle" :valueStyle="valueSamllStyle" :titleTxt="userData.base.visitingTime" :valueTxt="userData.base.sexTxt" arrow-direction="right" rightIcon="arrow-right" />
        <!--备注-->
        <HorizontalCellComponent v-if="item.approval_type=='visitor_apply'" :cellStyle="cellStyleContent" :titleStyle="titleSamllStyle" :valueStyle="valueSamllStyle" :titleTxt="userData.base.remarks" :valueTxt="userData.base.sexTxt" arrow-direction="right" rightIcon="arrow-right" />
        <!--缺卡时间-->
        <HorizontalCellComponent v-if="item.approval_type=='attendance_supplement'" :cellStyle="cellStyleContent" :titleStyle="titleSamllStyle" :valueStyle="valueSamllStyle" :titleTxt="userData.base.lackOfCardsTime" :valueTxt="item.absence_work_time" arrow-direction="right" rightIcon="arrow-right" />
        <!--补卡原因-->
        <HorizontalCellComponent v-if="item.approval_type=='attendance_supplement'" :cellStyle="cellStyleContent" :titleStyle="titleSamllStyle" :valueStyle="valueSamllStyle" :titleTxt="userData.base.cardReplacementReason" :valueTxt="item.reason" arrow-direction="right" rightIcon="arrow-right" />

        <view class="approval_btns_content">
            <u-button :customStyle="customStyleBtn" class="approval_btn_style" plain :color="color.colorBtnOrange" @click="viewDetail(item)">{{ viewDetailTxt }}</u-button>
            <u-button :customStyle="customStyleBtn" v-if="isShowBtn&&item.approval_type!=='order_appeal'" class="approval_btn_style" plain :color="color.colorBtnOrange" @click="handlerRefuse(item)">{{ refuseTxt }}</u-button>
            <u-button :customStyle="customStyleBtn" v-if="isShowBtn&&item.approval_type!=='order_appeal'" class="approval_btn_style" :color="color.colorBtnOrange" @click="handlerAgree(item)">{{ agreeTxt }}</u-button>
            <u-button :customStyle="customStyleBtn" v-if="isShowBtn&&item.approval_type=='order_appeal'" class="approval_btn_style " plain :color="color.colorBtnOrange" @click="handlerOrderRefuse(item,'ORDER_REJECT')">{{ wholeOrderRejectionTxt }}</u-button>
            <u-button :customStyle="customStyleBtn" v-if="isShowBtn&&item.approval_type=='order_appeal'" class="approval_btn_style " plain :color="color.colorBtnOrange" @click="handlerOrderRefuse(item,'ALL_ORDER_REFUND')">{{ wholeOrderRefundTxt }}</u-button>
            <u-button :customStyle="customStyleBtn" v-if="isShowBtn&&item.approval_type=='order_appeal'" class="approval_btn_style " :color="color.colorBtnOrange" @click="handlerOrderRefuse(item,'FINISH')">{{ completeProcessingTxt }}</u-button>

          </view>
      </view>
    </mescroll-uni>
    </view>
    <!-- 空白页 -->
    <EmptyComponent :emptyContent="emptyContent" v-if="isShowEmptyView"></EmptyComponent>
    <!--#ifdef MP-WEIXIN || MP-ALIPAY -->
    <CustomDialogComponent ref="customDialog"></CustomDialogComponent>
    <!--#endif-->
  </view>
</template>

<script>
import { apiMerchantMobileApprovalGetApprovalList } from '@/api/order'
import { mapGetters } from 'vuex'
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import comDic from '../../common/comDic'
import { getLastDayRange, deepClone, divide } from '@/utils/util'
import cache from '@/utils/cache'
import { modifyApprovalDialog, processOrderApprovalDialog, modifyPaymentDialog, modifyLeaveDialog, modifyCardReplacementDialog } from '@/utils/userUtil'

export default {
  data() {
    return {
      topHeight: 0,
      currentIndex: 0,
      customStyleBtn: {
        width: "120rpx",
        height: "48rpx",
        padding: 0,
        margin: "0 10rpx",
        fontSize: "24rpx"
      },
      cellStyleTitle: { height: "80rpx" },
      cellStyleContent: { height: "48rpx" },
      titleStyle: { "font-size": "28rpx", color: "#1d1e20" },
      valueStyle: { "font-size": "24rpx", color: "#fd953c" },
      valueOrangeStyle: { "font-size": "24rpx", color: "#8f9295" },
      valueGrayStyle: { "font-size": "24rpx", color: "#8f9295" },
      valueRedStyle: { "font-size": "24rpx", color: "#ff5757" },
      valueSamllStyle: { "font-size": "24rpx", color: "#1d1e20" },
      titleSamllStyle: { "font-size": "24rpx", color: "#8f9295" },
      viewDetailTxt: this.$t("page.user.view.detail"),
      refuseTxt: this.$t("page.approval.btn.refuse"),
      agreeTxt: this.$t("page.approval.btn.agree"),
      isShowBtn: true,
      statusTxt: "待审批",
      tabList: [{
        name: "待审批"
      }, {
        name: "已同意"
      }, {
        name: "已拒绝"
      }
      ],
      filterDataList: [
        {
          title: this.$t("dic.date.all.time"),
          chooseItem: this.$t("dic.date.all.time"),
          dataList: comDic.DIC_APPROVAL_DATE_LIST
        },
        {
          title: this.$t("dic.date.all.type"),
          chooseItem: this.$t("dic.date.all.type"),
          dataList: []
        }
      ],
      userData: {
        head: {
          status: "使用中",
          name: "诸葛明亮",
          mobile: "1684****877"
        },
        base: {
          title: "缴费退款",
          applicationTime: this.$t("page.approval.application.time"),
          approvalTime: this.$t("page.approval.approval.time"),
          remainingProcessingTime: this.$t("page.approval.remaining.processing.time"),
          processingTime: this.$t("page.approval.processing.time"),
          auditTime: this.$t("page.approval.audit.time"),
          orderType: this.$t("page.approval.order.type"),
          paymentItems: this.$t("page.approval.payment.items"),
          refundType: this.$t("page.approval.refund.type"),
          refundAmount: this.$t("page.approval.refund.amount"),
          refundReason: this.$t("page.approval.refund.reason"),
          leaveStartTime: this.$t("page.approval.leave.start.time"),
          leaveEndTime: this.$t("page.approval.leave.end.time"),
          LeaveType: this.$t("page.approval.leave.type"),
          leaveReason: this.$t("page.approval.leave.reason"),
          visiors: this.$t("page.approval.visitors"),
          visitorType: this.$t("page.approval.visitor.type"),
          visitingTime: this.$t("page.approval.visiting.time"),
          remarks: this.$t("page.invitation.remarks"),
          lackOfCardsTime: this.$t("page.approval.lack.of.cards.time"),
          cardReplacementReason: this.$t("page.approval.card.replacement.reason"),
          nameTxt: "诸葛明亮",
          sexTxt: "女",
          birthdayTxt: "1990-10",
          mobileTxt: "158*****5545"
        }
      },
      orderList: [],
      freezeAccounTxt: this.$t("page.user.freeze.account"),
      lossReportingTxt: this.$t("page.user.card.loss.reporting"),
      uploadFaceTxt: this.$t("page.user.upload.face"),
      itemStyle: { "font-size": "30rpx", margin: "26rpx 0" },
      activeStyle: { color: "#fd953c", fontWeight: "bold", transform: "scale(1.05)" },
      inactiveStyle: { color: "#8f9295" },
      pageNo: 1,
      pageSize: 10,
      parmas: {},
      isShowEmptyView: false,
      emptyContent: this.$t("tip.list.empty"),
      startDate: "",
      endDate: "",
      approvalType: "",
      approvalTypeTitle: "",
      approvalStatus: 0,
      wholeOrderRejectionTxt: this.$t("page.approval.whole.order.rejection"),
      wholeOrderRefundTxt: this.$t("page.approval.whole.order.refund"),
      completeProcessingTxt: this.$t("page.approval.complete.processing") // 完成处理 按钮名字
    };
  },
  mixins: [MescrollMixin],
  computed: {
    ...mapGetters(["color"])
  },
  created() {
    this.initData();
  },
  /**
     * 页面加载
     */
  onload(e) {
    console.log("申请审批", e);
    // #ifdef MP-WEIXIN || MP-ALIPAY
    this.msgListener();
    // #endif
  },
  onUnload() {
    // #ifdef MP-WEIXIN || MP-ALIPAY
    uni.$off(this.$common.MSG_APPROVAL_SUCCESS);
    // #endif
  },
  mounted() {
    console.log("mounted");
    // #ifdef H5
    this.msgListener();
    // #endif
  },
  destroyed() {
    // #ifdef H5
    // 销毁通知首页刷新
    uni.$emit(this.$common.MSG_UPDATE_UNREAD_BACK, "申请返回")
    uni.$off(this.$common.MSG_APPROVAL_SUCCESS);
    // #endif
  },
  methods: {
    /**
         * 初始化数据
         */
    initData() {
      // 设置距离顶部高度
      this.topHeight = uni.upx2px(90);
      // 获取字典
      this.getDicList();
      // 获取订单数据
      // var typeId = this.filterDataList[1].dataList[0].value || ''
      // this.parmas.typeId = typeId
      this.parmas.page = this.pageNo;
      this.parmas.page_size = this.pageSize;
      this.parmas.status = "not_approved";
      this.getOrderList(this.parmas);
    },
    /**
         * 信息监听
         */
    msgListener() {
      var that = this;
      uni.$on(this.$common.MSG_APPROVAL_SUCCESS, data => {
        console.log("data", data);
        that.getOrderList(that.parmas);
      });
    },
    /**
         * 下拉刷新返回
         */
    downCallback(page) {
      console.log(" downCallback page", page);
      this.pageNo = 1;
      this.parmas.page = this.pageNo;
      this.getOrderList(this.parmas);
    },
    /**
         * 上拉加载更多
         * @param {*} page
         */
    upCallback(page) {
      console.log(" upCallback page", page);
      this.pageNo++;
      this.parmas.page = this.pageNo;
      this.getOrderList(this.parmas);
    },
    /**
         * 获取字典列表
         */
    async getDicList() {
      var filterList = deepClone(this.filterDataList);
      // 获取该账户的权限信息判断
      var allTypeList = comDic.DIC_APPROVAL_TYPE_LIST || [];
      var newTypeList = [];
      // allTypeList.forEach(item => {
      //   if (this.$hasPermissionKey(item.key)) {
      //     newTypeList.push(item);
      //   }
      // });
      newTypeList = allTypeList
      newTypeList.unshift({ name: this.$t("dic.date.all.type"), value: "", key: "" });
      console.log("newTypeList", newTypeList);
      if (newTypeList.length > 0) {
        // 赋值
        filterList[1].dataList = newTypeList;
        filterList[1].title = newTypeList[0].name;
        filterList[1].chooseItem = newTypeList[0].name;
        this.approvalType = newTypeList[0].value;
        this.approvalTypeTitle = newTypeList[0].name;
        this.userData.base.title = newTypeList[0].name;
      }
      this.filterDataList = deepClone(filterList);
    },
    /**
         * 获取订单列表
         * @param {*} parmas
         */
    async getOrderList(parmas) {
      this.$showLoading({
        title: this.$t("tip.loading"),
        mask: true
      });
      const [error, res] = await this.$to(apiMerchantMobileApprovalGetApprovalList(parmas));
      if (error) {
        this.mescroll.endErr();
        this.isShowEmptyView = true
        uni.$u.toast(error.message);
        return;
      }
      if (res.code === 0) {
        uni.hideLoading();
        var data = Reflect.has(res, "data") ? res.data : [];
        var resultList = Reflect.has(data, "results") ? data.results : [];
        var count = data.count ? data.count : 0;
        // 这里要进行处理，数据在order_payment 里面,利用循环把他放出来
        if (resultList) {
          resultList.map(item => {
            var nameItem = comDic.DIC_APPROVAL_TYPE_LIST.find(subItem => {
              return item.approval_type === subItem.value;
            });
            item.approvalName = nameItem.name || "";
            item.origin_fee = "￥" + divide(item.origin_fee);
            item.pay_fee = "￥" + divide(item.pay_fee);
            item.subsidy_fee = "￥" + divide(item.subsidy_fee);
            item.wallet_fee = "￥" + divide(item.wallet_fee);
            item.complimentary_fee = "￥" + divide(item.complimentary_fee);
            item.subsidy_balance = "￥" + divide(item.subsidy_balance);
            item.wallet_balance = "￥" + divide(item.wallet_balance);
            item.complimentary_balance = "￥" + divide(item.complimentary_balance);
            item.refund_fee = "￥" + divide(item.refund_fee);
            item.real_fee = "￥" + divide(item.real_fee);
            if (Reflect.has(item, "residual_time")) {
              // 处理剩余时间
              console.log("this.currentIndex", this.approvalStatus);
              item.isCountDown = this.approvalStatus === 0
              item.times = this.getCountTime(item.residual_time)
            }
            return item;
          });
        }
        console.log("data", data, resultList);
        // 没有数据
        this.isShowEmptyView = this.pageNo === 1 && (!resultList || resultList.length === 0);
        if (this.pageNo === 1 && resultList && resultList.length > 0) {
          // 首次加载数据
          console.log("首次加载数据");
          this.orderList = deepClone(resultList);
        } else if (this.pageNo !== 1 && resultList && resultList.length > 0) {
          // 加载更多数据
          console.log("加载更多数据");
          this.orderList = this.orderList.concat(resultList);
        } else {
          // 其他情况
          console.log("其他情况");
          this.orderList = [];
          uni.hideLoading();
          uni.$u.toast(res.msg !== "成功" ? res.msg : "暂无数据");
        }
        this.mescroll.setPageNum(this.pageNo)
        this.mescroll.endBySize(this.pageSize, count);
      } else {
        this.orderList = [];
        uni.hideLoading();
        uni.$u.toast(res.msg);
        this.mescroll.endErr();
      }
    },
    /**
         * tab切换监听
         * @param {*} e
         */
    tabClick(e) {
      var index = e.index;
      console.log("tabClick", index);
      this.statusTxt = e.name;
      this.approvalStatus = index;
      switch (index) {
        case 0: // 待审批
          this.valueStyle = this.valueOrangeStyle;
          this.isShowBtn = true;
          this.parmas.status = "not_approved";
          break;
        case 1: // 已同意
          this.valueStyle = this.valueGrayStyle;
          this.isShowBtn = false;
          this.parmas.status = "agree";
          break;
        case 2: // 已拒绝
          this.valueStyle = this.valueRedStyle;
          this.isShowBtn = false;
          this.parmas.status = "reject";
          break;
        default:
          break;
      }
      this.pageNo = 1;
      this.parmas.page = 1;
      this.getOrderList(this.parmas);
    },
    /**
         * 查看详情
         * @param {*} itemData 每一项的数据
         */
    viewDetail(itemData) {
      console.log("viewDetail", itemData);
      itemData.approvalType = this.approvalType;
      itemData.approvalTypeTitle = this.approvalTypeTitle;
      itemData.approvalStatus = this.approvalStatus;
      itemData.isShowBtn = this.isShowBtn;
      cache.set(this.$common.KEY_APPROVAL_ITEM_INFO, itemData);
      this.$miRouter.push({
        path: "/pages_common_function/approval/approval_detail",
        query: {
          id: itemData.id,
          status: itemData.approval_type
        }
      });
    },
    /**
         * 拒绝
         * @param {*} e
         */
    async handlerRefuse(e) {
      console.log("handlerRefuse", e);
      var refundMsg = this.$t("tip.please.fill.in.the.reason.for.rejection");
      if (e.approval_type === "attendance_for_leave") {
        refundMsg = this.$t("tip.are.you.sure.you.reject.to.the.leave.application");
      }
      if (e.approval_type === "attendance_supplement") {
        refundMsg = this.$t("tip.are.you.sure.you.reject.to.the.replacemen.card.application");
      }
      this.modifyApprovalStatus(e.id, false, refundMsg, e.approval_type);
    },
    /**
         * 同意
         * @param {*} e
         */
    async handlerAgree(e) {
      console.log("handlerAgree", e);
      var successMsg = this.$t("tip.are.you.sure.you.agree");
      if (e.approval_type === "attendance_for_leave") {
        successMsg = this.$t("tip.are.you.sure.you.agree.to.the.leave.application");
      }
      if (e.approval_type === "attendance_supplement") {
        successMsg = this.$t("tip.are.you.sure.you.agree.to.the.replacemen.card.application");
      }
      this.modifyApprovalStatus(e.id, true, successMsg, e.approval_type);
    },
    /**
         * 修改订单状态
         * @param {*} isAgree  同意/拒绝
         */
    async modifyApprovalStatus(id, isAgree, content, type) {
      var [error, res] = [];
      if (type === "order_review") {
        [error, res] = await this.$to(modifyApprovalDialog(id, isAgree, content, this));
      }
      if (type === "order_jiaofei") {
        [error, res] = await this.$to(modifyPaymentDialog(id, isAgree, content, this));
      }
      if (type === "attendance_for_leave") {
        [error, res] = await this.$to(modifyLeaveDialog(id, isAgree, content, this));
      }
      if (type === "attendance_supplement") {
        [error, res] = await this.$to(modifyCardReplacementDialog(id, isAgree, content, this));
      }
      if (res && res === "修改成功" && !error) {
        setTimeout(() => {
          this.getOrderList(this.parmas);
        }, 500);
      }
    },
    /**
         *头部筛选下拉选项点击
         * @param {*} itemData 点击项数据
         * @param index 点击的项目
         */
    handlerDropDownItemClick(itemData, index) {
      console.log("handlerDropDownItemClick", itemData);
      // 点击记录赋值
      this.filterDataList[index].chooseItem = itemData.name;
      this.filterDataList[index].title = itemData.name;
      this.approvalType = itemData.value;
      this.approvalTypeTitle = itemData.name;
      this.userData.base.title = itemData.name;
      switch (index) {
        case 0: // 时间点击
          var timeNum = itemData.number;
          var dateList = getLastDayRange(timeNum, "{y}-{m}-{d}", true);
          this.dateTitle = dateList.join("至");
          this.startDate = dateList[0] || "";
          this.endDate = dateList[1] || "";
          this.parmas.start_date = dateList[0] || "";
          this.parmas.end_date = dateList[1] || "";
          if (itemData.number === 0) {
            delete this.parmas.start_date
            delete this.parmas.end_date
          }
          break;
        case 1: // 全部类型
          this.parmas.approval_type = itemData.value;
          if (itemData.name === "全部类型") {
            delete this.parmas.approval_type;
          }
          break;
        default:
          break;
      }
      this.pageNo = 1;
      this.parmas.page = 1;
      this.getOrderList(this.parmas);
    },
    /**
         * 订单修改退款
         * @param itemData 项目信息
         */
    handlerOrderRefuse(itemData, type) {
      console.log("handlerOrderRefuse", itemData, type);
      switch (type) {
        case "ORDER_REJECT":
          this.processOrderApproval(itemData.trade_no, itemData.id, itemData.real_fee, type, this.$t("page.approval.whole.order.rejection"), this.$t("page.approval.order.rejection.reason"), "", true, false, true);
          break;
        case "ALL_ORDER_REFUND":
          var refundAmount = this.$t("page.approval.refundable.amount") + "：" + divide(itemData.real_fee);
          this.processOrderApproval(itemData.trade_no, itemData.id, itemData.real_fee, type, this.$t("page.approval.whole.order.refund"), refundAmount, this.$t("page.approval.refund.amount"), true, true, false);
          break;
        case "FINISH":
          this.processOrderApproval(itemData.trade_no, itemData.id, itemData.real_fee, type, this.$t("page.approval.complete.processing"), this.$t("tip.whether.or.not") + this.$t("page.approval.complete.processing"), "", false, false, false);
          break;
        default:
          break;
      }
    },
    /**
         * 处理退款状态
         * @param {*} isAgree  同意/拒绝
         */
    async processOrderApproval(tradeNo, orderAppealId, realFee, type, title, inputTitle, inputLeftTip, isShowInputTitle, isShowInputLeft, isTxtArea) {
      var [error, res] = await this.$to(processOrderApprovalDialog(tradeNo, orderAppealId, realFee, null, type, title, inputTitle, inputLeftTip, isShowInputTitle, isShowInputLeft, isTxtArea, this));
      if (res && res === "修改成功" && !error) {
        setTimeout(() => {
          this.getOrderList(this.parmas);
        }, 500);
      }
    },
    /**
     * 获取剩余时间
     */
    getCountTime(time) {
      if (!time) {
        return 0
      }
      var endTime = time
      var countTime = 0

      if (typeof endTime === 'string' && endTime.indexOf("-") !== -1) {
        endTime = endTime.replace(/-/g, ',')
        console.log("endTime", endTime);
        endTime = new Date(endTime).getTime()
        console.log("endTime", endTime);
      }

      let currentTime = new Date().getTime()
      if (endTime > currentTime) {
        countTime = endTime - currentTime
      }
      return countTime
    }

  }
}
</script>

<style lang="scss" scoped>

.approval_container{
  font-size: 28rpx;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;

  .approval_tabs{
    height: 90rpx;
    padding: 0 40rpx;
    background-color: #ffffff;

  }
  .approval_middle_choose{
    margin-top: 1px;
  }
  // #ifdef MP-WEIXIN
  .approval_middle_content{
    padding-top:20rpx;
    height: calc(100% - 350rpx);
    padding-bottom: 50rpx;
  }
  // #endif
   // #ifdef H5
   .approval_middle_content{
    padding-top:20rpx;
    height: calc(100% - 258rpx);
  }
  // #endif
  .approval_middle_content_item{
    display: inline-block;
    width: 670rpx;
    min-height: 100rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    margin :20rpx 40rpx;
  }
  .horizontal_line{
    height: 1px;
    background-color: #eae9ed;
    margin:0 30rpx 10rpx;
  }

  .approval_btns_content{
    height: 48rpx;
    display: flex;
    justify-content: flex-end;
    margin:  20rpx  20rpx;
    flex-wrap: wrap;
    .approval_btn_style{
      width: 120rpx ;
      height: 48rpx ;
      border-radius: 6rpx;
      font-size: 24rpx;
      margin: 0 10rpx;
      padding: 0 !important;
    }
  }

}
</style>

<template>
  <view class="approval_detail_container">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('title.details')"
      placeholder
      :autoBack="true"
      :leftIconColor="color.navigation"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    ></u-navbar>
    <!--#endif-->
    <!--头部 标签-->

    <view class="approval_detail_title">
      <view :class="['approval_detail_title_top',approvalStatus==0?'background_color_orange':'',approvalStatus==1?'background_color_green':'',approvalStatus==2?'background_color_red':'']"></view>
      <image :src="titleIcon" mode="scaleToFill" ></image>
      <view :class="[approvalStatus==0?'color_orange':'',approvalStatus==1?'color_green':'',approvalStatus==2?'color_red':'']">{{ titleTxt }}</view>
    </view>
    <!--订单头部信息-->
    <view class="approval_detail_content_title">{{ approvalTypeTitle }}</view>
    <view class="approval_detail_content inline">
      <!--缴费退款 -->
      <!--订单类型-->
      <horizontalCellComponent v-if="approvalType=='order_jiaofei'" :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.approvalOrderTypeTxt" :valueTxt="userData.info.jiaofei_type_alias" />
      <!--退款金额-->
      <horizontalCellComponent v-if="approvalType=='order_jiaofei'" :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.refundAmountTxt" :valueTxt="userData.info.refund_fee"  />
      <!--退款原因-->
      <horizontalCellComponent v-if="approvalType=='order_jiaofei'" :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.refundReasonTxt" :valueTxt="userData.info.reason"/>

      <!--订单类型-->
      <horizontalCellComponent v-if="approvalType == 'order_review'" :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.approvalOrderTypeTxt" :valueTxt="userData.info.order_type_alias" />
      <!--退款金额-->
      <horizontalCellComponent v-if="approvalType == 'order_review'" :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.refundAmountTxt" :valueTxt="userData.info.real_fee"  />
      <!--退款原因-->
      <horizontalCellComponent v-if="approvalType == 'order_review'" :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.refundReasonTxt" :valueTxt="userData.info.reason"/>

      <!--退款申诉 start-->
      <!--订单类型-->
      <horizontalCellComponent v-if="approvalType == 'order_appeal'" :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.approvalOrderTypeTxt" :valueTxt="userData.info.order_type_alias" />
      <!--退款类型-->
      <horizontalCellComponent v-if="approvalType == 'order_appeal'" :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.refundTypeTxt" :valueTxt="userData.info.status_alias" />
      <!--退款金额-->
      <!-- <horizontalCellComponent v-if="approvalType == 'order_appeal'"  :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.refundAmountTxt" :valueTxt="userData.info.real_fee"  /> -->
      <!--退款原因-->
      <horizontalCellComponent v-if="approvalType == 'order_appeal'" :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.refundReasonTxt" :valueTxt="userData.info.reason"/>
     <!--退款申诉 end-->

      <!--请假申请 补卡申请 start-->
       <!--请假类型-->
      <horizontalCellComponent v-if="approvalType=='attendance_for_leave'" :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.LeaveType" :valueTxt="userData.info.type_name" arrow-direction="right" rightIcon="arrow-right" />
      <!--请假开始时间-->
      <horizontalCellComponent v-if="approvalType=='attendance_for_leave'" :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.leaveStartTime" :valueTxt="userData.info.start_time" arrow-direction="right" rightIcon="arrow-right" />
      <!--请假结束时间-->
      <horizontalCellComponent v-if="approvalType=='attendance_for_leave'" :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.leaveEndTime" :valueTxt="userData.info.end_time" arrow-direction="right" rightIcon="arrow-right" />
      <!--请假原因-->
      <horizontalCellComponent v-if="approvalType=='attendance_for_leave'" :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.leaveReason" :valueTxt="userData.info.reason" arrow-direction="right" rightIcon="arrow-right" />
      <!--补卡类型-->
      <horizontalCellComponent v-if="approvalType=='attendance_supplement'" :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.cardReplacementTypeTxt" :valueTxt="userData.base.sexTxt" arrow-direction="right" rightIcon="arrow-right" />
      <!--缺卡时间-->
      <horizontalCellComponent v-if="approvalType=='attendance_supplement'" :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.lackOfCardsTime" :valueTxt="userData.base.sexTxt" arrow-direction="right" rightIcon="arrow-right" />
      <!--补卡原因-->
      <horizontalCellComponent v-if="approvalType=='attendance_supplement'" :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.cardReplacementReason" :valueTxt="userData.base.sexTxt" arrow-direction="right" rightIcon="arrow-right" />

      <!--图片-->
      <view class="approval_detail_reply_title" v-if="approvalType=='attendance_for_leave'||approvalType=='attendance_supplement'">{{ userData.base.replyPictureTxt+"：" }}</view>
        <view class="approval_detail_reply_pic" v-if="approvalType=='attendance_for_leave'||approvalType=='attendance_supplement'">
          <view  class="approval_detail_img" v-for="(item,index) in userData.info.feedback_images" :key="index">
            <u-image   :src="item" :lazy-load="true" radius="8rpx" width="90rpx" height="90rpx"></u-image>
          </view>
        </view>
      <!--请假申请 补卡申请 end-->

    </view>
    <!--人员信息-->
    <view class="approval_detail_content_title">{{ userData.base.personnelInformationTxt }}</view>
    <view class="approval_detail_content inline">
      <horizontalCellComponent :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.userNameTxt" :valueTxt="userData.info.name" />
      <horizontalCellComponent :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.personnelNoTxt" :valueTxt="userData.info.person_no" />
      <horizontalCellComponent :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.departmentTxt" :valueTxt="userData.info.payer_department_group_name" />
      <horizontalCellComponent :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.mobileTxt" :valueTxt="userData.info.phone" />
       <!--所属考勤组-->
      <horizontalCellComponent v-if="approvalType=='attendance_supplement'" :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.attendanceGroupTxt" :valueTxt="userData.info.phone" />

    </view>
    <!--订单信息 请假跟补卡没有订单信息-->
    <view class="approval_detail_content_title" v-if="approvalType == 'order_jiaofei'||approvalType == 'order_appeal'||approvalType == 'order_review'">{{userData.base.orderInformationTxt }}</view>

    <view class="approval_detail_content inline" v-if="approvalType == 'order_jiaofei'||approvalType == 'order_appeal'">
      <!--缴费退款 start-->
      <horizontalCellComponent v-if="approvalType=='order_jiaofei'"  :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.paymentOrderTxt" :valueTxt="userData.info.trade_no" />
      <horizontalCellComponent v-if="approvalType=='order_jiaofei'"  :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.paymentItemsTxt" :valueTxt="userData.info.jiaofei_name" />
      <horizontalCellComponent v-if="approvalType=='order_jiaofei'"  :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.paymentTypeTxt" :valueTxt="userData.info.jiaofei_type_alias" />
      <horizontalCellComponent v-if="approvalType=='order_jiaofei'"  :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.paymentAmountTxt" :valueTxt="userData.info.jiaofei_pay_fee" />
      <horizontalCellComponent v-if="approvalType=='order_jiaofei'"  :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.orderTimeTxt" :valueTxt="userData.info.create_time" />
      <horizontalCellComponent v-if="approvalType=='order_jiaofei'"  :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.paymentTimeTxt" :valueTxt="userData.info.pay_time" />
      <horizontalCellComponent v-if="approvalType=='order_jiaofei'"  :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.paymentDateTxt" :valueTxt="userData.info.finish_time" />
      <!--缴费退款 end-->

      <!--退款申诉 start-->
      <!--订单号 -->
      <horizontalCellComponent v-if="approvalType == 'order_appeal'"  :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.orderNumberTxt" :valueTxt="userData.info.trade_no" />
      <!--实收金额 -->
      <horizontalCellComponent v-if="approvalType == 'order_appeal'"  :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.approvalAmountReceivedTxt" :valueTxt="userData.info.pay_fee" />
      <!--下单时间 -->
      <horizontalCellComponent v-if="approvalType == 'order_appeal'"  :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.orderTimeTxt" :valueTxt="userData.info.pay_time" />
      <!--预约时间 -->
      <horizontalCellComponent v-if="approvalType == 'order_appeal'"  :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.approvalAppointmentTimeTxt" :valueTxt="userData.info.reservation_date" />
      <!--退款申诉 end-->

    </view>
     <!--退款申请 start-->
     <view class="approval_detail_content_layout"  v-for="(paymentItem,paymentIndex) in userData.info.order_payment_list" :key="paymentIndex" :show="approvalType == 'order_review'">
     <view class="approval_detail_content inline"  >
      <!--订单号 -->
      <horizontalCellComponent :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.orderNumberTxt" :valueTxt="paymentItem.trade_no" />
      <!--实收金额 -->
      <horizontalCellComponent :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.approvalAmountReceivedTxt" :valueTxt="paymentItem.pay_fee" />
      <!--下单时间 -->
      <horizontalCellComponent :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.orderTimeTxt" :valueTxt="paymentItem.pay_time" />
      <!--预约时间 -->
      <horizontalCellComponent :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.approvalAppointmentTimeTxt" :valueTxt="paymentItem.reservation_date" />
    </view>
    </view>
    <!--退款申请 end-->
    <!--退款菜品 start-->
    <view class="approval_detail_content_layout"  v-for="(orderItem,orderIndex) in userData.info.food_info" :key="orderIndex" :show="approvalType == 'order_appeal'">
    <view class="approval_detail_goods">
      <image class="goods_img" :src="orderItem.food_img" mode="scaleToFill" ></image>
      <view class="goods_content">
        <view class="goods_content_name">{{ orderItem.food_name}}</view>
        <view class="goods_content_amount">{{userData.base.refundableAmountTxt}}：¥{{orderItem.real_fee}}</view>
        <view class="goods_content_amount">{{userData.base.actualRefundAmountTxt}}：¥{{orderItem.real_refund_fee}}</view>
      </view>
      <view class="goods_btn" v-if="isShowBtn">
        <view class="color_blue" @click="handlerOrderRefuseSigle(orderItem,'ORDER_REFUND')">{{userData.base.modifyRefundAmountTxt }}</view>
        <view class="color_orange text-right" @click="handlerOrderRefuseSigle(orderItem,'ORDER_REJECT')">{{userData.base.orderRejectionTxt }}</view>
      </view>
    </view>
    <!--退款菜品 end-->
  </view>
  <!-- 空白页 -->
  <EmptyComponent :emptyContent="emptyContent" v-if="isShowEmptyView"></EmptyComponent>
     <!--审批结果-->
     <view class="approval_detail_content_title" v-if="approvalStatus!=0&&approvalType!=='order_appeal'">{{userData.base.approvalResultsTxt}}</view>
     <view class="approval_detail_content inline" v-if="approvalStatus!=0&&approvalType!=='order_appeal'">
        <horizontalCellComponent :cellStyle="cellStyleContent" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.base.approvalContentTxt" :valueTxt="approvalContent" />
     </view>
    <view class="approval_detail_btns_layout" v-if="isShowBtn">
      <view class="approval_detail_btns" v-if="approvalType !== 'order_appeal'" >
        <u-button :customStyle="customStyleBtn"  class="approval_btn_style" plain :color="color.colorBtnOrange" @click="handlerRefuse(userData.info)">{{ refuseTxt }}</u-button>
        <u-button :customStyle="customStyleBtn"  class="approval_btn_style" :color="color.colorBtnOrange" @click="handlerAgree(userData.info)">{{ agreeTxt }}</u-button>
      </view>
      <!-- 退款申诉按钮层-->
      <view class="approval_detail_btns" v-if="approvalType == 'order_appeal'">
        <u-button :customStyle="customStyleBtn"  class="approval_btn_style btn_width_210" plain :color="color.colorBtnOrange" @click="handlerOrderRefuse(userData.info,'ORDER_REJECT')">{{ wholeOrderRejectionTxt }}</u-button>
        <u-button :customStyle="customStyleBtn"  class="approval_btn_style btn_width_210" plain :color="color.colorBtnOrange" @click="handlerOrderRefuse(userData.info,'ALL_ORDER_REFUND')">{{ wholeOrderRefundTxt }}</u-button>
        <u-button :customStyle="customStyleBtn"  class="approval_btn_style btn_width_210" :color="color.colorBtnOrange" @click="handlerOrderRefuse(userData.info,'FINISH')">{{ completeProcessingTxt }}</u-button>
      </view>
    </view>
    <!--#ifdef MP-WEIXIN || MP-ALIPAY -->
     <CustomDialogComponent ref="customDialog"></CustomDialogComponent>
    <!--#endif-->
  </view>
</template>

<script>
import horizontalCellComponent from '../../components/HorizontalCellComponent/HorizontalCellComponent.vue'
import { mapGetters } from 'vuex'
import { modifyApprovalDialog, processOrderApprovalDialog, modifyPaymentDialog, modifyLeaveDialog, modifyCardReplacementDialog } from '@/utils/userUtil'
import { apimerchantMobileApprovalGetApprovalDetail } from '@/api/order'
import cache from '@/utils/cache'
import { deepClone, divide } from '../../utils/util';

export default {
  data() {
    return {
      imgPath: this.$imgPath,
      cellStyleContent: { height: "50rpx" },
      titleStyle: { 'font-size': "24rpx", color: '#8f9295' },
      valueStyle: { 'font-size': "24rpx", color: '#1d1e20' },
      viewDetailTxt: this.$t('page.user.view.detail'), // 查看详情  按钮名字
      refuseTxt: this.$t('page.approval.btn.refuse'), // 拒绝 按钮名字
      agreeTxt: this.$t('page.approval.btn.agree'), // 同意 按钮名字
      wholeOrderRejectionTxt: this.$t('page.approval.whole.order.rejection'), // 整单驳回 按钮名字
      wholeOrderRefundTxt: this.$t('page.approval.whole.order.refund'), // 整单退款 按钮名字
      completeProcessingTxt: this.$t('page.approval.complete.processing'), // 完成处理 按钮名字
      isShowBtn: true, // 是否显示按钮层
      titleStatusList: ['page.approval.order.pending.approval', 'page.approval.order.has.been.agreed', 'page.approval.order.rejected'],
      titleIconList: [this.$imgPath.IMG_APPROVAL_ORANGE, this.$imgPath.IMG_APPROVAL_GREEN, this.$imgPath.IMG_APPROVAL_RED],
      titleIcon: "", // 头部icon
      titleTxt: "", // 头部状态
      customStyleBtn: {
        width: '324rpx',
        height: '70rpx',
        borderRadius: '8rpx',
        fontSize: '28rpx',
        padding: '0 !important',
        marginTop: '10rpx'
      },
      userData: {

        base: {
          refundRequestTxt: this.$t('page.approval.order.refund.request'),
          personnelInformationTxt: this.$t('page.approval.order.personnel.information'),
          orderInformationTxt: this.$t('page.approval.order.information'),
          approvalResultsTxt: this.$t('page.approval.order.approval.results'),
          refundTypeTxt: this.$t('page.approval.refund.type'),
          refundAmountTxt: this.$t('page.approval.refund.amount'),
          refundReasonTxt: this.$t('page.approval.refund.reason'),
          userNameTxt: this.$t('page.approval.order.user.name'),
          personnelNoTxt: this.$t('page.approval.order.personnel.no'),
          departmentTxt: this.$t('page.user.department'),
          mobileTxt: this.$t('page.user.mobile'),
          paymentOrderTxt: this.$t('page.approval.order.payment.order'),
          paymentItemsTxt: this.$t('page.approval.payment.items'),
          paymentTypeTxt: this.$t('page.approval.order.payment.type'),
          paymentAmountTxt: this.$t('page.approval.order.payment.amount'),
          orderTimeTxt: this.$t('page.approval.order.order.time'),
          paymentTimeTxt: this.$t('page.approval.order.payment.time'),
          paymentDateTxt: this.$t('page.approval.order.payment.date'),
          approvalContentTxt: this.$t('page.approval.order.approval.content'),
          approvalOrderTypeTxt: this.$t('page.approval.order.type'),
          approvalAmountReceivedTxt: this.$t('business.actual.amount.received'),
          approvalAppointmentTimeTxt: this.$t('page.approval.appointment.time'),
          orderNumberTxt: this.$t('page.order.order.number'),
          modifyRefundAmountTxt: this.$t('page.approval.modify.refund.amount'),
          refundableAmountTxt: this.$t('page.approval.refundable.amount'),
          actualRefundAmountTxt: this.$t('page.approval.actual.refund.amount'),
          orderRejectionReasonTxt: this.$t('page.approval.order.rejection.reason'),
          orderRejectionTxt: this.$t('page.approval.order.rejection'),
          leaveStartTime: this.$t('page.approval.leave.start.time'),
          leaveEndTime: this.$t('page.approval.leave.end.time'),
          LeaveType: this.$t('page.approval.leave.type'),
          leaveReason: this.$t('page.approval.leave.reason'),
          replyPictureTxt: this.$t('title.picture'),
          lackOfCardsTime: this.$t('page.approval.lack.of.cards.time'),
          cardReplacementTypeTxt: this.$t('page.approval.card.replacement.type'),
          cardReplacementReason: this.$t('page.approval.card.replacement.reason'),
          attendanceGroupTxt: this.$t('page.approval.attendance.group'),
          evaluation_img_list: []
        },
        info: {}
      },
      approvalType: '', // 类型
      approvalTypeTitle: '', // 类型名称
      approvalStatus: 0, // 类型状态
      amountInput: '', // 退款金额输入
      refundReasonInput: '', // 驳回原因输入
      customDialogTitle: this.$t('page.approval.whole.order.rejection'), // 自定义弹窗标题
      customDialogInputType: 'rejection', // 弹窗的类型
      refundableAmount: '1.00', // 可退金额
      pageNo: 1, // 页码
      pageSize: 10, // 每页显示数据
      parmas: {}, // 参数
      isShowEmptyView: false, // 是否显示空白内容
      emptyContent: this.$t('tip.list.empty'),
      id: '', // 订单Id
      approvalContent: ''// 审核内容
    }
  },
  components: {
    horizontalCellComponent
  },
  computed: {
    ...mapGetters(['color'])
  },
  onLoad(e) {
    // #ifdef MP-WEIXIN || MP-ALIPAY
    this.initData(e)
    // #endif
  },

  /**
   * 页面加载
   */
  created(e) {
    console.log("申请审批")
    this.titleIcon = this.titleIconList[0]
    this.titleTxt = this.$t(this.titleStatusList[0])
    // 初始化数据
    // #ifdef H5
    this.initData()
    // #endif
  },
  mounted() {

  },

  methods: {
    /**
     * 初始化数据
     */
    initData(e) {
      // #ifdef H5
      this.id = this.$route.query.id
      this.approvalType = this.$route.query.status
      // #endif
      // #ifdef MP-WEIXIN || MP-ALIPAY
      this.id = e.id || ''
      this.approvalType = e.status || ''
      // #endif
      this.parmas.id = this.id
      this.parmas.approval_type = this.approvalType
      this.getOrderDetail()
    },
    /**
     * 更新页面数据
     */
    updateView(itemInfo) {
      var cacheInfo = cache.get(this.$common.KEY_APPROVAL_ITEM_INFO)
      this.approvalTypeTitle = cacheInfo.approvalTypeTitle
      this.approvalStatus = cacheInfo.approvalStatus
      this.isShowBtn = cacheInfo.isShowBtn
      this.approvalContent = itemInfo.approval_reason ? itemInfo.approval_reason : ''
      // 表头样式赋值
      if (typeof (this.approvalStatus) === 'number' && this.approvalStatus < this.titleIconList.length) { this.titleIcon = this.titleIconList[this.approvalStatus] }
      this.titleTxt = this.$t(this.titleStatusList[this.approvalStatus])
      this.titleIcon = this.titleIconList[this.approvalStatus]
      // 页面赋值
      console.log(" this.userData.info", this.userData.info);
      this.userData.info = deepClone(this.formatterResultData(itemInfo))
    },
    /**
     * 格式化返回的数据
     * @param {*} itemInfo
     */
    formatterResultData(itemInfo) {
      if (Reflect.has(itemInfo, 'real_fee')) {
        itemInfo.real_fee = '￥' + divide(itemInfo.real_fee)
      }
      if (Reflect.has(itemInfo, 'pay_fee')) {
        itemInfo.pay_fee = '￥' + divide(itemInfo.pay_fee)
      }
      if (Reflect.has(itemInfo, 'jiaofei_pay_fee')) {
        itemInfo.jiaofei_pay_fee = '￥' + divide(itemInfo.jiaofei_pay_fee)
      }
      if (Reflect.has(itemInfo, 'real_refund_fee')) {
        itemInfo.real_refund_fee = '￥' + divide(itemInfo.real_refund_fee)
      }
      if (Reflect.has(itemInfo, 'refund_fee')) {
        itemInfo.refund_fee = '￥' + divide(itemInfo.refund_fee)
      }
      if (Reflect.has(itemInfo, 'food_info')) {
        var list = itemInfo.food_info || []
        if (list && Array.isArray(list) && list.length > 0) {
          list = list.map(item => {
            item.real_fee = divide(item.real_fee)
            item.raw_fee = divide(item.raw_fee)
            item.real_refund_fee = divide(item.real_refund_fee)
            return item
          })
        }
        itemInfo.food_info = list
      }
      if (Reflect.has(itemInfo, 'order_payment_list')) {
        var paylist = itemInfo.order_payment_list || []
        if (paylist && Array.isArray(paylist) && paylist.length > 0) {
          paylist = paylist.map(item => {
            item.pay_fee = '￥' + divide(item.pay_fee)
            return item
          })
        }
        itemInfo.order_payment_list = paylist
      }
      return itemInfo
    },
    /**
     * 拒绝
     * @param {*} e
     */
    async handlerRefuse(e) {
      console.log("handlerRefuse", e);
      var refundMsg = this.$t('tip.please.fill.in.the.reason.for.rejection')
      if (this.approvalType === 'attendance_for_leave') {
        refundMsg = this.$t('tip.are.you.sure.you.reject.to.the.leave.application')
      }
      if (this.approvalType === 'attendance_supplement') {
        refundMsg = this.$t('tip.are.you.sure.you.reject.to.the.replacemen.card.application')
      }
      this.modifyApprovalStatus(e.id, false, refundMsg, this.approvalType)
    },
    /**
     * 同意
     * @param {*} e
     */
    async handlerAgree(e) {
      console.log("handlerAgree");
      var successMsg = this.$t('tip.are.you.sure.you.agree')
      if (this.approvalType === 'attendance_for_leave') {
        successMsg = this.$t('tip.are.you.sure.you.agree.to.the.leave.application')
      }
      if (this.approvalType === 'attendance_supplement') {
        successMsg = this.$t('tip.are.you.sure.you.agree.to.the.replacemen.card.application')
      }
      this.modifyApprovalStatus(e.id, true, successMsg, this.approvalType)
    },
    /**
     * 修改订单状态
     * @param {*} isAgree  同意/拒绝
     */
    async modifyApprovalStatus(id, isAgree, content, type) {
      var that = this
      var [error, res] = []
      if (type === "order_review") {
        [error, res] = await this.$to(modifyApprovalDialog(id, isAgree, content, this))
      }
      if (type === 'order_jiaofei') {
        [error, res] = await this.$to(modifyPaymentDialog(id, isAgree, content, this))
      }
      if (type === 'attendance_for_leave') {
        [error, res] = await this.$to(modifyLeaveDialog(id, isAgree, content, this))
      }
      if (type === 'attendance_supplement') {
        [error, res] = await this.$to(modifyCardReplacementDialog(id, isAgree, content, this))
      }
      if (res && res === '修改成功' && !error) {
        setTimeout(() => {
          uni.$emit(that.$common.MSG_APPROVAL_SUCCESS, res)
          that.$miRouter.back()
        }, 500)
      }
    },
    /**
     * 菜品修改退款金额与驳回
     */
    handlerOrderRefuseSigle(goodItem, type) {
      if (type === "ORDER_REFUND") {
        var refundAmount = this.$t('page.approval.refundable.amount') + "：¥" + goodItem.real_fee
        this.processOrderApproval(this.userData.info.trade_no, this.userData.info.id, this.userData.info.real_fee, goodItem, type, this.$t('page.approval.modify.refund.amount'), refundAmount, this.$t('page.approval.refund.amount'), true, true, false)
      }
      if (type === "ORDER_REJECT") {
        this.processOrderApproval(this.userData.info.trade_no, this.userData.info.id, this.userData.info.real_fee, goodItem, type, this.$t('page.approval.btn.refuse'), this.$t('page.approval.order.rejection.reason'), '', true, false, true)
      }
    },
    /**
     * 订单修改退款
     * @param itemData 项目信息
     */
    handlerOrderRefuse(itemData, type) {
      console.log("handlerOrderRefuse", itemData, type);
      switch (type) {
        case "ORDER_REJECT":
          this.processOrderApproval(itemData.trade_no, itemData.id, itemData.real_fee, null, type, this.$t('page.approval.whole.order.rejection'), this.$t('page.approval.order.rejection.reason'), '', true, false, true)
          break;
        case "ALL_ORDER_REFUND":
          var refundAmount = this.$t('page.approval.refundable.amount') + "：" + divide(itemData.real_fee)
          this.processOrderApproval(itemData.trade_no, itemData.id, itemData.real_fee, null, type, this.$t('page.approval.whole.order.refund'), refundAmount, this.$t('page.approval.refund.amount'), true, true, false)
          break;
        case "FINISH":
          this.processOrderApproval(itemData.trade_no, itemData.id, itemData.real_fee, null, type, this.$t('page.approval.complete.processing'), this.$t('tip.whether.or.not') + this.$t('page.approval.complete.processing'), "", false, false, false)
          break;
        default:
          break;
      }
    },
    /**
     * 处理退款状态
     * @param {*} isAgree  同意/拒绝
     */
    async processOrderApproval(tradeNo, orderAppealId, realFee, goodInfo, type, title, inputTitle, inputLeftTip, isShowInputTitle, isShowInputLeft, isTxtArea) {
      var that = this
      var [error, res] = await this.$to(processOrderApprovalDialog(tradeNo, orderAppealId, realFee, goodInfo, type, title, inputTitle, inputLeftTip, isShowInputTitle, isShowInputLeft, isTxtArea, this))
      if (res && res === '修改成功' && !error) {
        setTimeout(() => {
          if (goodInfo != null) {
            that.getOrderDetail()
          } else {
            uni.$emit(that.$common.MSG_APPROVAL_SUCCESS, res)
            that.$miRouter.back()
          }
        }, 500)
      }
    },

    /**
     * 获取详情
     */
    async getOrderDetail() {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      const [error, res] = await this.$to(apimerchantMobileApprovalGetApprovalDetail(this.parmas))
      uni.hideLoading()
      if (error) {
        uni.$u.toast(error.message)
        return
      }
      if (res.code === 0) {
        var data = Reflect.has(res, "data") ? res.data : {}
        console.log("data", data);
        if (data && Object.keys(data).length > 0) {
          this.updateView(data)
        } else {
          uni.$u.toast('暂无详情')
        }
      } else {
        uni.$u.toast('获取失败,' + res.msg)
      }
    }

  }

}
</script>

<style lang="scss" scoped>
$color-approval-red:#ff6349;
$color-approval-green:#51d854;
$color-approval-orange:#fd953c;
$color-approval-blue:#158cf6;

.approval_detail_container{
  padding:60rpx 40rpx 130rpx 40rpx;

  .approval_detail_title{
    width: 670rpx;
    height: 120rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    font-size: 42rpx;
    margin: 0 auto;
    display: flex;
    align-items: center;
    position: relative;
    > image {
      width: 42rpx;
      height: 42rpx;
      margin: 30rpx 12rpx 30rpx 30rpx;
    }
    .approval_detail_title_top{
      position: absolute;
      top: 0;
      width: 670rpx;
      height: 8rpx;

      border-radius: 8rpx 8rpx 0px 0px;
      margin: 0 auto ;
    }
    .background_color_red{
      background-color:$color-approval-red ;
    }
    .color_red{
      color: $color-approval-red;
    }

    .background_color_green{
      background-color: $color-approval-green;
    }
    .color_green{
      color: $color-approval-green;
    }
    .background_color_orange{
      background-color: $color-approval-orange;
    }

  }
  .approval_detail_content_layout{
    margin-top: 10rpx;
  }

  .approval_detail_content_title{
      height: 73rpx;
      font-size: 24rpx;
      color: #8f9295;
      text-align: left;
      line-height: 73rpx;

    }
 .approval_detail_content{
    display: inline-block;
    width: 670rpx;
    min-height: 70rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    padding :19rpx 10rpx;
    box-sizing: border-box;
    .approval_detail_reply_title{
        width: 160rpx;
        font-size: 24rpx;
        color: #8f9295;
        padding: 5px 15px;
      }
      .approval_detail_reply_pic{
        width: 100%;
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        .approval_detail_img{
          margin-left:30rpx ;
          margin-bottom: 20rpx;
        }
      }
 }
 .approval_detail_goods{
  width: 670rpx;
  display: flex;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  justify-content: space-between;
  justify-items: center;
  margin-top: 20rpx;
  margin-bottom:100rpx;
  .goods_img{
    width: 120rpx;
    height: 120rpx;
    border-radius:10rpx;
  }
  .goods_content{
    display: inline-block;

    .goods_content_name{
      color: #1d201e;
      font-size: 30rpx;
    }
    .goods_content_amount{
      font-size: 24rpx;
      color: #1d1e20;
      margin:10rpx 0;
    }

  }
  .goods_btn{
    font-size: 30rpx;
    .color_orange{
      color: $color-approval-orange;
    }
    .color_blue{
      color: $color-approval-blue;
    }

  }
 }
 .approval_detail_btns_layout{
    position: fixed;
    width: 100%;
    min-height: 106rpx;
    background: #ffffff;
    bottom:0;
    left: 0;
  .approval_detail_btns{
    display: flex;
    justify-content: space-between;
    margin:  20rpx;
    flex-wrap: wrap;
    .approval_btn_style{
      width: 324rpx;
      height: 70rpx;
      border-radius: 8rpx;
      font-size: 28rpx;
      padding: 0 !important;
      margin-top:10rpx;
    }
    .btn_width_210{
      width: 210rpx !important;
    }
  }

 }

}
.custom_input_layout{
  display: inline-flex;
  align-items: center;
  margin-top:10rpx;
}
</style>

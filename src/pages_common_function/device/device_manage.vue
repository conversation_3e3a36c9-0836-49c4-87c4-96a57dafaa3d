<template>
  <view class="device_manage_container">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('title.device.management')"
      placeholder
      :autoBack="true"
      :leftIconColor="color.navigation"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    ></u-navbar>
    <!--#endif-->
    <!-- 筛选层 -->
    <view class="device_manage_search">
      <filterLayoutComponent :filterDataLayoutList="filterDataList" @handlerItemClick="handlerDrowDownItemClick"></filterLayoutComponent>
    </view>
    <!-- 列表数据 -->
    <view v-if="!isShowEmptyView" class="device_manage_list">
    <mescroll-uni ref="mescrollRef" :fixed="false" :safearea="false" :bottom="150" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="{ auto: false }" :up="{ auto: false }" >
      <view class="device_manage_item_container" v-for="(item,index) in deviceDataList" :key="index" >
        <view class="device_manage_item_level_one">
          <view class ="device_manage_item_level_one_title">{{ item.device_name}}</view>
          <view class = "device_manage_item_level_one_txt"><view :class="['txt_dot',item.txtDotColor]"></view>{{ item.online?"在线":"离线" }}</view>
        </view>
        <view class="horizontal_cell_line" ></view>
        <view class="device_manage_item_level_two">
          <view class ="device_manage_item_level_two_title">所属组织：</view>
          <view class ="device_manage_item_level_two_txt">{{ item.consumer_name }}</view>
        </view>
        <view class="device_manage_item_level_two">
          <view class ="device_manage_item_level_two_title">设备类型：</view>
          <view class ="device_manage_item_level_two_txt">{{ item.device_type_alias?item.device_type_alias:'--' }}</view>
        </view>
        <view class="device_manage_item_level_two">
          <view class ="device_manage_item_level_two_title">有效期：</view>
          <view class ="device_manage_item_level_two_txt">{{ item.activate_time | formatActivateTime }}</view>
        </view>
        <view class="device_manage_item_level_two">
          <view class ="device_manage_item_level_two_title">SN码：</view>
          <view class ="device_manage_item_level_two_txt">{{ item.serial_no? item.serial_no:'--'}}</view>
        </view>
        <view class="device_manage_btns">
          <u-button :customStyle="customStyleBtn" class="device_manage_btn_style" :color="color.colorBtnBlack" @click="activationCode(item)">{{ activationCodeTxt }}</u-button>
          <u-button :customStyle="customStyleBtn" class="device_manage_btn_style" :color="color.colorBtnOrange" @click="setUp(item.device_no,item.menu_type,item.device_settings_pwd)">{{ setUpTxt }}</u-button>
        </view>
      </view>
    </mescroll-uni>
  </view>
    <!-- 空数据 -->
    <emptyComponent v-if="isShowEmptyView"></emptyComponent>
    <!-- 弹出窗 -->
    <qrCodeDialogComponent ref="qrCodeDialog" :qrCodeContent="qrCodeContent"></qrCodeDialogComponent>
    <CustomDialogComponent ref="customDialogSingle" dialogType="custom" customInputHolder="请输入" :customDialogTitle="customDialogTitleTxt"  isShowCancelBtn @handlerCustomConfirm="handlerCustomConfirm" @handlerCustomCancel="handlerCustomCancel">
      <template slot="custom">
        <view>
        <view class="dialog_choose_item">
          <view class="dialog_choose_item_txt">菜谱设置:</view>
          <view class="dialog_choose_item_content" @click="handlerSettingChoose">
            <u-input v-model="dishesSettingValue" class="dialog_choose_item_content_input" border="none" :placeholder="customDialogChooseHolder" readonly></u-input>
            <u-icon  class= 'dialog_choose_item_content_icon' name="arrow-down-fill" color="#e2e1e1"></u-icon>
          </view>
          <!--菜谱设置下拉列表-->
          <view class="dialog_choose_drop_down" v-if="isShowDropDownView">
            <view class="dialog_choose_drop_down_item" v-for="(subItem,subIndex) in dishesSettingList" :key="subIndex"  @click="dishesSettingItemClick(subItem)">
              {{ subItem.name }}
            </view>
          </view>
        </view>
        <view class="dialog_choose_item">
          <view class="dialog_choose_item_txt">入口密码:</view>
          <view class="dialog_choose_item_content">
            <u-input v-model="dishesPasswordValue" border="none" :placeholder="customDialogInputHolder" :password="!isShowPassword" >
            <template slot="suffix">
              <u-icon :name="isShowPassword?'eye-off':'eye-fill'"  size="36" @click="clickPassword"></u-icon>

            </template>
            </u-input>
          </view>
        </view>
      </view>
      </template>
    </CustomDialogComponent>
    <!--#ifdef MP-WEIXIN || MP-ALIPAY -->
    <CustomDialogComponent ref="customDialog"></CustomDialogComponent>
    <!--#endif-->

  </view>
</template>

<script>

import { apiBackgroundDeviceDeviceList, apiBackgroundDeviceDeviceConfig } from '@/api/device'
import { apiBackgroundDeviceDeviceDeviceType } from '@/api/dic'
import filterLayoutComponent from "@/components/FilterLayoutComponent/FilterLayoutComponent"
import qrCodeDialogComponent from "@/components/QrCodeDialogComponent/QrCodeDialogComponent.vue"
import emptyComponent from "@/components/EmptyComponent/EmptyComponent.vue"
import { mapGetters } from 'vuex'
import { deepClone } from "../../utils/util"
import comDic from "../../common/comDic"
import { getUserOrgsList } from '@/utils/userUtil'
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import { isPass8To20orNumber } from '../../utils/validata'

export default {
  data() {
    return {
      activationCodeTxt: this.$t('page.user.card.activation.code'),
      setUpTxt: this.$t('page.user.card.set.up'),
      customDialogTitleTxt: this.$t('page.user.card.set.up'),
      customDialogInputHolder: this.$t('page.tip.holder.please.enter'),
      customDialogChooseHolder: this.$t('page.tip.holder.please.choose'),
      deviceDataList: [], // 列表数据
      pageNo: 1, // 页码
      pageSize: 10, // 每页显示数据
      isShowEmptyView: false, // 是否显示空数据
      qrCodeContent: '', // 激活码内容
      dishesSettingList: comDic.DIC_DISHES_SETTING, // 菜谱设置
      isShowDropDownView: false, // 是否显示下来列表
      dishesSettingValue: "", // 菜谱设置值，
      dishesSettingCode: "", // 菜谱设置值对应的code，
      dishesPasswordValue: "", // 菜谱设置密码值
      deviceNo: '', // 设备ID
      filterDataList: [ // 头部筛选列表
        {
          title: '全部组织',
          chooseItem: '全部组织',
          dataList: []
        },
        {
          title: '全部设备',
          chooseItem: '全部设备',
          dataList: []
        },
        {
          title: '全部',
          chooseItem: '全部',
          dataList: comDic.DIC_DEVICE_STATUS
        }
      ],
      parmas: { // 列表数据入参
        page: 1,
        page_size: 10
      },
      customStyleBtn: { // 按钮自定义样式这里为了兼容微信与支付宝
        width: '120rpx',
        height: '48rpx',
        borderRadius: '6rpx',
        fontSize: '24rpx',
        margin: '0 0 0 10rpx',
        padding: '0 !important'
      },
      isShowPassword: false// 是否显示密码
    }
  },
  mixins: [MescrollMixin],
  computed: {
    ...mapGetters(['color'])
  },
  filters: {
    /**
     * 格式化
     * @param {*} value
     */
    formatActivateTime(value) {
      if (value) {
        value = value.length > 21 ? value.replace(/ - /g, '至') : value
        return value.replace(/00:00:00/g, '')
      }
      return "--"
    }

  },
  components: { filterLayoutComponent, qrCodeDialogComponent, emptyComponent },
  /**
   * 页面加载
   */
  onload(e) {
    console.log("用户列表页面加载", e)
    this.initData()
  },
  created() {
    this.initData()
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      // 获取数据
      console.log("this.parmas", this.parmas);
      this.parmas.page = this.pageNo
      this.parmas.page_size = this.pageSize
      this.getDeviceList(this.parmas)
      // 获取字典
      this.getDicList()
    },
    /**
     * 下拉刷新返回
     */
    downCallback(page) {
      console.log(" downCallback page", page);
      this.pageNo = 1
      this.parmas.page = this.pageNo
      this.getDeviceList(this.parmas)
    },
    /**
     * 上拉加载更多
     * @param {*} page
     */
    upCallback(page) {
      console.log(" upCallback page", page);
      this.pageNo++
      this.parmas.page = this.pageNo
      this.getDeviceList(this.parmas)
    },
    /**
     * 获取用户列表
     * @param {*} parmas
     */
    getDeviceList(parmas) {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      apiBackgroundDeviceDeviceList(parmas)
        .then(res => {
          if (res.code === 0) {
            uni.hideLoading()
            var data = Reflect.has(res, "data") ? res.data : {}
            var resultList = Reflect.has(data, "results") ? data.results : []
            console.log("data", data, resultList);
            var count = data.count ? data.count : 0
            // 没有数据
            this.isShowEmptyView = this.pageNo === 1 && (!resultList || resultList.length === 0)
            if (this.pageNo === 1 && resultList && resultList.length > 0) {
              // 首次加载数据
              console.log("首次加载数据");
              this.deviceDataList = deepClone(resultList)
            } else if (this.pageNo !== 1 && resultList && resultList.length > 0) {
              // 加载更多数据
              console.log("加载更多数据");
              this.deviceDataList = this.deviceDataList.concat(resultList)
            } else {
              // 其他情况
              console.log("其他情况");
              uni.hideLoading()
              uni.$u.toast(res.msg === "成功" ? "没有更多了" : res.msg)
            }
            this.mescroll.setPageNum(this.pageNo)
            this.mescroll.endBySize(this.pageSize, count)
          } else {
            uni.hideLoading()
            uni.$u.toast(res.msg === "成功" ? "暂无数据" : res.msg)
            if (this.pageNo === 1) {
              this.deviceDataList = []
            }
            this.mescroll.endErr()
          }
        })
        .catch(err => {
          console.log("err", err.message);
          uni.$u.toast(err.message)
          this.mescroll.endErr()
        })
    },
    /**
     * 激活码
     * @param item 项的数据
     */
    activationCode(item) {
      console.log("激活码");
      try {
        var deviceType = item.device_type || ''
        this.qrCodeContent = deviceType === 'QCG' ? JSON.stringify(item.cupboard_json) : item.activation_code
        if (!this.qrCodeContent || this.qrCodeContent.length === 0) {
          uni.$u.toast("该设备没有激活码")
          return
        }
        // 展示激活码
        if (this.$refs.qrCodeDialog && typeof (this.$refs.qrCodeDialog.showQrDialog()) === "function") {
          this.$refs.qrCodeDialog.showQrDialog();
        }
      } catch (error) {
        console.log("error", error);
      }
    },
    /**
     * 设置
     */
    setUp(number, menuType, deviceSettingsPwd) {
      // 保存点击的设备number
      this.deviceNo = number
      console.log("设置");
      this.dishesSettingValue = menuType ? this.getMenuType(menuType) : ""
      this.dishesSettingCode = menuType
      this.dishesPasswordValue = deviceSettingsPwd || ''
      if (this.$refs.customDialogSingle && typeof (this.$refs.customDialogSingle.showCustomDialog()) === "function") {
        this.$refs.customDialogSingle.showCustomDialog();
      }
    },
    /**
     * 弹窗确认按钮点击
     */
    handlerCustomConfirm(e) {
      console.log("弹窗确认按钮点击", e);
      if (!this.dishesSettingValue) {
        this.$u.toast("请选择菜谱设置内容")
        return
      }
      if (!this.dishesPasswordValue) {
        this.$u.toast("请输入密码")
        return
      }
      if (!isPass8To20orNumber(this.dishesPasswordValue)) {
        this.$u.toast("密码长度8到20位，字母和数字组合")
        return
      }

      // 组参
      var params = {
        device_no: this.deviceNo,
        device_settings_pwd: this.dishesPasswordValue,
        menu_type: this.dishesSettingCode,
        menu_type_id: "",
        can_refund: 0
      }
      apiBackgroundDeviceDeviceConfig(params).then(res => {
        this.$refs.customDialogSingle.hideCustomDialog();
        if (res.code === 0) {
          this.$u.toast("设置成功")
          this.pageNo = 1
          this.parmas.page = 1
          this.getDeviceList(this.parmas)
        } else {
          this.$u.toast("设置失败 " + res.msg)
        }
      }).catch(error => {
        console.log("error", error);
        this.$refs.customDialogSingle.hideCustomDialog();
        this.$u.toast("设置失败 " + error.message)
      })
    },
    /**
     * 弹窗取消按钮点击
     */
    handlerCustomCancel() {
      console.log("弹窗取消按钮点击");
    },
    /**
     * 菜单选择
     */
    handlerSettingChoose() {
      console.log("handlerSettingChoose");
      this.isShowDropDownView = !this.isShowDropDownView
    },
    /**
     * 菜谱设置下拉列表选项选择
     * @param {*} itemData
     */
    dishesSettingItemClick(itemData) {
      console.log("dishesSettingItemClick", itemData);
      this.isShowDropDownView = false
      this.dishesSettingValue = itemData.name
      this.dishesSettingCode = itemData.value
    },
    /**
     * 获取字典列表
     */
    async getDicList() {
      var filterList = deepClone(this.filterDataList)
      // 设备类型列表
      var [errorDevice, resultDevice] = await this.$to(apiBackgroundDeviceDeviceDeviceType())
      var resultDeviceData = Reflect.has(resultDevice, 'data') ? resultDevice.data : []

      // 组织类型列表
      var [errorOrgan, resultOrgan] = await this.$to(getUserOrgsList(2))

      // 赋值
      if (resultOrgan && !errorOrgan) {
        resultOrgan.unshift({ name: '全部组织', value: '' })
        filterList[0].dataList = deepClone(resultOrgan)
      }
      if (resultDeviceData && !errorDevice) {
        resultDeviceData.unshift({ name: '全部设备', value: '' })
        filterList[1].dataList = resultDeviceData
      }

      this.filterDataList = deepClone(filterList)
    },
    /**
     * 头部筛选下拉列表点击事件
     * @param {点击项数据} itemData
     * @param {点击项所在列表的位置} index
     */
    handlerDrowDownItemClick(itemData, index) {
      console.log("handlerDrowDownItemClick", itemData, index);
      // 设置点击记录
      this.filterDataList[index].chooseItem = itemData.name
      this.filterDataList[index].title = itemData.name
      this.parmas.page = 1
      this.pageNo = 1
      switch (index) {
        case 0:// 组织
          this.parmas.organization_id = itemData.id || ''
          if (this.parmas.organization_id === '') {
            delete this.parmas.organization_id
          }
          break;
        case 1:// 设备类型
          this.parmas.device_type = itemData.key || ''
          if (this.parmas.device_type === '') {
            delete this.parmas.device_type
          }
          break;
        case 2:// 设备状态
          this.parmas.online = itemData.value
          if (itemData.value === '') {
            delete this.parmas.online
          }
          break;
        default:
          break;
      }

      this.getDeviceList(this.parmas)
    },
    /**
     * 获取设置类型
     */
    getMenuType(type) {
      if (type) {
        var list = comDic.DIC_DISHES_SETTING || []
        var itemList = list.find(item => {
          return item.value === type
        })
        if (itemList) {
          return itemList.name
        }
      }
      return ""
    },
    /**
     * 隐藏显示密码
     */
    clickPassword() {
      this.isShowPassword = !this.isShowPassword
    }

  }

}
</script>

<style lang="scss" scoped>
$colorBlack :#1d1e20;
$colorGray : #8f9295;
$colorRed: #ff5757;
$colorGreen:#56d648;
.device_manage_container{
  position: fixed;
  top: 0;
  bottom: 100rpx;
  left: 0;
  right: 0;
}
.device_manage_search{
  margin-bottom: 30rpx;
}
.device_manage_list{
  height: calc(100vh - 168rpx);
}
.device_manage_item_container{
  width: 670rpx;
  height: 382rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding:0rpx 27rpx;
  margin: 10rpx auto;
  font-size: 24rpx;
  .horizontal_cell_line{
    width: 610rpx;
    height: 1rpx;
    background-color: #eae9ed;
    margin: 0 auto;
  }
  .device_manage_item_level_one{
     display: flex;
     align-items: center;
     justify-content: space-between;
     height: 80rpx;
     line-height: 80rpx;
     text-align: center;
     .device_manage_item_level_one_title{
      font-size: 28rpx;
      color:$colorBlack;
     }
     .device_manage_item_level_one_txt{
      color: $colorGray;
      .txt_dot{
        width: 12rpx;
        height: 12rpx;
        display: inline-block;
        border-radius: 6rpx;
        margin: 2rpx 10rpx;
      }
      .active_color{
        background-color: $colorGreen;
      }
      .offline_color{
        background-color: $colorRed;

      }
      .noactive_color{
        background-color: $colorGray;
      }
     }

  }
  .device_manage_item_level_two{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20rpx;
     .device_manage_item_level_two_title{
      color: $colorGray;
     }
     .device_manage_item_level_two_txt{
      color:$colorBlack;

     }

  }
  .device_manage_btns{
    height: 48rpx;
    display: flex;
    justify-content: flex-end;
    margin:23rpx 0;

     .device_manage_btn_style{
      width: 120rpx;
      height: 48rpx;
      border-radius: 6rpx;
      font-size: 24rpx;
      margin: 0 0 0 10rpx;
      padding: 0 !important;
     }

  }

}
.dialog_choose_item {
    height: 80rpx;
    font-size: 36rpx;
    display: flex;
    justify-items: center;
    justify-content: space-between;
    padding:0 32rpx;
    position: relative;
    &:nth-child(2){
      margin-top: 30rpx;
    }
    .dialog_choose_item_txt{
      height: 80rpx;
      line-height: 80rpx;
      color: #8f9295;
      font-size: 36rpx;
      text-align: center;
    }
    .dialog_choose_item_content{
      width: 360rpx;
      height: 80rpx;
      background-color: #ffffff;
      border-radius: 8rpx;
      border: solid 1rpx #dbdada;
      display: inline-flex;
      justify-content: space-between;
      padding-left: 23rpx;
      .dialog_choose_item_content_input{
        color: #201e1d;
      }
      .dialog_choose_item_content_icon{
        margin :33rpx;
      }
      .dialog_choose_item_content_icon::before{
        content: "";
        width: 1rpx;
        height: 78rpx;
        background-color: #e2e1e1;
        margin-right: 33rpx;
      }
    }
    .dialog_choose_drop_down{
      position: absolute;
      top: 80rpx;
      right: 32rpx;
      width:360rpx ;
      border:1px solid #e2e1e1;
      border-radius: 0 0 10rpx 10rpx ;
      background: #ffffff;
      z-index: 1000;
      .dialog_choose_drop_down_item{
        padding: 20rpx;
        font-size: 28rpx;
      }
      .dialog_choose_drop_down_item:hover{
        background: #e2e1e1;
      }

    }
  }

</style>

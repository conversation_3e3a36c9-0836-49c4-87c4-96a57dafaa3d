<template>
  <view class="invitation_container">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('title.visitor.invitation')"
      placeholder
      :autoBack="true"
      :leftIconColor="color.navigation"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    ></u-navbar>
    <!--#endif-->
    <u-form :rules="userData.rules" ref="form" :model="userData">
     <!--访客信息-->
    <view class="invitation_content_title">{{ userData.head.visitorInformationTxt }}</view>
    <view class="invitation_content">
      <view class="invitation_content_item">
        <view class="invitation_content_item_left">
          <text>{{ userData.base.nameTxt }}</text>
          <text class="invitation_content_rq">*</text>
        </view>
        <view class="invitation_content_item_right">
          <u-input :value="userData.value.name" border="none" type="text" inputAlign="right" :placeholder="userData.base.tipNameTxt"  clearable  ></u-input>
          <u-icon name="arrow-right" color="#8f9295" size="28"></u-icon>
        </view>
      </view>
      <view class="horizontal_cell_line" ></view>
      <view class="invitation_content_item">
        <view class="invitation_content_item_left">
          <text>{{ userData.base.mobileTxt }}</text>
          <text class="invitation_content_rq">*</text>
        </view>
        <view class="invitation_content_item_right">
          <u-input :value="userData.value.name" border="none" type="number" inputAlign="right" :placeholder="userData.base.tipMobileTxt" maxlength="15" clearable  ></u-input>
          <u-icon name="arrow-right" color="#8f9295" size="28"></u-icon>
        </view>
      </view>
      <view class="horizontal_cell_line" ></view>
      <view class="invitation_content_item">
        <view class="invitation_content_item_left">
          <text>{{ userData.base.idCardTxt }}</text>
          <text class="invitation_content_rq">*</text>
        </view>
        <view class="invitation_content_item_right">
          <u-input :value="userData.value.name" border="none" type="idcard" inputAlign="right" :placeholder="userData.base.tipIdCardTxt" clearable maxlength="22"  ></u-input>
          <u-icon name="arrow-right" color="#8f9295" size="28"></u-icon>
        </view>
      </view>
    </view>

     <!--访问信息-->
     <view class="invitation_content_title">{{ userData.head.accessInformationTxt }}</view>
     <view class="invitation_content">
      <view class="invitation_content_item">
        <view class="invitation_content_item_left">
          <text>{{ userData.base.vistorTypeTxt }}</text>
          <text class="invitation_content_rq">*</text>
        </view>
        <view class="invitation_content_item_right">
          <u-input :value="userData.value.name" border="none" type="text" inputAlign="right" :placeholder="userData.base.tipVistorTypeTxt"  readonly  ></u-input>
          <u-icon name="arrow-right" color="#8f9295" size="28"></u-icon>
        </view>
      </view>
      <view class="horizontal_cell_line" ></view>
      <view class="invitation_content_item">
        <view class="invitation_content_item_left">
          <text>{{ userData.base.policyTypeTxt }}</text>
          <text class="invitation_content_rq">*</text>
        </view>
        <view class="invitation_content_item_right">
          <u-input :value="userData.value.name" border="none" type="text" inputAlign="right" :placeholder="userData.base.tipPolicyTypeTxt"  readonly  ></u-input>
          <u-icon name="arrow-right" color="#8f9295" size="28"></u-icon>
        </view>
      </view>
      <view class="horizontal_cell_line" ></view>
      <view class="invitation_content_item">
        <view class="invitation_content_item_left">
          <text>{{ userData.base.accessOrganizationTxt }}</text>
          <text class="invitation_content_rq">*</text>
        </view>
        <view class="invitation_content_item_right">
          <u-input :value="userData.value.name" border="none" type="text" inputAlign="right" :placeholder="userData.base.tipAccessOrganizationTxt"  readonly  ></u-input>
          <u-icon name="arrow-right" color="#8f9295" size="28"></u-icon>
        </view>
      </view>
    </view>
     <!--来访理由-->
     <view class="invitation_content_title">{{ userData.head.reasonVisitTxt }}</view>
     <view class="invitation_content">
      <view class="invitation_content_item">
        <view class="invitation_content_item_left">
          <text>{{ userData.base.remarksTxt }}</text>
        </view>
        <view class="invitation_content_item_right">
          <u-input :value="userData.value.name" border="none" type="text" inputAlign="right" :placeholder="userData.base.tipRemarksTxt"   clearable  ></u-input>
          <u-icon name="arrow-right" color="#8f9295" size="28"></u-icon>
        </view>
      </view>
    </view>
  </u-form>
    <!--按钮层-->
      <view class="approval_detail_btns">
        <u-button  :customStyle="customStyleBtn" class="approval_btn_style"  :disabled="isBtnDisable" :color="color.colorBtnOrange" @click="handlerRefuse()">{{ saveTxt }}</u-button>
      </view>

  </view>
</template>

<script>
import { getCardServiceCardUserList } from '@/api/user'
import { mapGetters } from 'vuex'
export default {
  data() {
    return {
      customStyleBtn: {
        width: '670rpx',
        height: '70rpx',
        borderRadius: '8rpx',
        fontSize: '28rpx',
        margin: '19rpx  40rpx'
      },
      userData: {
        head: {
          visitorInformationTxt: this.$t('page.invitation.visitor.information') + "(" + this.$t('page.invitation.required') + ")",
          accessInformationTxt: this.$t('page.invitation.access.information') + "(" + this.$t('page.invitation.required') + ")",
          reasonVisitTxt: this.$t('page.invitation.reason.for.visit') + "(" + this.$t('page.invitation.optional') + ")"
        },
        base: {
          nameTxt: this.$t('page.user.name'),
          mobileTxt: this.$t('page.user.mobile'),
          idCardTxt: this.$t('page.invitation.idCard.number'),
          vistorTypeTxt: this.$t('page.invitation.vistor.type'),
          policyTypeTxt: this.$t('page.invitation.policy.type'),
          accessOrganizationTxt: this.$t('page.invitation.access.organization'),
          remarksTxt: this.$t('page.invitation.remarks'),
          tipNameTxt: this.$t('tip.please.enter.the.visitors.mobile'),
          tipMobileTxt: this.$t('tip.please.enter.the.visitors.mobile'),
          tipIdCardTxt: this.$t('tip.please.enter.the.visitors.idcard'),
          tipVistorTypeTxt: this.$t('tip.please.select.a.vistor.type'),
          tipPolicyTypeTxt: this.$t('tip.please.select.a.policy.type'),
          tipAccessOrganizationTxt: this.$t('tip.please.select.the.organization.to.visit'),
          tipRemarksTxt: this.$t('tip.enter.the.reason')
        },
        value: {
          name: ''
        },
        rules: {
          'userData.value.name': {
            type: 'string',
            required: true,
            message: '请填写姓名',
            trigger: ['blur', 'change']
          },
          'userInfo.sex': {
            type: 'string',
            max: 1,
            required: true,
            message: '请选择男或女',
            trigger: ['blur', 'change']
          }

        }
      },
      saveTxt: this.$t('page.invitation.submit'),
      isBtnDisable: true// 按钮无法点击

    }
  },
  onReady() {
    console.log("onReady");
    // 需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则
    // this.$refs.form.setRules(this.rules)
  },
  computed: {
    ...mapGetters(['color'])
  },
  /**
   * 页面加载
   */
  onload(e) {
    console.log("个人页面加载", e)
  },
  methods: {
    // 获取用户列表
    getCardUserListPost(parmas) {
      this.$showLoading({
        title: this.$t('loging.project'),
        mask: true
      })
      getCardServiceCardUserList(parmas)
        .then(res => {
          if (res.code === 0) {
            uni.hideLoading()
            if (!res.data.length) {
              // 没有项目点直接进入绑定页面

            } else if (res.data && res.data.length > 0) {
              // 加载用户列表

            } else {
              // 多个项目点 进入选择项目点页面

            }
          } else {
            uni.hideLoading()
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    }
  }

}
</script>

<style lang="scss" scoped>
.invitation_container{
  padding :10rpx 40rpx;
  .invitation_content_title{
    height: 73rpx;
    font-size: 24rpx;
    color: #8f9295;
    text-align: left;
    line-height: 73rpx;
  }
  .invitation_content{
    width: 670rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    margin: 0 auto ;

    .invitation_content_item{
      display: flex;
      justify-content: space-between;
      justify-items: center;
      padding:28rpx 32rpx;
      font-size: 28rpx;
      .invitation_content_item_left{
        display: flex;
        color: #8f9295;
        width: 220rpx;
        height: 46rpx;
        line-height: 46rpx;
        text-align: center;
        .invitation_content_rq{
          color: #fd953c;
          font-size: 28rpx;
          margin:7rpx 10rpx;
          text-align: center;
        }
      }
      .invitation_content_item_right{
        display: inline-flex;
        height: 46rpx;
      }
    }
    .horizontal_cell_line{
      width: 610rpx;
      height: 1rpx;
      background-color:  #eae9ed;
      margin: 0 auto;
      }
  }

  .approval_detail_btns{
    width: 100%;
    height: 108rpx;
    background-color: #ffffff;
    position: fixed;
    bottom: 0;
    left: 0;
    .approval_btn_style{
      width: 670rpx;
      height: 70rpx;
      border-radius: 8rpx;
      font-size: 28rpx;
      margin:19rpx  40rpx;
    }

  }

}

</style>

<template>
  <view class="message_container">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('title.message.notification')"
      placeholder
      :autoBack="true"
      :leftIconColor="color.navigation"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    ></u-navbar>
    <!--#endif-->
    <mescroll-uni ref="mescrollRef" :fixed="false" :safearea="true" :bottom="100" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="{ auto: false }" :up="{ auto: false }" v-if="!isShowEmptyView">
      <view class="message_item" v-for="(item,index) in newDataList" :key="index" @click="handlerMessageClick(item)" :data-index="index">
        <view class="message_item_img_layout">
          <image :src="item.img?item.img:imgPath.IMG_HEAD_DEFAULT" class="message_item_img" mode="scaleToFill" ></image>
          <view  class ="message_item_dot"  v-if="item.read_flag=='未读'"  ></view>
        </view>
        <view class="message_item_content_layout">
          <view class="message_item_content_top">
            <view class="message_item_content_title">{{ item.title }} </view>
            <view class="message_item_content_time">{{ item.post_time }} </view>
          </view>
          <view class="message_item_content_tip">{{ item.sender_name }} </view>
        </view>

      </view>
    </mescroll-uni>
     <!-- 空白页 -->
     <EmptyComponent :emptyContent="emptyContent" v-if="isShowEmptyView"></EmptyComponent>
  </view>
</template>

<script>
import { apiBackgroundMessagesMessagesGetMsgList } from '@/api/message'
import { mapGetters } from 'vuex'
import { deepClone } from '../../utils/util'
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";

export default {
  data() {
    return {
      imgPath: this.$imgPath,
      newDataList: [],
      freezeAccounTxt: this.$t('page.user.freeze.account'),
      lossReportingTxt: this.$t('page.user.card.loss.reporting'),
      uploadFaceTxt: this.$t('page.user.upload.face'),
      pageNo: 1, // 页码
      pageSize: 10, // 每页显示数据
      parmas: {}, // 参数
      isShowEmptyView: false,
      emptyContent: this.$t("tip.list.empty")
    }
  },
  mixins: [MescrollMixin],
  computed: {
    ...mapGetters(['color'])
  },
  /**
   * 页面加载
   */
  onload(e) {
  },
  onShow() {
    // #ifdef MP-WEIXIN || MP-ALIPAY
    this.initData()
    // #endif
  },
  mounted() {
    // #ifdef H5
    this.initData()
    // #endif
  },
  created() {
  },
  destroyed() {
    // 销毁通知首页刷新
    uni.$emit(this.$common.MSG_UPDATE_UNREAD_BACK, "消息返回")
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      // 获取数据
      console.log("this.parmas", this.parmas);
      this.parmas.page = this.pageNo
      this.parmas.page_size = this.pageSize
      this.getMessageList(this.parmas)
    },
    /**
     * 下拉刷新返回
     */
    downCallback(page) {
      console.log(" downCallback page", page);
      this.pageNo = 1
      this.parmas.page = this.pageNo
      this.getMessageList(this.parmas)
    },
    /**
     * 上拉加载更多
     * @param {*} page
     */
    upCallback(page) {
      console.log(" upCallback page", page);
      this.pageNo++
      this.parmas.page = this.pageNo
      this.getMessageList(this.parmas)
    },
    /**
     * 获取消息列表
     * @param {*} parmas
     */
    getMessageList(parmas) {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      apiBackgroundMessagesMessagesGetMsgList(parmas)
        .then(res => {
          if (res.code === 0) {
            uni.hideLoading()
            var data = Reflect.has(res, "data") ? res.data : {}
            var resultList = Reflect.has(data, "results") ? data.results : []
            var count = data.count ? data.count : 0
            console.log("data", data, resultList);
            // 没有数据
            this.isShowEmptyView = this.pageNo === 1 && (!resultList || resultList.length === 0)
            if (this.pageNo === 1 && resultList && resultList.length > 0) {
              // 首次加载数据
              console.log("首次加载数据");
              this.newDataList = deepClone(resultList)
            } else if (this.pageNo !== 1 && resultList && resultList.length > 0) {
              // 加载更多数据
              console.log("加载更多数据");
              this.newDataList = this.newDataList.concat(resultList)
            } else {
              // 其他情况
              console.log("其他情况");
              uni.hideLoading()
              uni.$u.toast(res.msg === "成功" ? "暂无数据" : res.msg)
            }
            this.mescroll.setPageNum(this.pageNo)
            this.mescroll.endBySize(this.pageSize, count)
          } else {
            uni.hideLoading()
            this.isShowEmptyView = true
            uni.$u.toast(res.msg === "成功" ? "暂无数据" : res.msg)
            this.mescroll.endErr()
          }
        })
        .catch(err => {
          this.isShowEmptyView = true
          uni.$u.toast(err.message)
          this.mescroll.endErr()
        })
    },

    /**
     * 消息点击
     * @param itemData 项目数据
     */
    handlerMessageClick(itemData) {
      var currentId = itemData.msg_no
      var authorName = itemData.sender_name
      console.log("handlerMessageClick", currentId)
      this.$miRouter.push({
        path: '/pages_common_function/message/message_detail',
        query: {
          id: currentId,
          author: authorName
        }
      })
    }
  }

}
</script>

<style lang="scss" scoped>
.message_container{
  margin: 40rpx;
  height: calc(100vh - 88rpx);
  .message_item{
    width: 670rpx;
    min-height: 150rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    margin: 0 auto;
    padding:30rpx;
    margin-top: 20rpx;
    display: flex;
    .message_item_img_layout{
      width: 91rpx;
      height: 92rpx;
      position: relative;
      .message_item_dot{
        width: 20rpx;
        height: 20rpx;
        position: absolute;
        top: 0;
        right: 0;
        border: solid 3rpx #ffffff;
        background: red;
        border-radius: 10rpx;
      }
      .message_item_img{
        width: 91rpx;
        height: 92rpx;
      }

    }
    .message_item_content_layout{
      margin-left: 20rpx;
      width: 495rpx;
      .message_item_content_top{
        display: flex;
        justify-content: space-between;
        align-items: center;
        .message_item_content_title{
          width: 230rpx;
          font-size: 30rpx;
          color: #1d1e20;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .message_item_content_time{
          font-size: 24rpx;
          color: #93989e;

        }

      }
      .message_item_content_tip{
        font-size: 24rpx;
        color: #8f9295;
        margin-top: 19rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

      }

    }
  }
}
</style>

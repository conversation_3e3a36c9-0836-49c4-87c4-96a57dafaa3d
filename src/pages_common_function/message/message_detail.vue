<!-- 消息详情 -->
<template>
  <view class="news-details">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('page.message.news.detail')"
      placeholder
      :autoBack="true"
      :leftIconColor="color.navigation"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    ></u-navbar>
    <!--#endif-->
    <!--头部-->
    <view class="news-details_title">{{ detail.title }}</view>
    <!-- 时间作者-->
    <view class="news_details_tip">
        <view class="news_details_tip_author">{{ authorName }}</view>
        <view class="news_details_tip_time">{{ detail.post_time }}</view>
    </view>
    <!-- 内容-->
    <view class="news_details_content">
        <u-parse :content="detail.content"></u-parse>
    </view>
  </view>
</template>

<script>
import { apiBackgroundMessagesMessagesGetMsgReceive } from '@/api/message'
import { unescapeHTML } from '@/utils/util.js'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      detail: {}, // 详细内容
      authorName: ""// 作者

    }
  },
  computed: {
    ...mapGetters(['color'])
  },
  onLoad(e) {
    // #ifdef MP-WEIXIN || MP-ALIPAY
    this.initData(e)
    // #endif
  },
  created() {
    // #ifdef H5
    this.initData()
    // #endif
  },
  methods: {
    /**
     * 初始化数据
     */
    initData(e) {
      var id
      // #ifdef H5
      id = this.$route.query.id
      this.authorName = this.$Route.query.author
      // #endif
      // #ifdef MP-WEIXIN || MP-ALIPAY
      id = e.id || ''
      this.authorName = e.author || ''
      // #endif
      console.log("id", id);
      this.$setNavBarTitle('page.message.news.detail')
      this.getMessageDetail(id)
    },
    /**
     * 获取信息详情
     * @param {*} id
     */
    async getMessageDetail(id) {
      this.$showLoading({
        title: '获取中...',
        mask: true
      })
      const [err, res] = await this.$to(apiBackgroundMessagesMessagesGetMsgReceive({ msg_no: id }))
      uni.hideLoading()
      if (err) {
        return
      }
      if (res.code === 0) {
        console.log("apiBackgroundMessagesMessagesGetMsgReceive", res)
        var data = res.data || {}
        var content = Reflect.has(data, "content") ? data.content : ""
        data.content = unescapeHTML(content)
        this.detail = data
      } else {
        uni.$u.toast(res.msg)
      }
    }
  }
}
</script>

<style lang="scss">
page{
  background: #ffffff;
}
.news-details {
  padding: 0 40rpx;

   .news-details_title{
    box-sizing: border-box;
    font-size: 42rpx;
    line-height: 60rpx;
    color: #1d1e20;
    padding: 40rpx 0;
   }
   .news_details_tip{
    font-size: 28rpx;
    color: #8f9295;
    display: flex;
    justify-content: space-between;
    margin-bottom: 63rpx;
   }
   .news_details_content{
    max-width: 750rpx;
   }

}
</style>

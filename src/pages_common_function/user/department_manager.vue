<template>
  <view class="department_container">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar :title="titleName" placeholder :autoBack="true" :leftIconColor="color.navigation" leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"></u-navbar>
    <!--#endif-->
    <!-- 搜索栏-->
    <view>
      <searchComponent :searchPlaceholder="searchDataHolder" @searchChange="searchChange" :hasSearchFocus="hasSearchFocus"
        @searchClear="searchClear" :inputMaxLength="25" @handlerSearch="handlerUserSearch" type="department"></searchComponent>
    </view>
    <!-- 筛选层 -->
    <view>
      <filterLayoutComponent v-if="isShowFilterLayout" :filterDataLayoutList="filterDataList"
        @handlerItemClick="handlerChooseItemClick"></filterLayoutComponent>
    </view>
    <!-- 列表页 -->
    <view class="user_list_main_container" v-show="!isShowEmptyView">
      <mescroll-uni ref="mescrollRef" :fixed="false" :safearea="false" :bottom="100" @init="mescrollInit"
        @down="downCallback" @up="upCallback" :down="{ auto: false }" :up="{ auto: false, isBounce: true }">
        <view class="user_list_main">
          <view v-for="(item, index) in userInfoList" :key="index" class="user_list_item">
            <!-- 头像层-->
            <view class="user_detail_head">
              <!-- 头像-->
              <view class="user_detail_head_left">
                <!-- 头像img-->
                <image class="user_detail_head_left_img" :src="item.face_url ? item.face_url : imgPath.IMG_HEAD_DEFAULT"
                  alt="图像" mode="scaleToFill"></image>
                <view class="user_detail_head_left_content">
                  <view class="user_detail_head_left_top">
                    <!-- 头像名字-->
                    <view class="user_detail_head_left_name" v-html="item.newName"></view>
                    <!-- 头像状态图标-->
                    <view class="user_detail_head_left_icon">
                      <image :src="item.gender_alias | getSexImage" alt="状态" mode="scaleToFill"></image>
                    </view>
                  </view>
                  <!-- 头像电话-->
                  <view class="user_detail_head_mobile">{{ item.phone | hidePhone }}</view>
                </view>
              </view>
              <!-- 状态-->
              <view class="user_detail_status_right">
                <view
                  :class="['card_status_item', item.person_status == 'ENABLE' ? 'card_status_green' : '', item.person_status == 'PERSON_QUIT' ? 'card_status_gray' : '', item.person_status == 'FREEZE' ? 'card_status_orange' : '', item.person_status == 'DELETE' ? 'card_status_red' : '']">
                  {{ item.account_status_alias }}
                </view>
                <view
                  :class="['card_status_item', item.card_status == 'ENABLE' ? 'card_status_green' : '', item.card_status == 'UNUSED' ? 'card_status_gray' : '', item.card_status == 'LOSS' ? 'card_status_orange' : '', item.card_status == 'QUIT' || item.card_status == 'DELETE' ? 'card_status_red' : '']">
                  {{ item.card_status_alias }}
                </view>
              </view>

            </view>
            <!--分割灰线-->
            <view class="horizontal_cell_line"></view>
            <!--底部按钮-->
            <view class="user_detail_btns">
              <view class="user_detail_btns_content">
                <u-button :customStyle="customStyleBtn" class="user_detail_btn_style" plain :color="color.themeColor"
                  @click="viewDetail(item)">{{ viewDetailTxt }}</u-button>
                <u-button
                  v-if="item.card_status == 'ENABLE' && item.person_status == 'ENABLE'"
                  :customStyle="customStyleBtn" class="user_detail_btn_style" :color="color.colorBtnRedWarning"
                  @click="cardWithdrawal(item)">{{ cardWithdrawalTxt }}</u-button>
                <u-button
                  v-if="item.card_status == 'ENABLE' && item.person_status == 'ENABLE'"
                  :customStyle="customStyleBtn" class="user_detail_btn_style" :color="color.colorBtnRed"
                  @click="lossReporting(item)">{{ lossReportingTxt }}</u-button>
                <u-button
                  v-if="item.card_status == 'LOSS' && item.person_status == 'ENABLE'"
                  :customStyle="customStyleBtn" class="user_detail_btn_style" :color="color.colorBtnBlack"
                  @click="cancelLoss(item)">{{ cancelLossTxt }}</u-button>
                <u-button v-if="item.person_status == 'ENABLE'"
                  :customStyle="customStyleBtn" class="user_detail_btn_style" :color="color.colorBtnYellow"
                  @click="freezeAccount(item)">{{ freezeAccountTxt }}</u-button>
                <u-button v-if="item.person_status == 'ENABLE'"
                  :customStyle="customStyleBtn" class="user_detail_btn_style" :color="color.colorBtnOrange"
                  @click="uploadFace(item)">{{ uploadFaceTxt }}</u-button>
              </view>

            </view>
          </view>

        </view>
      </mescroll-uni>
    </view>
    <!-- 空白页 -->
    <emptyComponent :emptyContent="emptyContent" v-if="isShowEmptyView"></emptyComponent>
    <!--#ifdef MP-WEIXIN || MP-ALIPAY -->
    <CustomDialogComponent ref="customDialog"></CustomDialogComponent>
    <!--#endif-->

  </view>
</template>

<script>
import searchComponent from "@/components/SearchComponent/SearchComponent"
import emptyComponent from "@/components/EmptyComponent/EmptyComponent"
import filterLayoutComponent from "@/components/FilterLayoutComponent/FilterLayoutComponent"
import { getCardServiceCardUserList } from '@/api/user'
import { mapGetters } from 'vuex'
import { deepClone, hidePhoneNum, checkClient } from "../../utils/util"
import cache from '../../utils/cache'
import comDic from '../../common/comDic'
import { setCardWithdrawal, setCardLoss, setCardFreeze, setCardCancelLoss, setUploadFace, getSexImageByType } from '@/utils/userUtil'
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";

export default {
  data() {
    return {
      imgPath: this.$imgPath,
      searchDataHolder: this.$t('page.user.searchHolder'), // 搜索提示
      viewDetailTxt: this.$t('page.user.view.detail'), // 查看详情  按钮名字
      cardWithdrawalTxt: this.$t('page.user.card.withdrawal'), // 退卡 按钮名字
      lossReportingTxt: this.$t('page.user.card.loss.reporting'), // 挂失卡 按钮名字
      cancelLossTxt: this.$t('page.user.card.cancel.loss'), // 取消挂失 按钮名字
      freezeAccountTxt: this.$t('page.user.freeze.account'), // 冻结账户 按钮名字
      uploadFaceTxt: this.$t('page.user.upload.face'), // 上传face 按钮名字
      emptyContent: this.$t('tip.no.search.content'), // 空数据提示
      isShowEmptyView: false, // 是否显示空页面
      userInfoList: [], // 个人信息列表
      userInfoListClone: [], // 个人信息列表 clone
      filterDataList: [
        {
          title: '录入人脸',
          chooseItem: '全部',
          dataList: comDic.DIC_UPLOAD_FACE
        },
        {
          title: '卡状态',
          chooseItem: '全部',
          dataList: comDic.DIC_CARD_STATUS
        },
        {
          title: '账户状态',
          chooseItem: '全部',
          dataList: comDic.DIC_USER_STATUS
        }
      ],
      customStyleBtn: { // 按钮自定义样式这里为了兼容微信与支付宝
        width: '120rpx',
        height: '48rpx',
        borderRadius: '6rpx',
        fontSize: '24rpx',
        margin: '10rpx',
        padding: '0 !important'

      },
      isShowFilterLayout: false, // 是否显示下拉筛选layout
      hasSearchFocus: false, // 是否自动获取焦点
      pageNo: 1, // 当前页面no
      pageSize: 10, // 每页显示条数
      titleName: '', // 标题名称
      type: '', // 页面类型，根据页面类型控制标题，入参等参数
      groupId: '', // 组ID
      userId: '', // 用户ID
      params: { // 筛选入参
        page: 1,
        page_size: 10,
        is_self_org: false
      },
      searchValue: '', // 筛选值
      dialogTitle: this.$t('tip.prompt') // 弹窗title
    }
  },
  mixins: [MescrollMixin],
  computed: {
    ...mapGetters(['color'])
  },
  created() {
    // 初始化数据
    // #ifdef H5
    this.initData()
    // #endif
  },
  components: { searchComponent, filterLayoutComponent, emptyComponent },
  /**
   * 页面加载
   */
  onLoad(e) {
    console.log("用户列表页面加载", e)
    // #ifdef MP-WEIXIN || MP-ALIPAY
    this.initData(e)
    // #endif
    var that = this
    uni.$on(this.$common.MSG_UPLOAD_FACE_SUCCESS, () => {
      console.log("收到上传成功");
      that.getCardUserListPost(this.searchData)
    })
    uni.$on(this.$common.MSG_UPDATE_PERSON_INFO, () => {
      console.log("收到更新成功");
      that.getCardUserListPost(this.searchData)
    })
  },
  onShow() { },
  onUnload() {
    uni.$off(this.$common.MSG_UPLOAD_FACE_SUCCESS)
    uni.$off(this.$common.MSG_UPDATE_PERSON_INFO)
  },
  filters: {
    /**
      * 根据返回的内容过滤男女图标
      * @param {*} type
      */
    getSexImage(type) {
      return getSexImageByType(type)
    },
    /**
     * 隐藏手机
     */
    hidePhone(phone) {
      return hidePhoneNum(phone)
    }
  },
  methods: {

    /**
     * 页面进入初始化数据
     */
    initData(e) {
      // 页面进来先获取是从哪个页面过来的，
      var queryData = {}
      // #ifdef MP-WEIXIN || MP-ALIPAY
      queryData = e
      // #endif
      // #ifdef H5
      queryData = this.$route.query || {}
      // #endif
      console.log("type", queryData);
      if (queryData) {
        this.type = queryData.type ? queryData.type : ''
        this.groupId = queryData.groupId ? queryData.groupId : ''
        this.userId = queryData.userId ? queryData.userId : ''
        console.log("type", this.type, queryData);
        // 筛选layout在不是点击筛选点击跳入的时候显示,并且自动获取焦点
        this.isShowFilterLayout = this.type !== "search"
        this.hasSearchFocus = this.type === "search"
        this.params.page = this.pageNo
        this.params.page_size = this.pageSize
        this.searchData('', this.type !== "search")
      }
      // 设置名称
      var cacheTitle = cache.get(this.$common.TITLE_DEPARTMENT_NAME) || ''
      this.titleName = cacheTitle && cacheTitle.length > 0 ? cacheTitle : this.$t('title.search')
      this.$setNavBarTitle(this.titleName)
      // 判断平台
      var platform = checkClient()
      this.uploadFaceTxt = platform === 'mp-weixin' ? this.$t("page.user.upload.pic") : this.$t("page.user.upload.face")
    },
    /**
     * 下拉刷新返回
     */
    downCallback(page) {
      console.log(" downCallback page", page);
      this.pageNo = 1
      this.params.page = this.pageNo
      this.getCardUserListPost(this.searchData)
    },
    /**
     * 上拉加载更多
     * @param {*} page
     */
    upCallback(page) {
      console.log(" upCallback page", page);
      uni.$u.toast('upCallback page', page)
      this.pageNo++
      this.params.page = this.pageNo
      this.getCardUserListPost(this.searchData)
    },
    /**
     * 搜索数据
     * @param  searchValue 搜索内容
     * @param  isSearchData 是否搜索数据，如果是搜索，进来是先不进行搜索数据的
     */
    searchData(searchValue, isSearchData) {
      // 根据类型组参
      if (this.type === 'department') { // 部门组参
        this.params.card_department_group_id = this.groupId;
      }
      if (this.type === 'user') { // 用户组参
        this.params.card_user_group_ids = [this.userId]
      }
      if (searchValue && searchValue.length > 0) {
        this.params.search_condition = searchValue
      }
      // 搜索用户数据
      if (isSearchData) {
        this.getCardUserListPost(searchValue)
      }
    },
    /**
     * 获取用户列表
     * @param {*} searchValue  搜索内容
     */
    getCardUserListPost(searchValue) {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      getCardServiceCardUserList(this.params)
        .then(res => {
          if (res.code === 0) {
            uni.hideLoading()
            var data = res.data ? res.data : {}
            console.log("apiBackgroundAdminUserList data", data);
            var resultList = []
            if (Reflect.has(data, 'results')) {
              resultList = data.results || []
            }
            var count = data.count ? data.count : 0
            console.log("apiBackgroundAdminUserList resultList", resultList);

            if (resultList && resultList.length > 0 && this.pageNo === 1) {
              // 刚开始加载
              this.userInfoList = this.changeListColor(resultList, searchValue)
              console.log("apiBackgroundAdminUserList userinfo", this.userInfoList);
            } else if (resultList && resultList.length > 0 && this.pageNo !== 1) {
              // 增量加载，加载更多
              this.userInfoList = this.changeListColor(this.userInfoList.concat(resultList), searchValue)
              console.log("apiBackgroundAdminUserList userinfo", this.userInfoList);
            } else if (this.pageNo !== 1 && resultList.length === 0) {
              console.log("没有加载更多了");
              uni.$u.toast("没有更多了")
            } else {
              // 其他情况
              this.$set(this, "userInfoList", [])
              uni.hideLoading()
              var resultMsg = res.msg && res.msg !== '成功' ? res.msg : '暂无数据'
              uni.$u.toast(resultMsg)
            }
            // 如果是第一页并且没有数据要展示没有数据层
            this.isShowEmptyView = this.userInfoList.length === 0 && this.pageNo === 1
            this.mescroll.setPageNum(this.pageNo)
            this.mescroll.endBySize(this.pageSize, count)
          } else {
            console.log("code !=0");
            this.$set(this, "userInfoList", [])
            uni.hideLoading()
            uni.$u.toast(res.msg || "")
            this.mescroll.endErr()
          }
        })
        .catch(err => {
          this.$set(this, "userInfoList", [])
          console.log("err list", err);
          if (err.message) {
            uni.$u.toast(err.message)
          }
          this.mescroll.endErr()
        })
    }, /**
     * 用户输入监听
     * @param {*} e
     */
    searchChange(e) {
      console.log("searchChange", e)
    },
    /**
     * 查看详情
     */
    viewDetail(item) {
      console.log("viewDetail", item);
      // 存缓存存人员信息
      cache.set(this.$common.KEY_PERSON_USER_INFO, item)
      this.$miRouter.push({
        path: '/pages_common_function/user/user_detail'
      })
    },
    /**
     * 退卡
     */
    async cardWithdrawal(itemData) {
      console.log("退卡");
      var [error, res] = await this.$to(setCardWithdrawal(itemData.id, this.dialogTitle, this.$t('tip.prompt.card.withdrawal'), this))
      // 退卡成功刷新列表
      if (res && res === '退卡成功' && !error) {
        console.log("退卡返回", res);
        setTimeout(() => {
          this.searchData(this.searchValue, true)
        }, 500)
      }
    },
    /**
     * 挂失卡
     */
    async lossReporting(itemData) {
      console.log("挂失卡");
      var [error, res] = await this.$to(setCardLoss(itemData.id, this.dialogTitle, this.$t('tip.prompt.card.loss.reporting'), this))
      console.log("挂失卡", error, res);
      if (res && res === '挂失成功' && !error) {
        console.log("挂失成功");
        setTimeout(() => {
          console.log("刷新");
          this.searchData(this.searchValue, true)
        }, 500)
      }
    },
    /**
     * 取消挂失
     */
    async cancelLoss(itemData) {
      console.log("取消挂失");
      var [error, res] = await this.$to(setCardCancelLoss(itemData.id, this.dialogTitle, this.$t('tip.prompt.card.cancel.loss'), this))
      if (res && res === '取消挂失成功' && !error) {
        setTimeout(() => {
          this.searchData(this.searchValue, true)
        }, 500)
      }
    },
    /**
     * 冻结账户
     */
    async freezeAccount(itemData) {
      console.log("冻结账户");
      var [error, res] = await this.$to(setCardFreeze(itemData.id, this.dialogTitle, this.$t('tip.prompt.card.freeze.account'), this))
      if (res && res === '冻结成功' && !error) {
        setTimeout(() => {
          this.searchData(this.searchValue, true)
        }, 500)
      }
    },
    /**
     * 上传face
     */
    async uploadFace(itemData) {
      console.log("上传face");
      var [error, res] = await this.$to(setUploadFace(itemData.id, itemData.company_id, itemData.person_no, this))
      if (res && res === this.uploadFaceTxt + '成功' && !error) {
        // 更新操作
        this.searchData(this.searchValue, true)
      }
    },
    /**
     * 对列表数据进行高亮插入
     * @param {} listData
     * @param {*} searchData
     */
    changeListColor(listData, searchData) {
      if (!searchData || searchData.length === 0) {
        this.userInfoListClone = deepClone(listData.map(item => {
          item.newName = item.name
          return item
        }))
        return listData
      }
      // 为了防止列表复用问题要先拷贝一份
      var newListData = []
      var searChList = deepClone(listData)
      // 循环遍历每个项的name ，里面有searchData的关键子的话就进行插入颜色并赋值给新列表
      searChList.forEach((item) => {
        // 要拿命中关键字的项
        if (item.name.indexOf(searchData) !== -1) {
          let replaceReg = new RegExp(searchData, "ig")
          let replaceString = `<span style="color:#fd953c">${searchData}</span>`
          var newName = item.name.replace(replaceReg, replaceString)
          item.newName = newName
        } else {
          item.newName = item.name
        }
        newListData.push(item)
      })
      console.log("newListData", newListData);
      return newListData
    },
    /**
     * 筛选清除清除
     */
    searchClear() {
      this.userInfoList = deepClone(this.userInfoListClone)
      this.isShowEmptyView = this.userInfoList.length === 0
    },
    /**
     * 每个筛选选项的点击
     * @param {每项的数据} itemData
     * @param {点击的项所在列表的位置} index
     */
    handlerChooseItemClick(itemData, index) {
      console.log("handlerChooseItemClick", itemData, index);
      // 设置点击记录
      this.filterDataList[index].chooseItem = itemData.name
      this.filterDataList[index].title = itemData.name
      // 筛选列表
      switch (index) {
        case 0:// 录入人脸
          this.params.has_face = itemData.value
          if (itemData.value === '') {
            delete this.params.has_face
            this.filterDataList[index].title = '录入人脸'
          } else {
            this.filterDataList[index].title = itemData.name
          }
          break;
        case 1:// 卡状态
          this.params.card_status = [itemData.value]
          if (itemData.value === '') {
            delete this.params.card_status
            this.filterDataList[index].title = '卡状态'
          } else {
            this.filterDataList[index].title = itemData.name
          }
          break;
        case 2:// 账户状态
          this.params.person_status = itemData.value
          if (itemData.value === '') {
            delete this.params.person_status
            this.filterDataList[index].title = '账户状态'
          } else {
            this.filterDataList[index].title = itemData.name
          }
          break;
        default:
          break;
      }
      this.pageNo = 1
      this.getCardUserListPost(this.searchValue)
    },
    /**
     * 用户点击搜索
     */
    handlerUserSearch(e) {
      this.pageNo = 1
      if (e && e.length > 0) {
        this.searchValue = e
        this.searchData(e, true)
      }
    }

  }

}
</script>

<style lang="scss" scoped>
.department_container {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
// #ifdef H5
.user_list_main_container {
  width: 100%;
  height: calc(100% - 252rpx);
}
// #endif
// #ifdef MP-WEIXIN
.user_list_main_container {
  width: 100%;
  height: calc(100vh - 350rpx);
}
// #endif

.user_list_item {
  width: 670rpx;
  min-height: 271rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 20rpx 40rpx;

  .user_detail_head {
    height: 180rpx;
    display: flex;
    justify-items: center;
    justify-content: space-between;
    margin: 0 auto;

    .user_detail_head_left {
      display: flex;
      align-items: center;
      max-width: 400rpx;

      .user_detail_head_left_img {
        width: 100rpx;
        min-width: 100rpx;
        height: 100rpx;
        border-radius: 50rpx;
        margin: 40rpx 30rpx;
      }

      .user_detail_head_left_content {
        .user_detail_head_left_top {
          display: flex;
          align-items: center;

          .user_detail_head_left_name {
            display: inline;
            font-size: 30rpx;
            line-height: 42rpx;
            color: #1d1e20;
            // max-width: 200rpx;
          }

          .user_detail_head_left_icon {
            width: 34rpx;
            height: 34rpx;
            margin: 8rpx;

            & image {
              width: 34rpx;
              height: 34rpx;
            }
          }
        }

        .user_detail_head_mobile {
          display: block;
          font-size: 24rpx;
          color: #8f9295;
          margin-top: 13rpx;
        }
      }
    }

    .user_detail_status_right {
      display: flex;
      margin-right: 40rpx;
      justify-content: center;
      align-items: center;
      overflow: hidden;
    }

    .card_item {
      display: inline-flex;
      align-items: center;
    }

    .card_txt {
      width: 100rpx;
      font-size: 16rpx;
      text-align: center;
      margin-top: 10rpx;
      height: 40rpx;
      line-height: 40rpx;
      color: #8f9295;
    }

    .card_status_item {
      width: 112rpx;
      height: 40rpx;
      text-align: center;
      line-height: 42rpx;
      font-size: 24rpx;
      border-radius: 20rpx;
      margin-left: 10rpx;
    }

    .card_status_green {
      color: #51d854;
      background-color: #EDFBEE;
    }

    .card_status_gray {
      color: #989898;
      background-color: #f5f5f5;
    }

    .card_status_orange {
      color: #fd7c3c;
      background-color: #FFF2EB;
    }

    .card_status_red {
      color: #ff6349;
      background-color: #FFEFEC;
    }

  }

  .horizontal_cell_line {
    width: 610rpx;
    height: 1rpx;
    background-color: #eae9ed;
    margin: 0 auto;
  }

  .user_detail_btns {
    width: 100%;

    .user_detail_btns_content {
      min-height: 48rpx;
      display: flex;
      justify-content: flex-end;
      margin: 10rpx 30rpx;
      flex-wrap: wrap;

      .user_detail_btn_style {
        width: 120rpx;
        height: 48rpx;
        border-radius: 6rpx;
        font-size: 24rpx;
        margin: 10rpx;
        padding: 0 !important;
      }
    }

  }

}</style>

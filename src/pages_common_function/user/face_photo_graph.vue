<template>
  <view class="camera-box">
    <!--#ifdef H5-->
    <!-- Main Start -->
    <camera @error="error" class="camera" :device-position="reversal" flash="off">
      <cover-view class="camera-view">
        <!-- 提示 -->
        <cover-view class="tips white nr">在光线充足的地方，拍照效果更佳</cover-view>

        <!-- 这是文字提示（不需要删除即可） -->
        <cover-image class="face" :src="imgPath.IMG_FACE"></cover-image>

        <!-- Modal 弹窗 -->
        <cover-view class="modal bg-white xs" v-if="show">
          <cover-view>1.确保拍摄的图片只存在一张face图片信息</cover-view>
          <cover-view class="m-t-30">2.确保拍摄的图片清晰，无模糊</cover-view>
          <cover-view class="m-t-30">3.确保拍摄的图片，face位于图片正</cover-view>
          <cover-view class="m-l-14">中央位置且占据图片超过60%的区域</cover-view>
          <cover-view class="m-t-30">4.确保拍摄时光线充足</cover-view>

          <cover-view class="confirm-btn flex flex-center white" @click="show = false">确定</cover-view>
        </cover-view>
      </cover-view>
    </camera>
    <!-- Main End -->

    <!-- Footer Start -->
    <view class="footer flex row-between col-center">
      <view class="primary xxl" @click="$miRouter.back()">取消</view>
      <image class="photho" @click="takePhoto" :src="imgPath.IMG_FACE_PHOTO"></image>
      <image class="reversal" @click="changeReversal" :src="imgPath.IMG_FACE_REVERSAL"></image>
    </view>
    <!-- Footer End -->
    <!--#endif-->
  </view>
</template>

<script>
import cache from "@/utils/cache";

export default {
  // #ifdef H5
  data() {
    return {
      imgPath: this.$imgPath,
      src: '', // 拍照后图像路径(临时路径)
      show: true, // 拍照提示
      reversal: 'front',
      userId: '', // 用户ID，
      companyId: '', // 公司id,
      personNo: ''// 用户编号

    }
  },

  onLoad(e) {
    console.log("onload", e);
    this.$setNavBarTitle('pages.info.face_photograph')
    this.initData(e)
  },

  methods: {
    /**
     * 初始化数据
     */
    initData(e) {
      this.userId = e.userId ? e.userId : ''
      this.companyId = e.companyId ? e.companyId : ''
      this.personNo = e.personNo ? e.personNo : ''
      console.log("this.userId", e, this.userId, this.companyId, this.personNo);
    },
    // 取消/重新拍照按钮
    cancelBtn() {
      this.show = false
      this.src = ''
    },

    // 拍照按钮
    takePhoto() {
      if (this.show) return
      // 创建camera上下文CameraContext对象
      // 具体查看微信api文档
      const ctx = uni.createCameraContext()
      // 实拍照片配置
      ctx.takePhoto({
        quality: 'high', // 成像质量
        success: res => {
          // 成功回调
          this.src = res.tempImagePath // tempImagePath为api返回的照片路径
          cache.set(this.$common.KEY_FACE_PATH, this.src)
          this.toResultPage()
        },
        fail: error => {
          // 失败回调
          console.log(error)
        }
      })
    },

    // 切换相机
    changeReversal() {
      if (this.show) return
      if (this.reversal === 'back') this.reversal = 'front'
      else this.reversal = 'back'
    },

    error(event) {
      var error = event.detail ? (event.detail.errMsg || "") : ''
      var isDeny = false
      console.log("event", event, error);
      if (error && error.indexOf("deny") !== -1) {
        // 沒有授權
        error = '请在右上角设置中开启授权，否则无法进行拍照'
        isDeny = true
      }
      uni.showModal({
        title: '提示',
        showCancel: false,
        confirmText: isDeny ? '授权' : '確定',
        content: error,
        success(res) {
          if (res.confirm) {
            if (isDeny) {
              uni.openSetting({
                success(res) {
                  uni.navigateBack()
                }
              })
            } else {
              uni.navigateBack()
            }
          }
        }
      })
    },

    /**
     * 跳转结果页
     * @param {*} resultFace
     * @param {*} error
     */
    toResultPage() {
      this.$miRouter.push({
        path: '/pages_common_function/user/face_photograph_result',
        query: {
          return: 2,
          userId: this.userId,
          companyId: this.companyId,
          personNo: this.personNo

        }
      })
    }
  }
  // #endif
}
</script>

<style lang="scss">
page {
  background-color: #ffffff;
}

.camera-box {
  width: 100%;
  position: relative;
  height: calc(100vh - env(safe-area-inset-bottom));
}

.camera {
  width: 100%;
  z-index: 1;
  height: calc(100vh - (240rpx + env(safe-area-inset-bottom)));

  .camera-view {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 9999;

    .tips {
      position: absolute;
      padding: 26rpx 62rpx;
      left: 50%;
      top: 70rpx;
      z-index: 999;
      border-radius: 20rpx;
      transform: translate(-50%, -50%);
      background-color: rgba($color: #000000, $alpha: 0.5);
    }
    // 脸部轮廓
    .face {
      width: 100%;
      height: auto;
      background: none;
      position: absolute;
      left: 0%;
      top: 0%;
    }
    // 弹窗
    .modal {
      position: absolute;
      left: 50%;
      top: 50%;
      width: 450rpx;
      padding: 54rpx;
      border-radius: 20rpx;
      transform: translate(-50%, -50%);
      // 确定按钮
      .confirm-btn {
        width: 264rpx;
        height: 64rpx;
        margin: 0 auto;
        line-height: 64rpx;
        text-align: center;
        margin-top: 40rpx;
        border-radius: 10rpx;
        background-color: $color-primary;
      }
    }
  }
}

// 底部
.footer {
  height: 240rpx;
  padding: 0 60rpx;
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;

  // 拍照按钮
  .photho {
    width: 170rpx;
    height: 170rpx;
  }

  // 相机反转按钮
  .reversal {
    width: 62rpx;
    height: 62rpx;
  }
  .m-t-30{
    font-size: 26rpx;
  }
  .m-l-14{
    font-size: 14rpx;
  }
}

</style>

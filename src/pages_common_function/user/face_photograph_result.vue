<template>
  <view class="face_gather" catchtouchmove="true" @touchmove.stop.prevent="moveHandle">
    <!-- Main Start -->
    <view class="face-info bg-white" v-if="isLoadingSuccess">
      <template v-if="isSuccess">
        <view class="flex flex-center">
          <u-icon name="checkmark-circle-fill" color="#11E69E" size="50rpx"></u-icon>
          <text class="xxl f-w-500 black m-l-20">采集成功</text>
        </view>
      </template>
      <template v-else>
        <view class="flex flex-center">
          <u-icon name="info-circle-fill" color="#FF4C4D" size="50rpx"></u-icon>
          <text class="xxl f-w-500 black m-l-20">采集失败</text>
        </view>
      </template>

      <view class="flex flex-center m-t-26">
        <view class="img-box" :class="isSuccess == true ? 'success' : 'error'">
          <u-image width="238rpx" height="238rpx" :src="face" mode="aspectFill"></u-image>
        </view>
      </view>

      <view class="text-center m-t-30">{{ errorMsg }}</view>

      <view class="flex flex-center m-t-50">
        <view class="m-r-40">
          <u-button @click="navBack(1)" type="primary" size="small" :plain="true" text="重新上传"></u-button>
        </view>
        <template v-if="isSuccess">
          <view>
            <u-button @click="navBack(returnStep)" type="primary" size="small" text="完成"></u-button>
          </view>
        </template>
        <template v-else>
          <view>
            <u-button @click="navBack(returnStep)" type="primary" size="small" text="取消"></u-button>
          </view>
        </template>
      </view>
    </view>
    <!-- Main End -->
    <!-- 画布layout-->
    <view class="face_phone_canvas" v-if="isShowCanvas">
      <canvas canvas-id="upfaceCanvas"></canvas>
    </view>
  </view>
</template>

<script>
import cache from '@/utils/cache'
import { compressImageWeixin } from '@/utils/uploadFaceImg'
import { uploadFilePromise, uploadUserFace } from '@/utils/userUtil'
import { checkClient } from '@/utils/util'
export default {
  data() {
    return {
      face: '',
      returnStep: 1,
      isSuccess: true, // 是否采集成功，
      userId: '', // 用户ID，
      companyId: '', // 公司id,
      personNo: '', // 用户编号
      errorMsg: "", // 错误信息
      isLoadingSuccess: false, // 是否上传信息成功
      isShowCanvas: true
    }
  },
  onLoad(e) {
    console.log("onLoad", e);
    this.initData(e)
  },

  methods: {
    navBack(steps) {
      if (parseInt(steps) === 2) {
        uni.$emit(this.$common.MSG_UPLOAD_FACE_SUCCESS, true)
      }
      uni.navigateBack({
        delta: parseInt(steps)
      })
    },
    /**
     * 初始化数据
     */
    initData(e) {
      this.$setNavBarTitle('pages.info.face_photograph')
      this.face = cache.get(this.$common.KEY_FACE_PATH)
      this.returnStep = e.return
      this.userId = e.userId ? e.userId : ''
      this.companyId = e.companyId ? e.companyId : ''
      this.personNo = e.personNo ? e.personNo : ''
      console.log("this.userId", e, this.userId, this.companyId, this.personNo);
      console.log("this.face", this.face, this.returnStep);
      // 上传face
      this.uploadFace(this.face)
    },
    /**
     * 上传
     * @param {*} res
     */
    async uploadFace(res) {
      console.log("res", res);
      this.isShowCanvas = true
      let imgUrl = await compressImageWeixin('upfaceCanvas', res)
      this.isShowCanvas = false
      // 先上传文件形成url
      let [error, resultUrl] = await this.$to(uploadFilePromise(imgUrl))
      console.log("resultUrl", resultUrl);
      if (resultUrl && !error) {
        // 上传face
        let [errorFace, resultFace] = await this.$to(uploadUserFace(this.userId, this.companyId, this.personNo, resultUrl))
        console.log("resultFace", resultFace);
        this.isLoadingSuccess = true
        var uploadTxt = checkClient() === "mp-weixin" ? this.$t("page.user.upload.pic") : this.$t("page.user.upload.face")
        if (resultFace === uploadTxt + "成功") {
          this.isSuccess = true
        } else {
          this.errorMsg = errorFace
          this.isSuccess = false
        }
      } else {
        this.isSuccess = false
        this.isLoadingSuccess = true
      }
    },
    /**
    * js禁止滚动
    */
    moveHandle() {

    }
  }

}
</script>

<style lang="scss">
page {
  background-color: #ffffff;
}

.face_gather {
  padding: 30rpx 40rpx;

  // 基本信息
  .face-info {
    margin-top: 100rpx;

    .img-box {
      width: 240rpx;
      height: 240rpx;
      box-sizing: border-box;
      border-radius: 20rpx;
      overflow: hidden;
    }

    .success {
      border: 3px solid $color-primary;
    }

    .error {
      border: 3px solid #ff4c4d;
    }
  }
}

.face_phone_canvas {
  position: absolute;
  bottom: 0;
  left: 100%;

  & canvas {
    width: 375rpx;
    height: 375rpx;
  }
}</style>

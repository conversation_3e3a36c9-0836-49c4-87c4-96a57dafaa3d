<template>
   <view>
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="titleName"
      placeholder
      :autoBack="true"
      :leftIconColor="color.navigation"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    ></u-navbar>
    <!--#endif-->
     <!-- 列表页 -->
     <view class="user_list_main_container" v-if="!isShowEmptyView">
      <mescroll-uni ref="mescrollRef" :fixed="false" :safearea="true" :bottom="0" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="{ auto: false }" :up="{ auto: false }" >
      <view class="user_list_main">
        <view v-for="(item,index) in departmentList" :key="index" class="user_list_item" @click="handlerItemClick(item,index)">
          <view>
            <view class="user_list_item_txt">{{ item.group_name }}</view>
            <view class="user_list_item_number">{{ "("+item.card_counts+")"}}</view>
          </view>
          <u-icon name="arrow-right" color="#8f9295" size="18"></u-icon>
        </view>
      </view>
     </mescroll-uni>
   </view>
   <!-- 空白页 -->
   <EmptyComponent :emptyContent="emptyContent" v-if="isShowEmptyView"></EmptyComponent>
   </view>
</template>

<script>
import { getGroupListByType } from '@/api/user'
import { mapGetters } from 'vuex'
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import cache from '../../utils/cache';

export default {
  data() {
    return {
      departmentList: [], // 部门列表
      pageNo: 1, // 当前页
      pageSize: 10, // 每页显示个数,
      titleName: '',
      type: '',
      params: {},
      emptyContent: this.$t('tip.list.empty'), // 空数据提示
      isShowEmptyView: false // 是否显示空页面
    }
  },
  mixins: [MescrollMixin],
  computed: {
    ...mapGetters(['color'])
  },
  /**
   * 页面加载
   */
  onLoad(e) {
    console.log("用户部门页面加载", e)
    // #ifdef MP-WEIXIN || MP-ALIPAY
    this.initData(e)
    // #endif
  },
  created() {
    console.log("created");
    // #ifdef H5
    this.initData()
    // #endif
  },
  methods: {
    /**
     * 初始化数据
     */
    initData(e) {
      // 类型页面传递过来
      // #ifdef H5
      this.type = this.$route.query.id
      // #endif
      // #ifdef MP-WEIXIN || MP-ALIPAY
      this.type = parseInt(e.id) || ''
      // #endif
      this.titleName = this.type === 1 ? this.$t('title.user.department') : this.$t('page.user.manager.user.grouping')
      this.$setNavBarTitle(this.titleName)
      // 获取部门列表
      this.params = {
        page: this.pageNo,
        page_size: this.pageSize,
        status: 'enable'
      }
      var id = cache.get(this.$common.KEY_USER_ORGAN)
      if (id) {
        this.params.organization = id
      }
      if (this.type === 1) {
        this.params.level = 0
      } else {
        this.params.is_show_other = true
      }
      this.getUserList(this.params, this.type)
    },
    /**
     * 下拉刷新返回
     */
    downCallback(page) {
      console.log(" downCallback page", page);
      this.pageNo = 1
      this.params.page = this.pageNo
      this.getUserList(this.params, this.type)
    },
    /**
     * 上拉加载更多
     * @param {*} page
     */
    upCallback(page) {
      console.log(" upCallback page", page);
      this.pageNo++
      this.params.page = this.pageNo
      this.getUserList(this.params, this.type)
    },

    /**
     * 获取列表
     * @param {*} parmas
     * @type
     */
    getUserList(parmas, type) {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      getGroupListByType(parmas, type)
        .then(res => {
          if (res.code === 0) {
            uni.hideLoading()
            var data = res.data ? res.data : {}
            console.log("getCardServiceCardDepartmentGroupList data", data);
            var resultList = []
            if (Reflect.has(data, 'results')) {
              resultList = data.results
            }
            var count = data.count ? data.count : 0
            console.log("getCardServiceCardDepartmentGroupList resultList", resultList);
            // 如果是第一页并且没有数据要展示没有数据层
            this.isShowEmptyView = resultList.length === 0 && this.pageNo === 1

            if (resultList && resultList.length > 0 && this.pageNo === 1) {
              // 刚开始加载
              this.departmentList = resultList
              console.log("getCardServiceCardDepartmentGroupList userinfo", this.departmentList);
            } else if (resultList && resultList.length > 0 && this.pageNo !== 1) {
              // 增量加载，加载更多
              this.departmentList = this.departmentList.concat(resultList)
              console.log("getCardServiceCardDepartmentGroupList userinfo", this.departmentList);
            } else {
              // 其他情况
              uni.hideLoading()
              uni.$u.toast(res.msg === "成功" ? "暂无数据" : res.msg)
            }
            console.log("getCardServiceCardDepartmentGroupList userinfo", this.departmentList.length, count);
            this.mescroll.setPageNum(this.pageNo)
            this.mescroll.endBySize(this.pageSize, count)
          } else {
            uni.hideLoading()
            uni.$u.toast(res.msg)
            this.mescroll.endErr()
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
          this.mescroll.endErr()
        })
    },
    /**
     * 用户输入监听
     * @param {*} e
     */
    searchChange(e) {
      console.log("searchChange", e)
      if (e && e.length) {
        // 根据用户输入的内容重新获取列表

      } else {
        // 没有输入的时候还原列表
      }
    },
    /**
     * 项目点击
     * @param {} item
     * @param {} index
     */
    handlerItemClick(item, index) {
      console.log("handlerItemClick", item, index);
      cache.set(this.$common.TITLE_DEPARTMENT_NAME, item.group_name)
      this.$miRouter.push({
        path: '/pages_common_function/user/department_manager',
        query: {
          id: index,
          type: this.type === 1 ? 'department' : 'user',
          groupId: this.type === 1 ? item.id : '',
          userId: this.type !== 1 ? item.id : ''
        }
      })
    }
  }

}

</script>

<style lang="scss">
  page {
    padding: 0;
    background: #F0F3F5;
  }
  .user_list_main_container{
    margin-top: 50rpx;
    height: calc(100vh - 88rpx);
  }
  .user_list_main{
     display: block;
     .user_list_item{
      width: 670rpx;
      height: 120rpx;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      justify-items: center;
      background-color: #ffffff;
      border-radius: 12px;
      margin:20rpx auto;
      padding:0 30rpx;
      font-size: 32rpx;
      .user_list_item_txt{
        display: inline;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 0px;
        color: #1d1e20;
        line-height: 120rpx;
        text-align: center;
      }
      .user_list_item_number{
        display: inline;
        color: #8f9295;
      }

     }

  }

</style>

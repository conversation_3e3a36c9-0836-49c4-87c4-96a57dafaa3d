<template>
  <view class="user_detail_main">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('title.personnel.information')"
      placeholder
      :autoBack="true"
      :leftIconColor="color.navigation"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    ></u-navbar>
    <!--#endif-->
    <!-- 头像层-->
    <view class="user_detail_head">
      <!-- 头像-->
      <view class = "user_detail_head_left">
        <!-- 头像img-->
        <image class="user_detail_head_left_img" :src="userData.head.imgUrl?userData.head.imgUrl:imgPath.IMG_HEAD_DEFAULT" alt="图像" mode="scaleToFill"></image>
        <view  class="user_detail_head_left_content">
          <view class="user_detail_head_left_top">
            <!-- 头像名字-->
            <view class="user_detail_head_left_name">{{ userData.head.name }}</view>
             <!-- 头像状态图标-->
            <image class="user_detail_head_left_icon" :src="userData.head.statusImg" alt="状态" mode="scaleToFill"></image>
          </view>
           <!-- 头像电话-->
          <view class="user_detail_head_mobile">{{ userData.head.mobile }}</view>
        </view>
      </view>
      <!-- 状态-->
      <view class="user_detail_status_right">
          <view :class="['card_status_item',userData.base.personStatus=='ENABLE'?'card_status_green':'',userData.base.personStatus=='PERSON_QUIT'?'card_status_gray':'',userData.base.personStatus=='FREEZE'?'card_status_orange':'',userData.base.personStatus=='DELETE'?'card_status_red':'']">
            {{userData.head.personStatusAlias }}
          </view >
          <view :class="['card_status_item',userData.base.cardStatus=='ENABLE'?'card_status_green':'',userData.base.cardStatus=='UNUSED'?'card_status_gray':'',userData.base.cardStatus=='LOSS'?'card_status_orange':'',userData.base.cardStatus=='QUIT'||userData.base.cardStatus=='DELETE'?'card_status_red':'']">
          {{userData.head.status}}
          </view>
    </view>
    </view>
    <!-- 基本信息-->
    <view class="user_detail_base">
      <horizontalCellComponent :titleTxt="userData.base.name" :valueTxt="userData.head.name" arrow-direction="right" rightIcon="arrow-right"  @handlerCellClick="handlerCellClick"/>
      <horizontalCellComponent :titleTxt="userData.base.sex" :valueTxt="userData.base.sexTxt" arrow-direction="right" rightIcon="arrow-right"  @handlerCellClick="handlerCellClick"/>
      <!-- 繁胜说正式环境没有上的字段，这个出生日期先隐藏，已知会华杰-->
      <horizontalCellComponent v-if="false" :titleTxt="userData.base.birthday" :valueTxt="userData.base.birthdayTxt" arrow-direction="right" rightIcon="arrow-right"  @handlerCellClick="handlerCellClick"/>
      <horizontalCellComponent :titleTxt="userData.base.mobile" :valueTxt="userData.head.mobile" arrow-direction="right" rightIcon="arrow-right"  @handlerCellClick="handlerCellClick"/>
    </view>

    <!-- 部门信息-->
    <view class="user_detail_department">
      <horizontalCellComponent :titleTxt="userData.department.userId" :valueTxt="userData.department.userIdTxt" isLink isShowBottomLine />
      <horizontalCellComponent :titleTxt="userData.department.userCardNo" :valueTxt="userData.department.userCardNoTxt" isLink isShowBottomLine/>
      <horizontalCellComponent :titleTxt="userData.department.facePhoto" :valueTxt="userData.department.facePhotoTxt" isLink isShowBottomLine/>
      <horizontalCellComponent :titleTxt="userData.department.department" :valueTxt="userData.department.departmentTxt" isLink isShowBottomLine/>
      <horizontalCellComponent :titleTxt="userData.department.ground" :valueTxt="userData.department.groundTxt" isLink />
    </view>

    <!-- 底部按钮-->
    <view class="user_detail_btns">
      <view class="user_detail_btns_content">
        <!--冻结卡-->
        <u-button v-if="userData.base.personStatus == 'ENABLE'"  class="user_detail_btn_style" :customStyle="customStyleBtn" plain :color="color.themeColor" @click="freezeAccount(userData.head.id)">{{ freezeAccounTxt }}</u-button>
        <!--挂失卡-->
        <u-button v-if="userData.base.cardStatus == 'ENABLE'&& userData.base.personStatus =='ENABLE'"  class="user_detail_btn_style" :customStyle="customStyleBtn" vplain :color="color.themeColor" @click="lossReporting(userData.head.id)">{{ lossReportingTxt }}</u-button>
        <!--取消挂失-->
        <u-button v-if="userData.base.cardStatus  == 'LOSS'&&userData.base.cardStatus == 'LOSS'&&userData.base.personStatus =='ENABLE'"   :customStyle="customStyleBtn" class="user_detail_btn_style" :color="color.colorBtnBlack" @click="cancelLoss(userData.head.id)">{{ cancelLossTxt }}</u-button>
        <!--退卡-->
        <u-button v-if="userData.base.cardStatus =='ENABLE'&&userData.base.personStatus =='ENABLE'"  :customStyle="customStyleBtn" class="user_detail_btn_style" :color="color.colorBtnRedWarning" @click="cardWithdrawal(userData.head.id)">{{ cardWithdrawalTxt }}</u-button>
        <!--上传face-->
        <u-button v-if="userData.base.personStatus=='ENABLE'"  :customStyle="customStyleBtn" class="user_detail_btn_style"  :color="color.themeColor" @click="uploadFace(userData.head.id,userData.head.companyId,userData.head.personNo)">{{uploadFaceTxt }}</u-button>
      </view>

    </view>
    <!-- 底部弹出框-->
    <u-picker :show="isShowSexPicker" :columns="sexList" @confirm="sexChooseConfirm" keyName="name" @cancel="sexChooseCancel"></u-picker>
    <u-datetime-picker :show="isShowDatePicker" v-model="dateValue" mode="date" @confirm="dateChooseConfirm" @cancel="dateChooseCancel"></u-datetime-picker>
    <!--#ifdef MP-WEIXIN || MP-ALIPAY -->
      <CustomDialogComponent ref="customDialog"></CustomDialogComponent>
    <!--#endif-->
  </view>
</template>

<script>
import horizontalCellComponent from "../../../src/components/HorizontalCellComponent/HorizontalCellComponent"
import { setCardWithdrawal, setCardLoss, setCardFreeze, setCardCancelLoss, setUploadFace, modifyUserInfo, modifyUserInfoDialog, getSexNameByCode, getSexImageByType } from '@/utils/userUtil'
import { mapGetters } from 'vuex'
import cache from '../../utils/cache'
import { deepClone, hidePhoneNum, checkClient } from "../../utils/util"
import comDic from "../../common/comDic"
import { timeFormat } from '../../utils/date'
export default {
  data() {
    return {
      imgPath: this.$imgPath,
      userData: {
        head: {
          status: "",
          name: "",
          mobile: "",
          statusImg: '',
          id: '',
          companyId: '',
          personNo: ''

        },
        base: {
          name: this.$t('page.user.name'),
          sex: this.$t('page.user.sex'),
          birthday: this.$t('page.user.birthday'),
          mobile: this.$t('page.user.mobile'),
          sexTxt: "",
          birthdayTxt: "",
          cardStatus: '',
          personStatus: ''

        },
        department: {
          userId: this.$t('page.user.userId'),
          userCardNo: this.$t('page.user.userCardNo'),
          facePhoto: this.$t('page.user.facePhoto'),
          department: this.$t('page.user.department'),
          ground: this.$t('page.user.ground'),
          userIdTxt: "",
          userCardNoTxt: "",
          facePhotoTxt: "",
          departmentTxt: "",
          groundTxt: ""

        }

      },
      freezeAccounTxt: this.$t('page.user.freeze.account'),
      lossReportingTxt: this.$t('page.user.card.loss.reporting'),
      uploadFaceTxt: this.$t('page.user.upload.face'),
      cardWithdrawalTxt: this.$t('page.user.card.withdrawal'), // 退卡 按钮名字
      cancelLossTxt: this.$t('page.user.card.cancel.loss'), // 取消挂失 按钮名字
      customStyleBtn: {
        width: '210rpx',
        height: '70rpx',
        borderRadius: '8rpx',
        fontSize: '28rpx',
        marginTop: '10rpx'
      },
      isShowSexPicker: false, // 是否显示底部选项
      isShowDatePicker: false, // 是否显示日期选项
      sexList: [comDic.DIC_USER_SEX], // 性别列表
      dateValue: Number(new Date()), // 日期
      params: {}, // 修改个人信息存参
      dialogTitle: this.$t('tip.prompt') // 弹窗title
    }
  },
  computed: {
    ...mapGetters(['color'])
  },
  components: { horizontalCellComponent },
  /**
   * 页面加载
   */
  onload(e) {
    console.log("个人页面加载", e)
    this.initData()
  },
  created() {
    this.initData()
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      // 获取缓存人员信息
      var userInfo = cache.get(this.$common.KEY_PERSON_USER_INFO) || {}
      // 赋值
      if (userInfo) {
        this.updateView(userInfo)
      }
      console.log("this.sexList", this.sexList);
      // 判断平台
      var platform = checkClient()
      this.uploadFaceTxt = platform === 'mp-weixin' ? this.$t("page.user.upload.pic") : this.$t("page.user.upload.face")
    },
    /**
     * 更新页面内容
     */
    updateView(info) {
      var userInfo = deepClone(this.userData)
      // 头像层信息
      userInfo.head.id = info.id || ''
      userInfo.head.companyId = info.company_id || ''
      userInfo.head.personNo = info.person_no || ''
      userInfo.head.name = info.name || '-'
      userInfo.head.statusImg = getSexImageByType(info.gender_alias)
      userInfo.head.mobile = hidePhoneNum(info.phone) || '-'
      userInfo.head.status = info.card_status_alias || '-'
      userInfo.head.imgUrl = info.face_url
      userInfo.head.personStatusAlias = info.account_status_alias
      userInfo.head.effectiveTime = info.effective_time
      userInfo.head.expirationTime = info.expiration_time
      // 基础信息
      userInfo.base.sexTxt = info.gender_alias || '-'
      userInfo.base.birthdayTxt = '-' // 都没有这个字段啊
      userInfo.base.cardStatus = info.card_status || ''
      userInfo.base.personStatus = info.person_status || ''
      // 部门信息
      userInfo.department.userIdTxt = info.person_no || '-'
      userInfo.department.userCardNoTxt = info.card_no || ''
      userInfo.department.facePhotoTxt = info.face_url ? "已上传" : "未上传"
      userInfo.department.departmentTxt = info.card_department_group_alias || '-'
      userInfo.department.groundTxt = info.card_user_group_alias.join(',') || '-'

      // 更新
      this.$set(this, 'userData', userInfo)
    },
    /**
     * 退卡
     */
    async cardWithdrawal(id) {
      console.log("退卡");
      var [error, res] = await this.$to(setCardWithdrawal(id, this.dialogTitle, this.$t('tip.prompt.card.withdrawal'), this))
      // 退卡成功刷新列表
      if (res && res === '退卡成功' && !error) {
        console.log("退卡返回", res);
        this.userData.base.cardStatus = 'QUIT'
        this.goBack()
      }
    },
    /**
     * 挂失卡
     */
    async lossReporting(id) {
      console.log("挂失卡");
      var [error, res] = await this.$to(setCardLoss(id, this.dialogTitle, this.$t('tip.prompt.card.loss.reporting'), this))
      if (res && res === '挂失成功' && !error) {
        this.userData.base.cardStatus = 'LOSS'
        // 告诉前面的页面要更新状态
        this.goBack()
      }
    },
    /**
     * 取消挂失
     */
    async cancelLoss(id) {
      console.log("取消挂失");
      var [error, res] = await this.$to(setCardCancelLoss(id, this.dialogTitle, this.$t('tip.prompt.card.cancel.loss'), this))
      if (res && res === '取消挂失成功' && !error) {
        this.userData.base.cardStatus = 'ENABLE'
        // 告诉前面的页面要更新状态
        this.goBack()
      }
    },
    /**
     * 冻结账户
     */
    async freezeAccount(id) {
      console.log("冻结账户");
      var [error, res] = await this.$to(setCardFreeze(id, this.dialogTitle, this.$t('tip.prompt.card.freeze.account'), this))
      if (res && res === '冻结成功' && !error) {
        this.userData.base.personStatus = 'FREEZE'
        this.userData.base.cardStatus = '冻结中'
        // 告诉前面的页面要更新状态
        this.goBack()
      }
    },
    /**
     * 上传face
     */
    async uploadFace(id, companyId, personNo) {
      var [error, res] = await this.$to(setUploadFace(id, companyId, personNo, this))
      if (res && res === this.uploadFaceTxt + '成功' && !error) {
        // 更新操作
        this.userData.department.facePhotoTxt = '已上传'
      }
    },
    /**
     * 每个项目点击
     */
    handlerCellClick(e) {
      console.log("handlerCellClick", e);
      // if (!this.$hasPermissionKey('edit_card_user')) {
      //   uni.$u.toast('没有权限编辑')
      //   return
      // }
      var tiltle = ''
      this.params = {
        card_user_id: this.userData.head.id,
        person_no: this.userData.head.personNo,
        person_name: this.userData.head.name

      }
      if (this.userData.head.effectiveTime) {
        this.params.effective_time = this.userData.head.effectiveTime
      }
      if (this.userData.head.expirationTime) {
        this.params.expiration_time = this.userData.head.expirationTime
      }
      switch (e) {
        case "姓名":
          tiltle = "名字修改"
          this.modifyInfo(this.params, tiltle, 1)
          break;
        case "性别":
          this.isShowSexPicker = !this.isShowSexPicker
          break;
        case "手机号码":
          tiltle = "手机号码修改"
          this.modifyInfo(this.params, tiltle, 1)
          break;
        case "出生日期":
          this.isShowDatePicker = !this.isShowDatePicker
          break;
        default:
          break;
      }
    },
    /**
     * 更新用户信息
     * @param userInfo 入参包含人员基本信息
     * @param title  标题
     * @param type  类型， 1 是弹窗， 2是直接调用修改
     */
    async modifyInfo(userInfo, title, type) {
      console.log("modifyInfo", userInfo);
      var [error, res] = []
      if (type === 1) {
        [error, res] = await this.$to(modifyUserInfoDialog(userInfo, title, this))
      } else {
        [error, res] = await this.$to(modifyUserInfo(userInfo))
      }

      if (res && res === '修改成功' && !error) {
        // 更新操作
        this.userData.head.name = userInfo.person_name ? userInfo.person_name : this.userData.head.name
        this.userData.head.mobile = userInfo.phone ? hidePhoneNum(userInfo.phone) : this.userData.head.mobile
        this.userData.base.sexTxt = userInfo.gender ? getSexNameByCode(userInfo.gender) : this.userData.base.sexTxt
        this.userData.base.birthdayTxt = userInfo.birthday ? userInfo.birthday : this.userData.base.birthdayTxt
        this.userData.head.statusImg = getSexImageByType(this.userData.base.sexTxt)
        uni.$emit(this.$common.MSG_UPDATE_PERSON_INFO, "更新成功")
      }
      console.log("this.userData.head", this.userData.head);
    },
    /**
     * 性别选择
     * @param {*} e
     */
    sexChooseConfirm(e) {
      console.log("sexChooseConfirm", e);
      this.isShowSexPicker = false
      this.params.gender = e.value ? e.value[0].value : ''
      this.modifyInfo(this.params, '性别选择', 2)
    },
    /**
     * 日期选择
     * @param {*} e
     */
    dateChooseConfirm(e) {
      console.log("dateChooseConfirm", e);
      this.isShowDatePicker = false
      var dateValue = timeFormat(e.value)
      console.log("dateValue", dateValue);
      this.params.birthday = dateValue
      this.modifyInfo(this.params, '日期选择', 2)
    },
    /**
     * 性别选择取消
     */
    sexChooseCancel() {
      this.isShowSexPicker = false
    },
    /**
     * 日期选择取消
     */
    dateChooseCancel() {
      this.isShowDatePicker = false
    },
    /**
     * 返回上一页
     */
    goBack() {
      this.$u.toast("更新成功，三秒后自动返回列表页")
      setTimeout(() => {
        this.$miRouter.back()
      }, 3000)
    }

  }

}
</script>

<style lang="scss">
@mixin baseWidth {
  width: 670rpx;
  background-color: #ffffff;
  border-radius: 12rpx;

}
.user_detail_main{
  background: #F0F3F5;
  padding: 20 40rpx 0 200rpx;
  margin-top: 20rpx;

  .user_detail_head{
    @include baseWidth();
    height: 180rpx;
    display: flex;
    justify-items: center;
    justify-content: space-between;
    margin:0 auto;
    .user_detail_head_left{
      display: flex;
      align-items: center;
      .user_detail_head_left_img{
        width: 100rpx;
        height: 100rpx;
        margin: 40rpx 30rpx;
      }
      .user_detail_head_left_content{
        .user_detail_head_left_top{
          display: flex;
          align-items: center;
          .user_detail_head_left_name{
            display: inline;
            font-size: 36rpx;
            line-height: 42rpx;
            color: #1d1e20;
            max-width: 220rpx;
          }
          .user_detail_head_left_icon{
            width: 34rpx;
            height: 34rpx;
            margin: 8rpx ;
          }
        }
      .user_detail_head_mobile{
        display: block;
        font-size: 24rpx;
        color: #8f9295;
        margin-top: 13rpx;
      }
      }
    }
    .user_detail_status_right{
      display: flex;
      margin-right: 40rpx;
      justify-content: center;
      align-items: center;
      overflow: hidden;
    }
    .card_item{
      display: inline-flex;
      align-items: center;
    }
    .card_txt{
      width: 90rpx;
      font-size: 16rpx;
      text-align: center;
      margin-top: 10rpx;
      height: 40rpx;
      line-height: 40rpx;
      color: #8f9295;
    }
    .card_status_item{
      width: 112rpx;
      height: 40rpx;
      text-align: center;
      line-height: 42rpx;
      font-size: 24rpx;
      border-radius: 20rpx;
      margin-left:10rpx;
    }

    .card_status_green{
      color: #51d854;
      background-color: #EDFBEE;
    }
    .card_status_gray{
      color: #989898;
      background-color: #f5f5f5;
    }
    .card_status_orange{
      color: #fd7c3c;
      background-color: #FFF2EB;
    }
    .card_status_red{
      color: #ff6349;
      background-color: #FFEFEC;
    }
  }
  .user_detail_base{
    @include baseWidth();
    max-height: 363rpx;
    margin: 20rpx auto;
  }
  .user_detail_department{
    @include baseWidth();
    height: 454rpx;
    margin:0 auto;
 }
}
.user_detail_btns{
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  min-height: 108rpx;
  background-color: #ffffff;
  .user_detail_btns_content{
    width:670rpx ;
    min-height: 70rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
    flex-wrap: wrap;
    margin:  20rpx auto;
    .user_detail_btn_style{
      width: 210rpx;
      min-height: 70rpx;
      border-radius: 8rpx;
      font-size: 28rpx;
      margin-top:10rpx;
    }
  }

}

</style>

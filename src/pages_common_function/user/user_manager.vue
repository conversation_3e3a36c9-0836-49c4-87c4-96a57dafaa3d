<template>
   <view>
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('page.user.userManager')"
      placeholder
      :autoBack="true"
      :leftIconColor="color.navigation"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    ></u-navbar>
    <!--#endif-->
    <!-- 搜索栏-->
    <view>
      <searchComponent :searchPlaceholder="searchPlaceholder" @handlerSearchLayoutClick="handlerSearchLayoutClick" inputReadonly type="user_manager"></searchComponent>
    </view>
    <!-- 列表页 -->
    <view class="user_list_main_container"  v-if="!isShowEmptyView">
      <view class="user_list_main">
        <view v-for="(item,index) in userinfo" :key="index" class="user_list_item" @click="handlerItemClick(item,item.index)">
          <view class="user_list_item_txt">{{ item.name }}</view>
          <u-icon name="arrow-right" color="#8f9295" size="18"></u-icon>
        </view>
      </view>
    </view>
    <emptyComponent :emptyContent="emptyContent" v-if="isShowEmptyView"></emptyComponent>

  </view>
</template>

<script>
import searchComponent from "@/components/SearchComponent/SearchComponent"
import emptyComponent from "@/components/EmptyComponent/EmptyComponent"
import { mapGetters } from 'vuex'
import cache from '@/utils/cache'

export default {

  data() {
    return {
      searchPlaceholder: this.$t('page.user.searchHolder'),
      emptyContent: this.$t('tip.no.search.content'),
      isShowEmptyView: false, // 是否显示空页面
      userinfo: [
        {
          name: this.$t('title.user.department'),
          index: 1
        },
        {
          name: this.$t('page.user.manager.user.grouping'),
          index: 2
        }
      ],
      pageNo: 1,
      pageSize: 10
    }
  },
  components: { searchComponent, emptyComponent },
  computed: {
    ...mapGetters(['color'])
  },
  /**
   * 页面加载
   */
  onload(e) {
    console.log("用户列表页面加载", e)
  },
  created() {
  },
  methods: {

    /**
     * 筛选层点击监听
     */
    handlerSearchLayoutClick() {
      // 清空页面标题缓存
      cache.set(this.$common.TITLE_DEPARTMENT_NAME, '')
      // 搜索层点击跳转到用户列表页面
      this.$miRouter.push({
        path: '/pages_common_function/user/department_manager',
        query: {
          type: "search",
          index: -1
        }
      })
    },
    /**
     * 项目点击
     * @param {} item
     * @param {*} index
     */
    handlerItemClick(item, index) {
      console.log("handlerItemClick", item, index);
      this.$miRouter.push({
        path: '/pages_common_function/user/user_department',
        query: {
          id: index
        }
      })
    }
  }

}

</script>

<style lang="scss">
  page {
    padding: 0;
    background: #F0F3F5;
  }
  .user_list_main_container{
    margin-top: 50rpx;
  }
  .user_list_main{
     display: block;
     .user_list_item{
      width: 670rpx;
      height: 120rpx;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      justify-items: center;
      background-color: #ffffff;
      border-radius: 12px;
      margin:20rpx auto;
      padding:0 30rpx;
      .user_list_item_txt{
        flex:19;
        font-size: 32rpx;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 0px;
        color: #1d1e20;
        line-height: 120rpx;
        text-align: left;
      }
      .user_list_item_arraw{
        width: 10rpx;
      }

     }

  }

</style>

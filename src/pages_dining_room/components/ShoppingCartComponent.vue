<template>
  <!-- 上拉购物车列表-->
  <view  class="shopping_cart_container padding-bottom-h5-shooping padding-bottom-wechat" :style="{ 'height': vheight }" v-if="isShowShoppingCart" catchtouchmove="true"  @touchmove.stop.prevent="moveHandle">
    <!-- 遮罩层-->
    <view class="shopping_cart_mask" :class="{ 'show': isShowMask, 'hide': isShowMask != true }"
      @tap="showShoppingCart(false)">
    </view>
    <!-- 数据内容-->
    <!-- 数据头部-->
    <view class="shopping_cart_content_top">
      <view class="shopping_cart_content_top_title"> {{ topLeftTxt }}</view>
      <view class="shopping_cart_content_top_right">
        <u-icon name="trash" width="24rpx" height="24rpx" class="shopping_cart_content_top_right_icon"></u-icon>
        <view class="shopping_cart_content_top_right_txt" @click="clearShoppingCard">{{ topRightTxt }}</view>
      </view>
    </view>
    <view class="horizontal_cell_line"></view>
    <!-- 数据列表-->
    <scroll-view scroll-y class="scroll-style" :style="[scrollStyle]" :show-scrollbar="dataList.length>3" enhanced>
      <view class="shopping_cart_middle_list" v-for="(item, index) in dataList" :key="index"
        @click="handlerShoppingCartClick(item)">
        <view class="shopping_cart_list_item">
          <image class="shopping_cart_list_img" :src="item.image ? item.image : imgPath.IMG_DEFAULT " alt="菜品"></image>
          <view class="shopping_cart_list_middle">
            <view class="shopping_cart_list_middle_title">{{ item.food_name }}</view>
            <view class="shopping_cart_list_middle_number">库存<span style="margin-left: 20rpx;"></span> {{ item.number == -1 ? '不限量': item.number}}
            </view>
            <!-- <view class="shopping_cart_list_middle_price">¥{{ item.price }}</view> -->
          </view>
          <view class="shopping_cart_middle_btn_layout">
            <u-button class="shopping_cart_middle_btn" :customStyle="customStyleBtnListItem" :color="color.colorBtnOrange"
              @click="modifyShoppingCardItem(item)">{{ modifyShoppingCardTxt }}</u-button>
            <u-button class="shopping_cart_middle_btn" :customStyle="customStyleBtnListItem" :color="color.colorBtnOrange"
              @click="removeShoppingCardItem(item, index)" plain>{{ removeShoppingCardTxt }}</u-button>
          </view>
        </view>
        <view class="horizontal_cell_line"></view>
      </view>
    </scroll-view>
    <!-- 空数据-->
    <view class="filter_empty_view" v-if="dataList.length == 0">{{ emptyTxt }}</view>

  </view>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'shoppingCart',
  data() {
    return {
      imgPath: this.$imgPath,
      isShowShoppingCart: false, // 是否显示下拉菜单
      isShowMask: true, // 是否显示遮罩层
      isShowBottomLine: false, // 是否显示下面的分割线
      vheight: "", // 控件高度
      bottomHeight: this.topHeightDistance,
      chooseItem: "", // 选中的选项
      emptyTxt: this.$t('page.search.no.options.available'), // 暂无数据,
      modifyShoppingCardTxt: this.$t('announcement.revise'), // 按钮修改文字,
      removeShoppingCardTxt: this.$t('announcement.Delete'), // 按钮删除文字,
      customStyleBtnListItem: {
        width: '90rpx',
        height: '44rpx',
        borderRadius: '8rpx',
        fontSize: '24rpx',
        padding: '0 !important',
        marginLeft: '10rpx'
      },
      scrollStyle: {
        maxHeight: '560rpx'
      },
      dataList: this.shoppingCarList // 数据
    }
  },
  props: {
    shoppingCarList: {
      type: Array,
      default: () => {
        return []
      }
    },
    topLeftTxt: { // 头部左侧内容
      type: String,
      default: '2022-02-24  早餐'
    },
    topRightTxt: { // 头部右侧内容
      type: String,
      default: '清空'
    },
    topHeightDistance: { // 距离底部的高度
      type: Number,
      default: 0
    },
    chooseTarget: { // 选择的项目
      type: String,
      default: ''
    }

  },
  computed: {
    ...mapGetters(['color'])
  },
  watch: {
    chooseTarget(newVal) {
      console.log("watch chooseTarget", newVal);
      this.setChooseItem(newVal)
    },
    dataList: {
      handler(newvalue) {
        console.log("newlue", newvalue)
      },
      deep: true

    }

  },
  created() {
    // 计算屏幕剩余高度  填补剩余高度
    let that = this;
    uni.getSystemInfo({
      success(res) {
        that.vheight = (res.windowHeight - uni.upx2px(80) - that.bottomHeight) + "px";
        console.log("vheight", res.windowHeight, that.vheight, that.bottomHeight);
      }
    });
  },
  methods: {
    /**
     * 显示与隐藏上拉列表
     * @param {*} isShow
     */
    showShoppingCart(isShow) {
      this.isShowShoppingCart = isShow
      console.log("showShoppingCart", isShow);
      this.$emit("showShoppingCartChange", this.isShowShoppingCart)
    },
    /**
     * 获取当前显示隐藏的状态
     *
     */
    getShoppingCartStatus() {
      return this.isShowShoppingCart
    },
    /**
     * 列表项目点击
     */
    handlerShoppingCartClick(data) {
      this.chooseItem = data.name ? data.name : data
      this.$emit("handlerShoppingCartClick", data)
    },
    /**
     * 设置选中项目
     * @param {*} name
     */
    setChooseItem(name) {
      this.chooseItem = name
    },
    /**
     * 设置控件距离上面顶部的距离
     */
    setTopHeight(distance) {
      this.topHeight = distance
    },
    /**
     * 修改购物车列表项
     * @param {item包含每项的数据} item
     */
    modifyShoppingCardItem(item) {
      console.log("item", item);
      this.$emit('modifyShoppingCardInventory', item)
    },
    /**
     *删除购物车列表项
     * @param {item包含每项的数据} item
     */
    removeShoppingCardItem(item, index) {
      console.log("item", item);
      this.$emit('removeShoppingCardInventory', item, index)
    },
    // 清空购物车标
    clearShoppingCard() {
      this.$emit('clearShoppingCard', true)
    },
    // 设置购物车数据
    setShoppingCardList(list) {
      this.dataList = list
    },
    moveHandle() {

    }

  }

}

</script>

<style lang="scss" scoped>
// 去掉列表滚动条
::-webkit-scrollbar {
  display: none;
}
/* #ifdef H5 */
.padding-bottom-h5-shooping {
  bottom: 132rpx;
}
/* #endif */
/* #ifdef MP-WEIXIN || MP-ALIPAY */
.padding-bottom-wechat {
  bottom: 162rpx;
}
/* #endif */

.shopping_cart_container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: fixed;
  left: 0;
  background: #ffffff;
  .shopping_cart_content_top {
    width: 100%;
    height: 100rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 40rpx;

    .shopping_cart_content_top_title {
      font-size: 36rpx;
      color: #1d201e;
    }

    .shopping_cart_content_top_right {
      display: inline-flex;
      align-items: center;

      .shopping_cart_content_top_right_icon {
        width: 23rpx;
        height: 25rpx;
        align-self: center;
        margin-right: 10rpx;
      }

      .shopping_cart_content_top_right_txt {
        font-size: 28rpx;
        color: #93989e;
      }

    }

  }

  .shopping_cart_middle_list {
    box-sizing: border-box;

    .shopping_cart_list_item {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin: 36rpx 20rpx;

      .shopping_cart_list_img {
        width: 121rpx;
        height: 120rpx;
        border-radius: 10rpx;

        &image {
          width: 121rpx;
          height: 120rpx;
        }
      }

      .shopping_cart_list_middle {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        flex: 1;
        padding: 0 20rpx;

        .shopping_cart_list_middle_title {
          font-size: 30rpx;
          color: #1d201e;

        }

        .shopping_cart_list_middle_number {
          font-size: 20rpx;
          color: #8f9295;
          margin-top: 14rpx;

        }

        .shopping_cart_list_middle_price {
          font-size: 32rpx;
          color: #ff5757;
          margin-top: 14rpx;
        }
      }

      .shopping_cart_middle_btn_layout {
        display: inline-flex;
        align-self: flex-end;
        justify-content: flex-start;

        .shopping_cart_middle_btn {
          width: 90rpx;
          height: 44rpx;
          border-radius: 8rpx;
          font-size: 24rpx;
          padding: 0 !important;
          margin-left: 10rpx;
        }
      }

    }

    .horizontal_cell_line {
      width: 678rpx;
      height: 1px;
      background-color: #efefef;
      margin: 0 auto;
    }

  }

  .shopping_cart_content {
    width: 100%;
    padding: 0 40rpx;
    background-color: #ffffff;
    box-shadow: 0 5px 5px rgba(0, 0, 0, .1);
    transition: transform .15s linear;
    z-index: 9999;
    border-radius: 10rpx 10rpx 0 0;

    .shopping_cart_item {
      width: 675rpx;
      height: 80rpx;
      line-height: 80rpx;
      flex-direction: row;
      font-size: 24rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #ffffff;

      &.hide {
        display: none;
      }

      &.show {
        transform: translate3d(0, calc(44px + 1rpx), 0);
      }

      .shopping_cart_item_title {
        font-size: 32rpx;
        color: #1d1e20;
        line-height: 60rpx;

        &.active {
          color: #fd953c;
        }

        &.no_active {
          color: #1d1e20;
        }

      }
    }

    .horizontal_cell_line {
      width: 675rpx;
      height: 1px;
      background-color: #eae9ed;
      margin: 0 auto;
    }
  }

  .filter_empty_view {
    width: 100%;
    height: 100rpx;
    line-height: 100rpx;
    color: #c2c5c8;
    font-size: 24rpx;
    text-align: center;
    background: #ffffff;
    z-index: 1000;
  }

  .shopping_cart_mask {
    width: 100%;
    flex: 1 1 auto;
    flex-basis: 0;
    background-color: rgba(0, 0, 0, 0);
    transition: background-color .15s linear;
    overflow: hidden;
    z-index: 9000;

    &.show {
      background-color: rgba(0, 0, 0, 0.5);
    }

    &.hide {
      display: none;
    }
  }

}
</style>

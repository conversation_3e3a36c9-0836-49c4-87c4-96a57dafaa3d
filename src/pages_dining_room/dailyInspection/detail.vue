<template>
  <div class="daily_inspection_container">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="isCheck ? '查看记录' : '添加巡查记录'"
      placeholder
      :autoBack="true"
      :leftIconColor="color.navigation"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    ></u-navbar>
    <!--#endif-->

    <!--内容列表 -->
    <view class="daily_inspection_detail_content">
      <!-- 兼容小程序 u-cell外包一层view -->
      <view class="title">
        <u-cell
          :disabled="isCheck"
          isLink
          :arrow-direction="showPopup ? 'down' : 'right'"
          @click="showPopup = true"
        >
          <view slot="title" style="width: 120rpx">
            <span class="project_name">巡查项目</span>
          </view>
          <view slot="value" style="text-align: left; margin-left: 20rpx">
            {{ chooseItemName }}
          </view>
        </u-cell>
      </view>
      <view class="result">
        <view style="margin-bottom: 20rpx">巡查结果</view>
        <u--textarea
          :disabled="isCheck"
          v-model="inspectionResult"
          placeholder="请输入内容"
          count
          maxlength="20"
        ></u--textarea>
      </view>
      <view class="result">
        <view style="margin-bottom: 20rpx">巡查凭证</view>
        <view v-if="isCheck">
          <template v-for="(img, i) in fileList1">
            <img
              :key="i"
              :src="img.url"
              alt=""
              style="width: 80rpx; height: 80rpx; margin-right: 20rpx"
              @click="onInspectionDetail(i)"
            />
          </template>
        </view>
        <view v-else>
          <!-- 拍照上传 -->
          <u-upload
            :disabled="isCheck"
            name="1"
            :fileList="fileList1"
            @afterRead="afterRead"
            @delete="deletePic"
            multiple
            :maxCount="3"
          ></u-upload>
        </view>
      </view>
    </view>

    <view v-if="!isCheck" class="btn_save_box">
      <u-button :color="color.themeColor" @click="onAddInspectionRecord">保存</u-button>
    </view>

    <u-action-sheet
      title="巡查项目"
      :show="showPopup"
      @select="selectClick"
      @close="showPopup = false"
    >
      <scroll-view scroll-y style="min-height: 100px; max-height: 300px">
        <view
          v-for="(item, index) in projectList"
          :key="index"
          class="list-item"
          @click.stop="onSelectItem(item)"
        >
          <view class="filter_drop_down_item">
            <view
              class="filter_drop_down_item_title"
              :class="{
                active: chooseItemName == item.name,
                no_active: chooseItemName != item.name
              }"
            >
              {{ item.name }}
            </view>
            <u-icon
              v-if="chooseItemName == item.name"
              name="checkbox-mark"
              :color="color.colorBtnOrange"
              size="28"
            ></u-icon>
          </view>
        </view>
      </scroll-view>
    </u-action-sheet>

    <!-- 预览图 -->
    <image-preview ref="imgPreview"></image-preview>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { uploadFilePromise } from '@/utils/userUtil'
import {
  apiBackgroundFundSupervisionDailyPatrolGetProjectList,
  apiMerchantMobileDailyPatrolAdd
} from '@/api/dinging'
import { decodeQuery } from '@/utils/util'
import ImagePreview from '@/components/ImagePreview/ImagePreview.vue'
export default {
  components: { ImagePreview },
  data() {
    return {
      isCheck: false,
      chooseItemName: '',
      fileList1: [],
      showPopup: false,
      projectList: [],
      inspectionResult: '',
      emptyContent: this.$t('tip.list.empty')
    }
  },
  computed: {
    ...mapGetters(['color'])
  },
  onLoad(options) {
    this.getProjectList()
    const obj = decodeQuery(options.query)
    if (obj) {
      this.inspectionResult = obj.result || ''
      this.fileList1 = obj.image_json || []
      this.chooseItemName = obj.project || ''
      this.isCheck = true
    }
  },
  methods: {
    // 查看结果
    onInspectionDetail(i) {
      if (this.$refs.imgPreview) {
        const urlArr = this.fileList1.map(item => item.url)
        this.$refs.imgPreview.setImgList(urlArr, i)
        this.$refs.imgPreview.showDialog()
      }
    },
    onSelectItem(item) {
      this.chooseItemName = item.name
      this.showPopup = false // 选择后关闭弹窗
    },
    closeActiveSheet() {
      this.showPopup = false
    },
    // 新增图片
    async afterRead(event) {
      // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
      let lists = [].concat(event.file)
      let fileListLen = this[`fileList${event.name}`].length
      lists.map(item => {
        return this[`fileList${event.name}`].push({
          ...item
        })
      })
      for (let i = 0; i < lists.length; i++) {
        let [error, resultUrl] = await this.$to(uploadFilePromise(lists[i].url))
        if (error) {
          uni.$u.toast(error.message || 'error')
          return
        }
        let item = this[`fileList${event.name}`][fileListLen]
        this[`fileList${event.name}`].splice(
          fileListLen,
          1,
          Object.assign(item, {
            status: 'success',
            message: '',
            url: resultUrl
          })
        )
        fileListLen++
      }
    },
    // 删除图片
    deletePic(event) {
      this[`fileList${event.name}`].splice(event.index, 1)
    },
    // 获取巡查项目列表
    async getProjectList() {
      this.projectList = []
      const [error, res] = await this.$to(apiBackgroundFundSupervisionDailyPatrolGetProjectList())
      if (error) {
        uni.$u.toast(error.message)
      }
      if (res && res.code === 0) {
        var resultList = res.data || []
        this.projectList.push(
          ...resultList.map(item => ({
            name: item,
            value: item
          }))
        )
      }
    },
    // 添加巡查记录
    async onAddInspectionRecord() {
      if (!this.chooseItemName) {
        this.$u.toast('巡查项目不能为空')
        return
      }

      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      const params = {
        result: this.inspectionResult,
        project: this.chooseItemName,
        image_json: this.fileList1
      }

      if (!params.project) {
        delete params.project
      }

      const paramsP = {
        patrol_record: [params]
      }

      const [error, res] = await this.$to(apiMerchantMobileDailyPatrolAdd(paramsP))
      uni.hideLoading()
      if (error) {
        uni.$u.toast(error.message || 'error')
        return
      }
      if (res && res.code === 0) {
        this.$u.toast('保存成功')
        setTimeout(() => {
          uni.$emit('refreshList')
          uni.navigateBack({ delta: 1 })
        }, 500)
      } else {
        uni.$u.toast(res.msg || '保存失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .u-checkbox-group--column {
  align-items: center !important;
}

::v-deep .u-upload__wrap__preview__image {
  width: 120rpx !important;
  height: 100rpx !important;
}
::v-deep .u-upload__button {
  width: 120rpx !important;
  height: 100rpx !important;
  .u-icon__icon {
    font-size: 48rpx !important;
  }
}

.popup_content {
  min-height: 200rpx;
  background: #fff;
  padding: 40rpx;
}
.daily_inspection_detail_content {
  padding: 20rpx;
  height: calc(100vh - 168rpx);
  .title {
    background: #fff;
    margin-bottom: 20rpx;
    border-radius: 10rpx;
    font-size: 24rpx;
    position: relative;
    .project_name::before {
      position: absolute;
      left: 10rpx;
      content: '*';
      color: red;
    }
  }
  .result {
    background: #fff;
    margin-bottom: 20rpx;
    border-radius: 10rpx;
    font-size: 30rpx;
    padding: 30rpx;
  }
}

.list-item {
  padding: 0 40rpx;
  text-align: center;
  border-bottom: 1px solid #eee;
}

.filter_drop_down_item {
  width: 675rpx;
  height: 80rpx;
  line-height: 80rpx;
  flex-direction: row;
  font-size: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  .filter_drop_down_item_title {
    line-height: 60rpx;
    font-size: 32rpx;
    &.active {
      color: #fd953c;
    }
    &.no_active {
      color: #1d1e20;
    }
  }
}

.btn_save_box {
  position: absolute;
  bottom: 20rpx;
  width: 80%;
  left: 10%;
}
</style>

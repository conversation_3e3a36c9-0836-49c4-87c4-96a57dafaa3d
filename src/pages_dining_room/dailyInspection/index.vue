<template>
  <div class="daily_inspection_container">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('title.daily.inspection')"
      placeholder
      :autoBack="true"
      :leftIconColor="color.navigation"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    ></u-navbar>
    <!--#endif-->
    <!--头部筛选 -->
    <filterLayoutComponent
      :filterDataLayoutList="menuDataList"
      @handlerItemClick="handlerMenuItemClick"
    ></filterLayoutComponent>

    <!--内容列表 -->
    <view class="daily_inspection_content" v-if="!isShowEmptyView">
      <mescroll-uni
        ref="mescrollRef"
        :fixed="false"
        :safearea="true"
        :bottom="0"
        @init="mescrollInit"
        @down="downCallback"
        @up="upCallback"
        :down="{ auto: false }"
        :up="{ auto: false }"
      >
        <view class="inspection_content_item" v-for="(item, index) in inspectionList" :key="index">
          <view class="left_content">
            <view class="left_box">
              <span class="left_box_title">巡查项目</span>
              <span class="left_box_content">{{ item.project || '-' }}</span>
            </view>
            <view class="left_box">
              <span class="left_box_title">巡查人</span>
              <span class="left_box_content">{{ item.name }}（{{ item.account }}）</span>
            </view>
            <view class="left_box">
              <span class="left_box_title">巡查时间</span>
              <span class="left_box_content">{{ getCreateTime(item.create_time) }}</span>
            </view>
          </view>
          <view class="right_content">
            <view class="btn" @click="onCheckDetail(item)">
              {{ $t('page.recommend.view.result') }}
            </view>
          </view>
        </view>
      </mescroll-uni>
    </view>

    <view class="btn_add_box">
      <u-button :color="color.themeColor" @click="onAddInspectionRecord">添加巡查记录</u-button>
    </view>

    <!-- 空白页 -->
    <emptyComponent :emptyContent="emptyContent" v-if="isShowEmptyView"></emptyComponent>
  </div>
</template>

<script>
import filterLayoutComponent from '@/components/FilterLayoutComponent/FilterLayoutComponent.vue'
import emptyComponent from '@/components/EmptyComponent/EmptyComponent.vue'
import { mapGetters } from 'vuex'
import comDic from '../../common/comDic'
import { deepClone, getLastDayRange } from '@/utils/util'
import MescrollMixin from '@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js'
import {
  apiMerchantMobileDailyPatrolList,
  apiBackgroundFundSupervisionDailyPatrolGetProjectList
} from '@/api/dinging'
import { timeFormat } from '@/utils/date.js'

export default {
  mixins: [MescrollMixin],
  components: { filterLayoutComponent, emptyComponent },
  data() {
    return {
      isFirstLoad: true,
      pageNo: 1, // 页码
      pageSize: 10, // 每页显示数据
      parmas: {}, // 参数
      inspectionList: [],
      emptyContent: this.$t('tip.list.empty'),
      isShowEmptyView: false,
      menuDataList: [
        {
          title: '时间',
          chooseItem: '最近7天',
          dataList: comDic.DIC_COLLECTION_CODE_DATE_LIST
        },
        {
          title: '巡查项目',
          chooseItem: '全部',
          dataList: [
            {
              name: '全部',
              value: ''
            }
          ]
        }
      ]
    }
  },
  computed: {
    ...mapGetters(['color'])
  },
  onLoad() {
    this.initData()
    this.initList()
    uni.$on('refreshList', this.initList)
  },
  onUnload() {
    uni.$off('refreshList')
  },
  methods: {
    onAddInspectionRecord() {
      this.$miRouter.push({
        path: '/pages_dining_room/dailyInspection/detail'
      })
    },
    // 格式化时间
    getCreateTime(val) {
      return timeFormat(new Date(val).getTime(), 'yyyy-mm-dd hh:MM:ss')
    },
    onCheckDetail(item) {
      console.log(item, 'item')
      this.$miRouter.push({
        path: '/pages_dining_room/dailyInspection/detail',
        query: {
          result: item.result,
          image_json: item.image_json,
          project: item.project
        }
      })
    },
    // 初始化
    initData() {
      // 默认七天
      this.parmas.start_date = getLastDayRange(7, '{y}-{m}-{d}')[0]
      this.parmas.end_date = getLastDayRange(7, '{y}-{m}-{d}')[1]
    },
    initList() {
      this.parmas.page = 1
      this.parmas.page_size = this.pageSize

      this.getProjectList()
      this.getInspectionList()
    },
    // 获取巡查项目列表
    async getProjectList() {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      this.menuDataList[1].dataList = [
        {
          name: '全部',
          value: ''
        }
      ]
      const [error, res] = await this.$to(apiBackgroundFundSupervisionDailyPatrolGetProjectList())
      uni.hideLoading()
      if (error) {
        uni.$u.toast(error.message)
      }
      if (res && res.code === 0) {
        var resultList = res.data || []
        this.menuDataList[1].dataList.push(
          ...resultList.map(item => ({
            name: item,
            value: item
          }))
        )
      }
    },
    // 获取巡查记录列表
    async getInspectionList() {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })

      const [error, res] = await this.$to(apiMerchantMobileDailyPatrolList(this.parmas))
      uni.hideLoading()
      if (error) {
        this.mescroll.endErr()
        if (this.pageNo === 1) {
          this.isShowEmptyView = true
        }
        if (error.message) {
          uni.$u.toast(error.message)
        }
        return
      }
      if (res && res.code === 0) {
        var data = res.data || {}
        if (data) {
          var resultList = data.results || []
          // 没有数据
          this.isShowEmptyView = this.pageNo === 1 && (!resultList || resultList.length === 0)
          if (this.pageNo === 1 && resultList && resultList.length > 0) {
            // 首次加载数据
            this.inspectionList = deepClone(resultList)
          } else if (this.pageNo !== 1 && resultList && resultList.length > 0) {
            // 加载更多数据
            this.inspectionList = this.inspectionList.concat(resultList)
          } else {
            // 其他情况
            uni.hideLoading()
            uni.$u.toast(res.msg !== '成功' && res.msg !== 'OK' ? res.msg : '暂无数据')
          }
          this.mescroll.endErr()
        }
      } else {
        this.inspectionList = []
        if (res.msg) {
          uni.$u.toast(res.msg)
        }
        this.mescroll.endErr()
      }
    },
    /**
     * 下拉刷新返回
     */
    downCallback(page) {
      this.pageNo = 1
      this.parmas.page = this.pageNo
      this.getInspectionList()
    },
    /**
     * 上拉加载更多
     * @param {*} page
     */
    upCallback(page) {
      this.pageNo++
      this.parmas.page = this.pageNo
      this.getInspectionList()
    },
    // 顶部筛选
    handlerMenuItemClick(itemData, index) {
      this.menuDataList[index].chooseItem = itemData.name
      // 根据选择组参
      switch (index) {
        // 时间
        case 0:
          if (itemData.value === 'yesterday') {
            let list = getLastDayRange(1, '{y}-{m}-{d}')
            this.parmas.start_date = list[0]
            this.parmas.end_date = list[1]
          } else if (itemData.value === 'sevenday') {
            let list = getLastDayRange(6, '{y}-{m}-{d}')
            this.parmas.start_date = list[0]
            this.parmas.end_date = list[1]
          } else if (itemData.value === 'month') {
            let list = getLastDayRange(30, '{y}-{m}-{d}')
            this.parmas.start_date = list[0]
            this.parmas.end_date = list[1]
          }
          break
        // 巡查项目
        case 1:
          this.parmas.project = itemData.value
          if (!itemData.value) {
            delete this.parmas.project
          }
          break
        default:
          break
      }
      this.parmas.page = 1
      this.pageNo = 1
      this.getInspectionList()
    }
  }
}
</script>

<style lang="scss" scoped>
.daily_inspection_content {
  padding: 20rpx;
  height: calc(100vh - 168rpx);
  .inspection_content_item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx;
    background: #fff;
    border-radius: 10rpx;
    margin-bottom: 20rpx;
    .left_content {
      flex: 1;
      .left_box {
        display: flex;
        line-height: 48rpx;
        .left_box_title {
          width: 120rpx;
          text-align: right;
          margin-right: 40rpx;
        }
        .left_box_content {
          flex: 1;
        }
      }
    }
    .right_content {
      width: 120rpx;
      .btn {
        color: $color-primary;
      }
    }
  }
}
.btn_add_box {
  position: absolute;
  bottom: 20rpx;
  width: 80%;
  left: 10%;
  right: 10%;
}
</style>

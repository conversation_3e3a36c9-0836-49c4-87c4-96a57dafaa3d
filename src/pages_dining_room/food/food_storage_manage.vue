<template>
  <!--存餐管理-->
  <view class="food_storage_container">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('title.food.storage.management')"
      placeholder
      :autoBack="true"
      :leftIconColor="color.navigation"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    ></u-navbar>
    <!--#endif-->
    <!--筛选  -->
    <filterLayoutComponent :filterCustomStyle="filterCustomStyle" :filterDataLayoutList="drowDownDatalist" :isRightIConFill="isRightIConFill" :filterTiltleCustomStyle="filterTiltleCustomStyle" @handlerItemClick="handlerDropDownItemClick">
    </filterLayoutComponent>
    <!--存餐柜列表-->
    <mescroll-uni ref="mescrollRef" :fixed="false" :safearea="true" :bottom="0" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="{ auto: false }" :up="{ auto: false }" v-if="!isShowEmptyView">
      <view class="food_storage_list_item" v-for="(item,index) in foodCupboardList" :key="index">
        <view class="food_storage_item_title">
          <view class="food_storage_item_title_txt">{{item.device_name}}
          </view>
          <view class="food_storage_item_title_empty">{{ cabinetsFreeTxt+"：" }} <span :style="[item.ceil_num == '0'?emptyNumberColor:'']">{{item.ceil_num}}</span>
          </view>
        </view>
        <!--存餐码按钮-->
        <u-button class="food_storage_btn_style"   :customStyle="customStyleBtn"  :color="color.colorBtnOrange" @click="handlerQrCode(item)" :disabled="item.ceil_num == '0'">{{ btnQrCodeTxt}}</u-button>
      </view>
    </mescroll-uni>
     <!-- 空白页 -->
     <emptyComponent :emptyContent="emptyContent" v-if="isShowEmptyView"></emptyComponent>
    <!--存餐弹出码-->
    <qrCodeDialogComponent :qrDialogTitle="btnQrCodeTxt" ref="qrCodeDialog" :qrCodeContent="qrCodeContent"></qrCodeDialogComponent>

  </view>
</template>

<script>
import { apiMerchantMobileDeviceCupboardList } from '@/api/device'
import qrCodeDialogComponent from "@/components/QrCodeDialogComponent/QrCodeDialogComponent.vue"
import filterLayoutComponent from '@/components/FilterLayoutComponent/FilterLayoutComponent.vue'
import emptyComponent from '@/components/EmptyComponent/EmptyComponent.vue'
import { deepClone } from '../../utils/util'
import { getUserOrgsList } from "@/utils/userUtil"
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";

import { mapGetters } from 'vuex'
export default {
  data() {
    return {
      nameTitle: "烧腊档口",
      btnQrCodeTxt: this.$t('page.recommend.food.storage.code'), // 存餐码
      cabinetsFreeTxt: this.$t('page.recommend.number.of.free.cabinets'), // 空闲柜子数
      emptyNumberColor: { color: this.$store.state.color.colorBtnOrange },
      foodCupboardList: [],
      customStyleBtn: {
        width: '100rpx',
        height: '44rpx',
        borderRadius: '6rpx',
        padding: '0 !important',
        fontSize: '24rpx'
      },
      drowDownDatalist: [{
        title: "",
        chooseItem: "",
        leftIconName: 'map',
        leftIconSize: '36',
        leftIconWidth: '42rpx',
        leftIconHeight: '53rpx',
        dataList: []
      }],
      filterCustomStyle: { // 自定义筛选样式
        background: 'none',
        justifyContent: 'flex-start'
      },
      isRightIConFill: false,
      filterTiltleCustomStyle: {
        fontSize: '36rpx',
        margin: '0 18rpx'
      },
      parmas: { // 列表数据入参
        page: 1,
        page_size: 10
      },
      pageNo: 1, // 页码
      pageSize: 10, // 每页显示数据
      qrCodeContent: '', // 激活码内容
      isShowEmptyView: false, // 是否显示空白内容
      emptyContent: this.$t('tip.list.empty')

    }
  },
  mixins: [MescrollMixin],
  components: {
    qrCodeDialogComponent,
    filterLayoutComponent,
    emptyComponent
  },
  computed: {
    ...mapGetters(['color'])
  },
  /**
   * 页面加载
   */
  onload(e) {
    console.log("用户列表页面加载", e)
    this.initData()
  },
  /**
   * 页面创建
   */
  created() {
    this.initData()
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      // 获取数据
      console.log("this.parmas", this.parmas);
      this.parmas.page = this.pageNo
      this.parmas.page_size = this.pageSize
      // 先获取字典，将字典第一个，就是所在的根组织的id传过去
      this.getDicList()
    },
    /**
     * 下拉刷新返回
     */
    downCallback(page) {
      console.log(" downCallback page", page);
      this.pageNo = 1
      this.parmas.page = this.pageNo
      this.getDeviceList(this.parmas)
    },
    /**
     * 上拉加载更多
     * @param {*} page
     */
    upCallback(page) {
      console.log(" upCallback page", page);
      this.pageNo++
      this.parmas.page = this.pageNo
      this.getDeviceList(this.parmas)
    },
    /**
     * 获取设备列表
     * @param {*} parmas
     */
    getDeviceList(parmas) {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      apiMerchantMobileDeviceCupboardList(parmas)
        .then(res => {
          if (res.code === 0) {
            uni.hideLoading()
            var data = Reflect.has(res, "data") ? res.data : {}
            var resultList = Reflect.has(data, "results") ? data.results : []
            var count = data.count ? data.count : 0
            console.log("data", data, resultList);
            // 没有数据
            this.isShowEmptyView = this.pageNo === 1 && (!resultList || resultList.length === 0)
            if (this.pageNo === 1 && resultList && resultList.length > 0) {
              // 首次加载数据
              console.log("首次加载数据");
              this.foodCupboardList = deepClone(resultList)
            } else if (this.pageNo !== 1 && resultList && resultList.length > 0) {
              // 加载更多数据
              console.log("加载更多数据");
              this.foodCupboardList = this.foodCupboardList.concat(resultList)
            } else {
              // 其他情况
              console.log("其他情况");
              uni.hideLoading()
              uni.$u.toast(res.msg === '成功' ? '暂无数据' : res.msg)
            }
            this.mescroll.setPageNum(this.pageNo)
            this.mescroll.endBySize(this.pageSize, count)
          } else {
            uni.hideLoading()
            uni.$u.toast(res.msg)
            this.mescroll.endErr()
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
          this.mescroll.endErr()
        })
    },
    /**
     * 存餐码按钮点击
     * @param {*} e
     */
    async handlerQrCode(e) {
      console.log("handlerQrCode", e);
      try {
        this.qrCodeContent = e.put_meal_qrcode || ''
        // 展示激活码
        if (this.$refs.qrCodeDialog && typeof (this.$refs.qrCodeDialog.showQrDialog()) === "function") {
          this.$refs.qrCodeDialog.showQrDialog();
        }
      } catch (error) {
        console.log("error", error);
      }
    },
    /**
     * 列表筛选选择
     */
    handlerDropDownItemClick(itemData, index) {
      console.log("handlerDropDownTitleChoose", itemData, index);
      // 设置点击记录
      this.drowDownDatalist[index].chooseItem = itemData.name || ''
      // 更新列表
      if (Reflect.has(itemData, 'id')) {
        this.parmas.organization_id = itemData.id || ''
        this.getDeviceList(this.parmas)
      }
    },
    /**
     * 获取筛选字典
     */
    async getDicList() {
      var [error, res] = await this.$to(getUserOrgsList(2))
      console.log("getDicList", res, error);
      if (res && !error) {
        this.drowDownDatalist[0].dataList = deepClone(res)
        this.drowDownDatalist[0].chooseItem = res[0].name
        this.drowDownDatalist[0].title = res[0].name
        // 将根的组织ID赋值到入参
        this.parmas.organization_id = res[0].id
      }
      // 获取列表
      this.getDeviceList(this.parmas)
    }

  }
}
</script>

<style lang="scss" scoped>
.food_storage_container{
  height: calc(100vh - 168rpx);
  .food_storage_title{
    display: flex;
    justify-content: flex-start;
    box-sizing: border-box;
    padding: 30rpx 30rpx 10rpx 30rpx;
    align-items: center;
    cursor: pointer;
    .food_storage_title_txt{
      font-size: 36rpx;
      color: #1d1e20;
      margin :0 18rpx;
    }
  }
  .food_storage_list_item{
    width: 670rpx;
    height: 140rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20rpx auto;
    .food_storage_item_title{
      width: 100%;
      .food_storage_item_title_txt{
        display: block;
        font-size: 32rpx;
        color: #1d1e20;
      }
      .food_storage_item_title_empty{
        display: block;
        font-size: 24rpx;
        color: #8f9295;
        margin-top: 14rpx;
      }

    }
    .food_storage_btn_style{
      width: 100rpx;
      height: 44rpx;
      border-radius: 6rpx;
      padding: 0 !important;
      font-size: 24rpx;
    }

  }

}
</style>

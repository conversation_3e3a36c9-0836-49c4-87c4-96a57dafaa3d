<template>
  <!--添加菜品-->
  <view class="add_menu_container">
    <!--标题栏-->
    <!--#ifdef MP-WEIXIN || H5-->
    <u-navbar :title="$t('title.add.dishes')" placeholder :autoBack="true" :leftIconColor="color.navigation"
      leftIconSize="37rpx" :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"></u-navbar>
    <!--#endif-->
    <!--搜索栏-->
    <search-component ref="searchComponent" :searchPlaceholder="searchDataHolder" :isShowHistory="isShowHistory" v-show="isShowSearch"
      @handlerSearch="handlerMenuSearch" @searchHandlerItem="searchHandlerItem" @searchClear="searchClear" type="add_menu"
      :history-count="historyCount" @searchFocus="searchFocus"> </search-component>
    <!--列表-->
    <view class="add_menu_middle_list" v-for="(item, index) in menuDatalist" :key="index">
      <view class="add_menu_list_item">
        <image class="add_menu_list_img" :src="item.image ? item.image : imgPath.IMG_DEFAULT" alt="菜品"></image>
        <view class="add_menu_list_middle">
          <view class="add_menu_list_middle_title">{{ item.food_name }}</view>
          <view class="add_menu_list_middle_number">{{ txtInventory }}<span style="margin-left: 20rpx;"></span> {{
            item.number && item.number != -1 ? item.number : "无限制" }}</view>
          <!-- <view class="add_menu_list_middle_price">¥{{item.price}}</view> -->
        </view>
        <u-button class="add_menu_middle_btn" :customStyle="customStyleBtnListItem" :color="color.colorBtnOrange"
          @click="handlerAddDishesDialog(item, index)">{{ item.isAdd ? btnAlAddDishesSamllTxt : btnAddDishesSamllTxt
          }}</u-button>
      </view>
      <view class="horizontal_cell_line"></view>

    </view>
    <!--底部层-->
    <view class="add_menu_bottom_style padding-bottom-h5 padding-bottom-wechat">
      <view @click="showAddListDialog" class='add_menu_bottom_img'>
      <image  :src="hasMenu ? imgPath.IMG_GOODS_ACTIVE_ORANGE : imgPath.IMG_GOODS_EMPTY_GRAY"
        mode="scaleToFill" ></image>
      </view>
      <view v-if="hasMenu" class="add_menu_bottom_img_dot">{{ goodsAddNumber }}</view>
      <u-button class="add_menu_bottom_btn" :customStyle="customStyleBtn" :color="color.colorBtnOrange"
        :disabled="isAddMenuBtnDisabled" @click="handlerAddDishes()">{{ btnAddDishesTxt }}</u-button>
    </view>
    <!--弹窗-->
    <custom-dialog-component ref="customDialog" dialogType="input" :customDialogTitle="txtDishesInventory" isShowCancelBtn
      :isCloseOverlay="isCloseOverlay" @handlerCustomConfirm="handlerAddInventory" @handlerCustomCancel="handlerCustomCancel" custom-input-holder="输入库存数量（份）"
      :inputTipWarn="inputTipWarn"></custom-dialog-component>
    <custom-dialog-component ref="listingDialog" :customDialogTitle="txtListingDishes" :confirmBtnTxt="txtListing"
      :isCloseOverlay="isCloseOverlay" isShowCancelBtn @handlerCustomConfirm="handlerAddDishesConfirm" :customContent="customListingContent"
      @handlerCustomCancel="handlerCustomCancel"></custom-dialog-component>
    <!--上拉菜品弹窗-->
    <shopping-cart-component ref="shoppingCartDialog" :shopping-car-list="shoppingCartList"
      @modifyShoppingCardInventory="modifyShoppingCardInventory"
      @removeShoppingCardInventory="removeShoppingCardInventory"
      @clearShoppingCard="clearShoppingCard"
      @showShoppingCartChange = "showShoppingCartChange"
      :top-left-txt = 'topLeftTxt'
      ></shopping-cart-component>

  </view>
</template>

<script>
import searchComponent from '../../components/SearchComponent/SearchComponent.vue';
import customDialogComponent from '../../components/CustomDialogComponent/CustomDialogComponent.vue';
import shoppingCartComponent from '../components/ShoppingCartComponent.vue'
import { mapGetters } from 'vuex'
import { getApiBackgroundFoodMenuFoodList, getApiMerchantMobileMenuMenuFoodModify } from '../../api/dinging'
import { deepClone } from '../../utils/util';
import cache from '../../utils/cache';
// import { isNumber } from '../../utils/validata'

export default {
  name: '',
  data() {
    return {
      imgPath: this.$imgPath,
      searchDataHolder: this.$t('tip.please.enter.a.keyword.search'), // 搜索框提示语
      btnAddDishesTxt: this.$t('page.add.dishes.listing.Menu'), // 按钮名称
      btnAddDishesSamllTxt: this.$t('page.add.dishes.btn.add'), // 列表添加按钮名称
      btnAlAddDishesSamllTxt: this.$t('page.add.dishes.btn.add.already'), // 列表已添加按钮名称
      isShowHistory: false, // 默认显示历史记录
      customStyleBtn: { // 底部按钮样式
        width: '527rpx',
        height: '74rpx',
        backgroundColor: '#fd953c',
        borderRadius: '8rpx',
        fontSize: '30rpx'
      },
      customStyleBtnListItem: { // 列表小按钮样式
        width: '90rpx',
        height: '44rpx',
        backgroundColor: '#fd953c',
        borderRadius: '8rpx',
        fontSize: '24rpx',
        padding: '0 !important',
        alignSelf: 'flex-end'
      },
      isAddMenuBtnDisabled: true, // 默认按钮进来是不可点
      hasMenu: false, // 默认进来没有选择菜品
      menuDatalist: [], // 菜单列表
      menuDatalistClone: [],
      goodsAddNumber: 0, // 菜品添加数量
      pageNo: 1, // 页码
      pageSize: 9999, // 页数
      menuId: "",
      menuType: "week",
      currentDate: '', // 添加菜品日期
      mealSection: '', // 餐段
      searchName: '', // 搜索内容
      historyCount: 20,
      txtInventory: this.$t('page.menu.inventory'),
      txtDishesInventory: this.$t('page.menu.dishes') + this.$t('page.menu.inventory'),
      txtListing: this.$t('page.menu.listing'),
      txtListingDishes: this.$t('page.menu.listing.dishes'),
      txtTipComfirnListing: this.$t(''),
      shoppingCartList: [], // 购物车列表
      addInventoryIndex: -1, // 添加菜品的index
      addInventoryItem: null, // 添加菜品的index
      cacheGoodList: [], // 缓存商品数据
      inputTipWarn: this.$t('page.menu.tip.warn'),
      isCloseOverlay: false,
      customListingContent: "", // 上架提示
      mealSectionTxt: "", // 餐段提示
      isShowSearch: true,
      topLeftTxt: ''
    }
  },
  components: {
    searchComponent,
    customDialogComponent,
    shoppingCartComponent
  },
  computed: {
    ...mapGetters(['color'])
  },
  onLoad() {
    this.initData()
  },
  methods: {
    // 初始化数据
    initData() {
      var params = this.$Route.query || {}
      console.log("params", params);
      if (params && typeof params === 'object') {
        this.menuId = params.menuId ? params.menuId : ''
        this.menuType = params.menuType ? params.menuType : 'week'
        this.mealSection = params.mealSection ? params.mealSection : ''
        this.mealSectionTxt = params.mealSectionTxt ? params.mealSectionTxt : ''
        this.currentDate = params.currentDate ? params.currentDate : ''
        this.topLeftTxt = this.currentDate + " " + this.mealSectionTxt
      }
      this.cacheGoodList = cache.get(this.$common.KEY_UPDATE_GOODS_INFO) || []
      this.getFoodList()
    },
    /**
     * 搜索
     * @param {*}
     */
    handlerMenuSearch(e) {
      this.searchName = e
      if (this.$refs.searchComponent) {
        this.$refs.searchComponent.setSearchLayoutVisible(false)
      }
      this.getFoodList()
    },
    // 点击上架
    handlerAddDishes() {
      if (this.shoppingCartList.length === 0) {
        return this.$u.toast('请至少添加一个菜品')
      }
      if (this.$refs.listingDialog && Reflect.has(this.$refs.listingDialog, 'showCustomDialog')) {
        this.customListingContent = this.$t('page.menu.tip.listing.first') + this.currentDate + "," + this.mealSectionTxt + this.$t('page.menu.tip.listing.second')
        this.$refs.listingDialog.setContent(this.customListingContent)
        this.$refs.listingDialog.showCustomDialog()
      }
    },
    /**
     * 上架到菜谱按钮
     */
    async handlerAddDishesConfirm() {
      console.log("上架到菜谱按钮");
      var parmas = {
        id: this.menuId,
        meal_type: this.mealSection,
        set_meal_buy_limit: "{}",
        set_meal_setting_stock: "{}",
        setting_stock: "",
        use_date: this.currentDate
      }
      var settingStock = {}
      // 先看看有没有缓存，有缓存也要存进去入参
      if (this.cacheGoodList) {
        for (let j in this.cacheGoodList) {
          var goodList = this.cacheGoodList[j].list
          if (goodList && Array.isArray(goodList)) {
            goodList.forEach(item => {
              settingStock[item.id] = parseFloat(item.setting_stocks)
            })
          }
        }
      }
      // 第二步在把购物车的进行赋值
      for (let i in this.shoppingCartList) {
        settingStock[this.shoppingCartList[i].id] = parseFloat(this.shoppingCartList[i].number)
      }
      parmas.setting_stock = JSON.stringify(settingStock)
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      const [error, res] = await this.$to(getApiMerchantMobileMenuMenuFoodModify(parmas, this.menuType))
      uni.hideLoading()
      if (error) {
        return this.$u.toast("添加失败！")
      }
      if (res && res.code === 0) {
        this.$u.toast("添加成功")
        uni.$emit(this.$common.MSG_UPDATE_GOODS_ADD, { msg: "添加成功" })
        this.$miRouter.back()
      } else {
        this.$u.toast("添加失败" + res.msg)
      }
    },
    /**
     * 添加菜品弹出弹窗进行菜品库存添加
     * @param {菜品每项的数据} data
     */
    handlerAddDishesDialog(data, index) {
      console.log("handlerAddDishesDialog", data);
      this.addInventoryItem = data
      this.addInventoryIndex = index
      if (this.$refs.customDialog) {
        this.$refs.customDialog.showCustomDialog()
        if (data.number && data.number !== -1) {
          this.$refs.customDialog.setInputContent(data.number)
        }
      }
    },
    /**
     * 弹窗确认按钮点击
     * @params data  { inputContent: content, confirmTxt: this.confirmTxt }
     */
    handlerCustomConfirm(data) {
      var value = data.inputContent
      if (value) {
        if (this.$refs.customDialog) {
          this.$refs.customDialog.hideCustomDialog()
        }
      }
    },
    /**
     * 显示弹出上拉菜品列表
     */
    showAddListDialog() {
      console.log("showAddListDialog");
      if (this.$refs.shoppingCartDialog) {
        this.$refs.shoppingCartDialog.showShoppingCart(true)
      }
      this.isShowSearch = false
    },
    /**
     * 获取菜品列表
     */
    async getFoodList() {
      var parmas = {
        page: this.pageNo,
        page_size: this.pageSize,
        menu_type: this.menuType,
        id: this.menuId
      }
      if (this.searchName) {
        parmas.name = this.searchName
      }
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      const [error, res] = await this.$to(getApiBackgroundFoodMenuFoodList(parmas))
      uni.hideLoading()
      console.log("getFoodList", res);
      if (error) {
        uni.$u.toast(error.message)
        return
      }
      if (res && res.code === 0) {
        var data = res.data || {}
        if (data && typeof data === 'object' && Reflect.has(data, 'foods')) {
          var list = this.setCacheGood(data.foods)
          if (this.shoppingCartList) {
            list = this.setShoppCarCache(list)
          }
          this.menuDatalist = list || []
        }
      } else {
        var errMsg = res && res.msg ? res.msg : ''
        if (errMsg) {
          uni.$u.toast(errMsg)
        }
      }
    },
    // 点击历史记录进行搜索
    searchHandlerItem(e) {
      console.log("searchHandlerItem", e);
      this.searchName = e
      this.getFoodList()
    },
    // 添加菜品库存
    handlerAddInventory(e) {
      console.log("e", e, this.addInventoryItem);
      var count = e.inputContent
      if (count) {
        console.log(count);
        // if (!isNumber(count) && count !== "-1") {
        //   return this.$u.toast("数量有误，请输入正整数")
        // }
        let regNum = /^[0-9]{1,5}$/
        if (!regNum.test(count) && count !== "-1") {
          return this.$u.toast("数量有误，最大输入99999")
        }
        var item = deepClone(this.addInventoryItem)
        if (item) {
          item.number = count
          var index = -1
          var shoppingCartList = deepClone(this.shoppingCartList)
          for (let i in shoppingCartList) {
            if (shoppingCartList[i].id === item.id) {
              shoppingCartList[i] = item
              index = i
              break
            }
          }
          if (index === -1) {
            shoppingCartList.push(item)
          }
          this.$set(this, 'shoppingCartList', shoppingCartList)
          this.updateShoppingCart()
          console.log("this.shoppingCartLis", this.shoppingCartList);
          this.isAddMenuBtnDisabled = false
          this.hasMenu = true
          this.goodsAddNumber = this.shoppingCartList.length
          this.$set(this.menuDatalist[this.addInventoryIndex], 'isAdd', true)
          this.$set(this.menuDatalist[this.addInventoryIndex], 'number', count)
        }
        if (this.$refs.customDialog) {
          this.$refs.customDialog.hideCustomDialog()
        }
      } else {
        this.$toast({ title: '亲，请输入库存数量' })
      }
    },
    // 修改购物车库存
    modifyShoppingCardInventory(e) {
      console.log("modifyShoppingCardInventory", e)
      var index = -1
      for (let i in this.menuDatalist) {
        if (this.menuDatalist[i].id === e.id) {
          index = i
          break;
        }
      }
      this.handlerAddDishesDialog(e, index)
    },
    // 删除购物车库存
    removeShoppingCardInventory(e, index) {
      console.log("modifyShoppingCardInventory", e, index);
      var item = deepClone(e)
      if (index > -1 && index < this.shoppingCartList.length) {
        // 删除购物车添加的菜品
        this.shoppingCartList.splice(index, 1)
        this.updateShoppingCart()
      }
      // 更新菜品列表的状态
      var foodId = item.id
      var dataList = deepClone(this.menuDatalist)
      for (let i = 0; i < dataList.length; i++) {
        if (dataList[i].id === foodId) {
          dataList[i].isAdd = false
          dataList[i].number = -1
          break
        }
      }
      dataList = this.setCacheGood(dataList)
      this.$set(this, "menuDatalist", dataList)
      console.log("this.shoppingCartList", this.shoppingCartList.length);
      this.goodsAddNumber = this.shoppingCartList.length
      if (this.shoppingCartList.length === 0) {
        this.isAddMenuBtnDisabled = true
        this.hasMenu = false
        this.goodsAddNumber = ""
      }
    },
    // 清除购物车数据
    clearShoppingCard() {
      this.shoppingCartList = []
      this.updateShoppingCart()
      var list = deepClone(this.menuDatalist)
      if (list) {
        list.map(item => {
          item.isAdd = false
          item.number = -1
          return item
        })
      }
      list = this.setCacheGood(list)
      this.$set(this, 'menuDatalist', list)
      this.hasMenu = false
      this.goodsAddNumber = ""
      this.isAddMenuBtnDisabled = true
    },
    // 设置已经在卖的产品
    setCacheGood(goodsList) {
      if (this.cacheGoodList) {
        // 先把多维列表的产品组成一个新的列表
        var list = []
        for (let i in this.cacheGoodList) {
          var subList = this.cacheGoodList[i].list
          if (subList && Array.isArray(subList)) {
            subList.forEach(item => {
              list.push(item)
            })
          }
        }
        //  循环看一下后台返回的列表有没有属于我们已经上架的商品，有的就设置已添加，库存也要设置过去
        console.log("this.cacheGoodList", this.cacheGoodList);
        goodsList.forEach(menuItem => {
          var id = menuItem.id
          var findItem = list.find(subItem => {
            return subItem.id === id
          })
          if (findItem) {
            menuItem.number = findItem.setting_stocks
            menuItem.isAdd = true
          }
        })
        return goodsList
      }
      return goodsList
    },
    // 设置购物车数据 ，一般是在产品列表刷新的时候使用
    setShoppCarCache(goodsList) {
      if (this.shoppingCartList && this.shoppingCartList.length > 0) {
        goodsList.forEach(menuItem => {
          var id = menuItem.id
          var findItem = this.shoppingCartList.find(subItem => {
            return subItem.id === id
          })
          if (findItem) {
            console.log("findItemCard", findItem);
            menuItem.number = findItem.number
            menuItem.isAdd = true
          }
        })
        return goodsList
      }
      return goodsList
    },
    // 获取焦点
    searchFocus() {
      if (this.$refs.searchComponent) {
        this.$refs.searchComponent.setSearchLayoutVisible(true)
      }
    },
    // 搜索清除
    searchClear() {
      this.searchName = ""
      this.getFoodList()
    },
    // 弹窗隐藏
    handlerCustomCancel() {
      // 用户修改点击取消按钮
      if (Reflect.has(this.$refs, 'customDialog')) {
        this.$refs.customDialog.hideCustomDialog()
      }
      if (Reflect.has(this.$refs, 'listingDialog')) {
        this.$refs.listingDialog.hideCustomDialog()
      }
    },
    // 更新购物车数据
    updateShoppingCart() {
      if (this.$refs.shoppingCartDialog) {
        this.$refs.shoppingCartDialog.setShoppingCardList(this.shoppingCartList)
      }
    },
    // 显示购物车改变
    showShoppingCartChange(flag) {
      this.isShowSearch = !flag
    }
  }

}
</script>
<style lang="scss" scoped>
.add_menu_container {
  width: 100%;
  height: 100%;
  background: #ffffff;
  padding-bottom: 120rpx;

  .add_menu_middle_list {
    box-sizing: border-box;
    padding-bottom: 10rpx;

    .add_menu_list_item {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin: 36rpx;

      .add_menu_list_img {
        width: 121rpx;
        height: 120rpx;
        border-radius: 10rpx;

        &image {
          width: 121rpx;
          height: 120rpx;
        }
      }

      .add_menu_list_middle {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        flex: 1;
        padding: 0 20rpx;

        .add_menu_list_middle_title {
          font-size: 30rpx;
          color: #1d201e;

        }

        .add_menu_list_middle_number {
          font-size: 20rpx;
          color: #8f9295;
          margin-top: 14rpx;

        }

        .add_menu_list_middle_price {
          font-size: 32rpx;
          color: #ff5757;
          margin-top: 14rpx;
        }
      }

      .add_menu_middle_btn {
        width: 90rpx;
        height: 44rpx;
        border-radius: 8rpx;
        font-size: 24rpx;
        padding: 0 !important;
        align-self: flex-end;
      }

    }

    .horizontal_cell_line {
      width: 678rpx;
      height: 1px;
      background-color: #efefef;
      margin: 0 auto;
    }

  }
    /* #ifdef H5 */
  .padding-bottom-h5 {
    padding: 30rpx;
  }
  /* #endif */
  /* #ifdef MP-WEIXIN || MP-ALIPAY */
  .padding-bottom-wechat {
    padding: 30rpx 30rpx 60rpx 30rpx;
  }
  /* #endif */

  .add_menu_bottom_style {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: #ffffff;
    display: flex;
    flex-direction: row;
    justify-items: center;
    justify-content: space-between;
    .add_menu_bottom_btn {
      width: 527rpx;
      height: 74rpx;
      background-color: #fd953c;
      border-radius: 8rpx;
      font-size: 30rpx;
    }

    .add_menu_bottom_img_dot {
      width: 30rpx;
      height: 30rpx;
      line-height: 30rpx;
      text-align: center;
      border-radius: 15rpx;
      background: red;
      font-size: 20rpx;
      color: #ffffff;
      position: absolute;
      left: 88rpx;
      top: 30rpx;
    }

    .add_menu_bottom_img {
      width: 88rpx;
      height: 57rpx;
      margin-right: 50rpx;

      & image {
        width: 88rpx;
        height: 57rpx;
      }
    }
  }

}
</style>

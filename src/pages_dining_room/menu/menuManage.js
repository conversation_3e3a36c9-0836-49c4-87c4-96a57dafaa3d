import { getApiMerchantMobileMenuMenuFoodModify } from '../../api/dinging'
import { to } from "../../utils/util"
export const modifyMenuGoods = async function(list, menuId, mealSection, currentDate, menuType, tipMsg, that) {
  if (!list) {
    return
  }
  var parmas = {
    id: menuId,
    meal_type: mealSection,
    set_meal_buy_limit: "{}",
    set_meal_setting_stock: "{}",
    setting_stock: "",
    use_date: currentDate
  }
  var settingStock = {}
  for (let i in list) {
    var goodList = list[i].list
    if (goodList && Array.isArray(goodList)) {
      goodList.forEach(item => {
        settingStock[item.id] = parseFloat(item.setting_stocks)
      })
    }
  }
  parmas.setting_stock = JSON.stringify(settingStock)
  console.log("parmas", parmas);
  uni.showLoading()
  const [error, res] = await to(getApiMerchantMobileMenuMenuFoodModify(parmas, menuType))
  uni.hideLoading()
  if (error) {
    return that.$u.toast(tipMsg + "失败！")
  }
  if (res && res.code === 0) {
    that.$u.toast(tipMsg + "成功")
    that.getGoodsList(that.mealSection)
  } else {
    that.$u.toast(tipMsg + "失败" + res.msg)
  }
}

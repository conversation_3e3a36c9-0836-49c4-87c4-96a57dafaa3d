<template>
  <!--菜谱管理-->
  <view class="menumanage" catchtouchmove="true" >
    <view class="menumanage-t">
      <u-navbar :title="$t('Recipe.management')" :autoBack="true" :bgColor="bgColor" :leftIconColor="color.color1"
        placeholder leftIconSize="37rpx" :titleStyle="{ color: color.color1, fontSize: '37rpx' }"></u-navbar>
      <!-- #ifdef H5 -->
      <view class="menumanage_title_margin_bottom"></view>
      <!-- #endif -->
      <!--顶部筛选-->
      <filter-layout-component :filterCustomStyle="filterCustomStyle" :filterDataLayoutList="drowDownDatalist"
        :isRightIconFill="isRightIconFill" :filterTiltleCustomStyle="filterTiltleCustomStyle"
        :filterRightIConCustomStyle="filterRightIConCustomStyle" @handlerItemClick="handlerFilterItemClick"
        :isShowVerticalLine="isShowVerticalLine"></filter-layout-component>
    </view>
    <!-- 日历 -->
    <view class="calendar">
      <!-- <week-time @back="changeBack" @backs="changeBacker"></week-time> -->
      <calendar @changeBack="changeDay" :disabled="calDisabled"></calendar>
    </view>
    <!-- 餐段 -->
    <view class="segments">
      <view class="segments-l">{{ timeer }}</view>
      <view class="segments-r">
        <u-tabs lineWidth="60rpx" lineHeight="5" :activeStyle="{ color: color.themeColor }" :lineColor="color.themeColor" :current="currentMealType"
          :list="mealsegmentslist" @click="changeMealSection"></u-tabs>
      </view>
    </view>
    <view class="segments-img" v-if="handOff">
      <image :src="imgPath.IMG_GOODS_EMPTY_GRAY" mode="scaleToFill"></image>
      <view class="segmentstext">{{ $t('Recipe.currently') }}</view>
    </view>
    <view class="segments-dishes" v-if="!handOff">
      <scroll-view scroll-y class="segments-dishes-name" :style="[scrollStyle]">
        <view class="app_ul" :style="[leftMenuStyle]">
          <view class="app_li line-1" v-for="(item, index) in detailList" @click="changes(index)" :key="index"
            :class="{ options: index == numTabChoose }">
            {{ item.name }}
          </view>
        </view>
      </scroll-view>
      <scroll-view scroll-y :style="[scrollStyle]" v-if="detailList.length > 0"
        :show-scrollbar="detailList[numTabChoose] && detailList[numTabChoose].list.length > 2" enhanced>
        <view class="flex flex-col" v-if="detailList[numTabChoose] && detailList[numTabChoose].list.length > 0">
          <view class="segments-dishes-card" v-for="(items, subIndex) in detailList[numTabChoose].list" :key="subIndex">
            <view class="segments-dishes-cardimg m-t-10">
              <image :src="items.image ? items.image : imgPath.IMG_DEFAULT" mode="scaleToFill"></image>
            </view>
            <view class="segments-dishes-Information m-t-10">
              <view class="segments-dishes-Informationname">{{ items.food_name }}</view>
              <view class="segments-dishes-Informationnumber">库存 {{ items.setting_stocks == -1 ? '不限量' :
                items.current_stock + '份' }}</view>
                <!-- <view class="segments-dishes-Informationnumber text-decoration">￥ {{ items.origin_price | formatPrice }}</view> -->
              <view class="segments-dishes-Informationvalue m-t-10">
                <text class="w-150 line-break">￥{{ items.food_price | formatPrice }}</text>
                <view class="flex w-240">
                  <u-button class="custom-style" :customStyle="customStyleBtn"
                  :color="color.themeColor" @click="modifyNumber(items.id, items.current_stock)">
                  {{ $t('announcement.revise') }}
                </u-button>
                  <u-button class="custom-style" :customStyle="customStyleBtn"
                    :plain="true" :color="color.themeColor" @click="removeGood(items.id, subIndex)">
                    {{ $t('Recipe.Takedown') }}
                  </u-button>
                </view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
      <!-- <empty-component v-if="detailList.length <= 0" class="empty-view"></empty-component> -->
    </view>
    <!-- 按钮 -->
    <view class="botter-but padding-bottom-h5 padding-bottom-wechat ">
      <u-button :color="color.themeColor" @click="announcements">
        {{ $t('Recipe.Add.dishes') }}
      </u-button>
    </view>
    <!-- 修改的弹窗 -->
    <custom-dialog-component ref="modifyDialog" dialogType="input" :customDialogTitle="txtDishesInventory" isShowCancelBtn
      :isCloseOverlay="isCloseOverlay" @handlerCustomConfirm="handlerAddInventory" custom-input-holder="输入库存数量（份）"
      @handlerCustomCancel="handlerCustomCancel" :inputTipWarn="inputTipWarn"></custom-dialog-component>
    <!-- 下架的弹窗 -->
    <custom-dialog-component ref="removeDialog" dialogType="custom" :customDialogTitle="txtListing" isShowCancelBtn
      :isCloseOverlay="isCloseOverlay" @handlerCustomConfirm="handlerRemoveGoods"
      @handlerCustomCancel="handlerCustomCancel">
      <template slot="custom">
        <view>{{ $t('Recipe.prompt') }}</view>
      </template>
    </custom-dialog-component>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
import calendar from '@/components/Calendar/Calendar.vue'

import CustomDialogComponent from '@/components/CustomDialogComponent/CustomDialogComponent.vue'
import FilterLayoutComponent from '@/components/FilterLayoutComponent/FilterLayoutComponent.vue'
// import EmptyComponent from '../../components/EmptyComponent/EmptyComponent.vue'
import { getApiMerchantMobileMenuList, getApiMerchantMobileMenuFoodList, apiBackgroundReportCenterDataReportGetMealType } from '@/api/dinging'
import cache from '../../utils/cache'
import { getUserOrgsList } from '@/utils/userUtil'
import { deepClone, divide, getLastDayRange } from '../../utils/util'
import { modifyMenuGoods } from './menuManage'

export default {
  computed: {
    ...mapGetters(['color'])
  },
  components: {
    // weekTime,
    calendar,
    CustomDialogComponent,
    FilterLayoutComponent
    // EmptyComponent
  },
  data() {
    return {
      imgPath: this.$imgPath,
      packageer: [], // 左侧选项卡选项
      detailList: [], // 右侧套餐详情
      handOff: false, // 有无选则菜品
      numTabChoose: 0, // 套餐选项卡的索引
      timeer: '', // 显示的日期
      bgColor: 'rgba(0,0,0,0)',
      mealsegmentslist: [
        {
          name: this.$t('Meal.Breakfast'),
          value: 'breakfast'
        },
        {
          name: this.$t('Meal.lunch'),
          value: 'lunch'
        },
        {
          name: this.$t('Meal.tea'),
          value: 'afternoon'
        },
        {
          name: this.$t('Meal.dinner'),
          value: 'dinner'
        },
        {
          name: this.$t('Meal.supper'),
          value: 'supper'
        },
        {
          name: this.$t('Meal.early'),
          value: 'morning'
        }
      ],
      customStyleBtn: {
        width: '100rpx',
        height: '44rpx',
        borderRadius: '8rpx',
        fontSize: '24rpx',
        display: 'flex',
        marginRight: '20rpx',
        padding: '0 !important'
      },
      drowDownDatalist: [
        {
          // 筛选下拉列表数据
          title: '',
          chooseItem: '',
          leftIconName: 'map',
          leftIconSize: '36',
          leftIconWidth: '42rpx',
          leftIconHeight: '53rpx',
          leftIconColor: "#ffffff",
          rightIconColor: "#ffffff",
          dataList: []
        },
        {
          // 筛选下拉列表数据
          title: '',
          chooseItem: '',
          rightIconColor: "#ffffff",
          dataList: []
        }
      ],
      isRightIconFill: false, // 是否图标实心
      filterCustomStyle: {
        // 自定义头部筛选菜单样式
        background: 'none',
        fontSize: '32rpx'
      },
      filterTiltleCustomStyle: {
        // 自定义标题筛选菜单样式
        color: '#ffffff'
      },
      filterRightIConCustomStyle: {
        // 自定义右侧图标筛选菜单样式
        color: '#ffffff'
      },
      isShowVerticalLine: false, // 是否显示中间的分隔竖线
      orgsId: "", // 组织id
      menuType: 'week', // 菜谱类型
      menuId: "", // 菜谱ID
      currentDate: '', //  日期
      goodsList: [], // 菜品列表
      mealSection: 'breakfast', // 餐段
      mealSectionTxt: '早餐', // 餐段名称
      scrollStyle: {
        maxHeight: '780rpx'
      },
      leftMenuStyle: {
        height: '100%'
      },
      txtDishesInventory: this.$t('page.menu.dishes') + this.$t('page.menu.inventory'),
      txtListing: this.$t('Recipe.Takedown'),
      goodId: '', // 暂存商品Id
      goodIndex: -1, // 暂存商品index
      inputTipWarn: this.$t('page.menu.tip.warn'),
      isCloseOverlay: false,
      currentMealType: 0, // 当前餐段
      calDisabled: true // 禁止今天以前的时间选择
    }
  },
  onLoad(e) {
    this.initData()
    var that = this
    uni.$on(this.$common.MSG_UPDATE_GOODS_ADD, () => {
      console.log("更新菜品");
      that.getGoodsList(that.mealSection)
    })
  },
  filters: {
    // 格式化价格
    formatPrice(value) {
      return divide(value)
    }
  },
  onShow() {
    console.log("onShow");
  },
  destroyed() {
    console.log("destroyed");
    uni.$off(this.$common.MSG_UPDATE_GOODS_ADD)
  },
  methods: {
    //  初始化数据
    async initData() {
      var that = this
      uni.getSystemInfo({
        success(res) {
          var screenHeight = res.windowHeight
          if (screenHeight) {
            var height = (screenHeight - uni.upx2px(550)) + "px"
            console.log("screenHeight", screenHeight, "height", height);
            that.$set(that.scrollStyle, 'maxHeight', height)
            that.$set(that.leftMenuStyle, 'height', height)
          }
        }
      })
      this.orgsId = cache.get(this.$common.KEY_USER_ORGAN) || ""
      this.currentDate = getLastDayRange(1, '{y}-{m}-{d}')[0]
      console.log("orgsInfo", this.orgsId);
      // 初始化菜谱
      this.getMenuList()
      // 获取当前组织
      this.getOrgsList()
    },
    //  点击日历时时间的切换日期
    changeDay(timeData) {
      console.log("timeData", timeData)
      let objectDate = new Date(timeData.time)
      let day = objectDate.getDate()
      let month = objectDate.getMonth()
      this.timeer = `${month + 1}月${day}日`
      this.currentDate = timeData.fulldate
      this.numTabChoose = 0
      if (this.menuId) {
        this.getGoodsList(this.mealSection)
      }
    },
    // 弹窗隐藏
    handlerCustomCancel() {
      // 用户修改点击取消按钮
      if (Reflect.has(this.$refs, 'modifyDialog')) {
        this.$refs.modifyDialog.hideCustomDialog()
      }
      if (Reflect.has(this.$refs, 'removeDialog')) {
        this.$refs.removeDialog.hideCustomDialog()
      }
    },
    // 左侧选项修改
    changes(key) {
      this.numTabChoose = key
    },
    // 添加菜品
    announcements() {
      // 点击添加菜品
      console.log('添加菜品')
      if (!this.drowDownDatalist[1].dataList || this.drowDownDatalist[1].dataList.length === 0) {
        return this.$u.toast("请先在PC端配置菜谱！")
      }
      cache.set(this.$common.KEY_UPDATE_GOODS_INFO, this.detailList)
      this.$miRouter.push({
        path: '/pages_dining_room/menu/add_menu',
        query: {
          menuId: this.menuId,
          menuType: this.menuType,
          mealSection: this.mealSection,
          mealSectionTxt: this.mealSectionTxt,
          currentDate: this.currentDate
        }
      })
    },
    //  获取菜谱列表
    async getMenuList() {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      var parmas = {
        org_id: this.orgsId
      }
      const [error, res] = await this.$to(getApiMerchantMobileMenuList(parmas))
      uni.hideLoading()
      if (error) {
        if (error.message) {
          uni.$u.toast(error.message)
        }
        return
      }
      if (res && res.code === 0) {
        console.log("res", res);
        var datalist = res.data || []
        if (datalist && Array.isArray(datalist) && datalist.length > 0) {
          this.drowDownDatalist[1].dataList = deepClone(datalist)
          this.drowDownDatalist[1].title = datalist[0].name ? datalist[0].name : ''
          this.drowDownDatalist[1].chooseItem = datalist[0].name ? datalist[0].name : ''
          this.menuType = datalist[0].menu_type ? datalist[0].menu_type : ''
          this.menuId = datalist[0].id ? datalist[0].id : ''
          // 获取菜品列表
          this.getMealType()
        } else {
          this.resetEmptyList()
        }
      } else {
        console.log("res error", res.msg);
        this.resetEmptyList()
      }
    },
    // 充值列表
    resetEmptyList() {
      this.drowDownDatalist[1].dataList = []
      this.drowDownDatalist[1].title = ''
      this.drowDownDatalist[1].chooseItem = []
      this.goodsList = []
      this.detailList = []
      this.handOff = true
      this.menuId = ''
    },
    // 获取组织列表
    async getOrgsList() {
      // 组织类型列表
      var [err, res] = await this.$to(getUserOrgsList(2))
      console.log("getOrgsList", res);
      // 赋值
      if (res && !err && Array.isArray(res) && res.length > 0) {
        this.drowDownDatalist[0].dataList = deepClone(res)
        this.drowDownDatalist[0].title = res[0].name ? res[0].name : ''
        this.drowDownDatalist[0].chooseItem = res[0].name ? res[0].name : ''
      }
    },
    // 获取餐段
    async getMealType() {
      console.log("getMealType");
      var [err, res] = await this.$to(apiBackgroundReportCenterDataReportGetMealType({ org_id: this.orgsId }))
      if (res && !err && res.code === 0) {
        console.log("apiBackgroundReportCenterDataReportGetMealType", res);
        var data = res.data || {}
        var mealTypeList = data.meal_type_info || []
        var nowType = data.now_meal_type
        var currentIndex = 0
        if (mealTypeList) {
          mealTypeList = mealTypeList.map((item, index) => {
            item.name = item.meal_type_alias
            item.value = item.meal_type
            if (nowType && Reflect.has(nowType, 'meal_type') && item.meal_type === nowType.meal_type) {
              currentIndex = index
              this.mealSection = nowType.meal_type
              this.mealSectionTxt = nowType.meal_type_alias
            }
            return item
          })
          this.mealsegmentslist = deepClone(mealTypeList)
          this.currentMealType = currentIndex
          console.log("this.currentMealType", this.currentMealType, this.mealSection);
          this.getGoodsList(this.mealSection)
        }
      }
    },
    // 获取菜品列表
    async getGoodsList(type) {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      var parmas = {
        date: this.currentDate,
        menu_id: this.menuId,
        menu_type: this.menuType
      }
      const [error, res] = await this.$to(getApiMerchantMobileMenuFoodList(parmas))
      uni.hideLoading()
      if (error) {
        this.goodsList = []
        this.setCurrentGoodList(type)
        if (error.message) {
          uni.$u.toast(error.message)
        }
        return
      }
      if (res && res.code === 0) {
        console.log("res", res);
        var data = res.data || []
        if (data && typeof data === 'object' && Object.keys(data).length > 0) {
          this.goodsList = deepClone(data)
          console.log("this.goodsList", this.goodsList);
        } else {
          this.goodsList = []
        }
        this.setCurrentGoodList(type)
      } else {
        this.goodsList = []
        this.setCurrentGoodList(type)
        console.log("res error", res.msg);
      }
    },
    // 设置当前菜品详细列表
    setCurrentGoodList(type) {
      console.log("setCurrentGoodList", type);
      if (this.goodsList && Reflect.has(this.goodsList, type)) {
        var data = deepClone(this.goodsList[type])
        if (data && Reflect.has(data, 'food_data')) {
          var foodData = data.food_data
          if (foodData) {
            var newList = []
            for (let key in foodData) {
              var item = {
                name: key,
                list: foodData[key]
              }
              newList.push(item)
            }
            this.$set(this, 'detailList', newList)
          }
        }
      } else {
        this.detailList = []
      }
      this.handOff = this.detailList.length === 0
      console.log("this.detailList", this.detailList);
    },
    // 切换餐段
    changeMealSection(val) {
      console.log("changeMealSection", val);
      this.mealSection = val.value
      this.mealSectionTxt = val.name
      this.numTabChoose = 0
      this.setCurrentGoodList(this.mealSection)
    },
    modifyNumber(goodId, number) {
      this.goodId = goodId
      if (Reflect.has(this.$refs, 'modifyDialog')) {
        this.$refs.modifyDialog.showCustomDialog()
        if (number && number !== -1) {
          this.$refs.modifyDialog.setInputContent(number)
        }
      }
    },
    // 修改库存
    handlerAddInventory(value) {
      console.log("handlerAddInventory", value);
      var number = value.inputContent || 0
      console.log("number", number, typeof number, this.goodId);
      // let regNum = /(^[0-9]{1,5}$)| ^-[1]{1}$/
      let regNum = /^[0-9]{1,5}$/
      if (!regNum.test(number) && number !== "-1") {
        return this.$u.toast("数量有误，最大输入99999")
      }
      var list = deepClone(this.detailList)
      if (list[this.numTabChoose].list && Array.isArray(list[this.numTabChoose].list)) {
        list[this.numTabChoose].list = list[this.numTabChoose].list.map(item => {
          if (item.id === this.goodId) {
            item.setting_stocks = parseFloat(number)
          }
          return item
        });
      }
      modifyMenuGoods(list, this.menuId, this.mealSection, this.currentDate, this.menuType, "库存修改", this)
      this.handlerCustomCancel()
    },
    //  删除下架
    removeGood(goodId, index) {
      this.goodId = goodId
      this.goodIndex = index
      if (Reflect.has(this.$refs, 'removeDialog')) {
        this.$refs.removeDialog.showCustomDialog()
      }
    },
    //  下架
    handlerRemoveGoods() {
      console.log("handlerAddInventory", this.goodId);
      var list = deepClone(this.detailList)
      if (list[this.numTabChoose].list && Array.isArray(list[this.numTabChoose].list) && this.goodIndex !== -1) {
        list[this.numTabChoose].list.splice(this.goodIndex, 1)
      }
      modifyMenuGoods(list, this.menuId, this.mealSection, this.currentDate, this.menuType, "下架", this)
      this.handlerCustomCancel()
    },
    // 筛选层点击
    handlerFilterItemClick(e, index) {
      this.drowDownDatalist[index].title = e.name ? e.name : ''
      this.drowDownDatalist[index].chooseItem = e.name ? e.name : ''
      var id = e.id
      if (id && index === 0) {
        this.orgsId = id
        this.getMenuList()
      } else
      if (index === 1) {
        this.menuType = e.menu_type ? e.menu_type : ''
        this.menuId = e.id ? e.id : ''
        // 获取菜品列表
        this.getGoodsList(this.mealSection)
      }
      console.log("e", e)
    }
  }
}
</script>

<style lang="scss" scoped>
.menumanage {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background-color: #fff;
}

.menumanage_title_margin_bottom {
  margin-bottom: 50rpx;
}

.menumanage-t {
  width: 750rpx;
  height: 230rpx;
  background-image: linear-gradient(133deg, #fdac55 0%, #fe9c4b 50%, #ff8b41 100%);

  .choose {
    width: 750rpx;
    height: 80rpx;
    margin-top: 40rpx;
    background-color: rgba($color: #000000, $alpha: 0);
    display: flex;
    justify-content: center;
    align-items: center;

    .choose-organization,
    .choose-timesitem,
    .choose-groupitem {
      width: 375rpx;
      height: 80rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      select {
        border: none;
        color: #fff;
        font-size: 36rpx;
        background-color: rgba($color: #000000, $alpha: 0);

        option {
          color: #000;
        }
      }
    }
  }
}

.segments {
  width: 750rpx;
  height: 80rpx;
  border-top: 1rpx solid #efefef;
  border-bottom: 1rpx solid #efefef;
  background-color: #ffffff;
  display: flex;

  .segments-l {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 30rpx;
    color: #1d1e20;
    font-weight: bold;
    width: 197rpx;
    height: 80rpx;
    border-right: 1rpx solid #efefef;
  }

  .segments-r {
    height: 80rpx;
    width: 555rpx;
    border-right: 1rpx solid #efefef;
  }
}

.segments-img {
  width: 500rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin: 90rpx auto;

  image {
    width: 234rpx;
    height: 176rpx;
  }

  .segmentstext {
    margin-top: 37rpx;
    font-size: 30rpx;
    line-height: 24rpx;
    color: #c2c5c8;
    opacity: 0.7;
  }
}

.segments-dishes {
  margin-bottom: 107rpx;
  display: flex;

  .segments-dishes-name {
    height: 100%;
    background-color: #f0f3f5;
    overflow: hidden;

    .app_ul {
      height: 100%;
      .app_li {
        width: 198rpx;
        height: 100rpx;
        padding: 0px 10rpx;
        line-height: 100rpx;
        text-align: center;
      }

      .options {
        background-color: #fff;
        color: #fe9c4b;
      }
    }
  }

  .segments-dishes-card {
    width: 514rpx;
    height: 129rpx;
    display: flex;
    margin: 20rpx;
    .segments-dishes-cardimg {
      margin-right: 10rpx;
      width: 120rpx;
      height: 120rpx;

      image {
        width: 100%;
        height: 100%;
        border-radius: 20rpx;
      }
    }

    .segments-dishes-Information {
      display: flex;
      flex-direction: column;

      .segments-dishes-Informationname {
        font-size: 30rpx;
        color: #1d201e;
        width: 360rpx;
      }

      .segments-dishes-Informationnumber {
        font-size: 20rpx;
        height: 19rpx;
        color: #8f9295;
        margin: 5rpx 0;
      }
      .text-decoration {
        text-decoration: line-through;
      }

      .segments-dishes-Informationvalue {
        height: 44rpx;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;

        text {
          height: 44rpx;
          line-height: 44rpx;
          font-size: 32rpx;
          color: #ff5757;
        }

        .custom-style {
          width: 100rpx;
          height: 44rpx;
          line-height: 44rpx;
          border-radius: 8rpx;
          font-size: 24rpx;
          display: flex;
          margin-right: 20rpx;
          padding: 0 !important;
        }
      }
    }
  }

  .segments-dishes-value {
    width: 100%;
    flex: 1;

    .segments-dishes-text {
      margin: 20rpx;
    }
  }
}

.botter-but {
  width: 750rpx;
  height: 107rpx;
  background-color: #ffffff;
  padding: 19rpx 40rpx 18rpx 40rpx;
  position: absolute;
}
/* #ifdef H5 */
.padding-bottom-h5 {
  bottom: 0;
}
/* #endif */
/* #ifdef MP-WEIXIN || MP-ALIPAY */
.padding-bottom-wechat {
  bottom: 40rpx;
}
/* #endif */
.empty-view{
  margin: 0 auto !important;
}
</style>

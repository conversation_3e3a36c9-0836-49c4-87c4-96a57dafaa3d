<template>
  <!--订单补扣-->
  <view class="order_deduction_container" catchtouchmove="true">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('title.order.supplementary.deduction')"
      placeholder
      :autoBack="true"
      :leftIconColor="color.navigation"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    ></u-navbar>
    <!--#endif-->
    <!--筛选层-->
    <view class="search-container">
      <searchComponent :searchPlaceholder="userData.head.searchDataHolder"  @searchClear="searchClear" @handlerSearch="handlerOrderSearch" type="order_deduction"></searchComponent>
    </view>
    <view class="warn_tip_style">{{warnTip}}</view>
    <!--内容列表-->
    <view class="order_deduction_content" v-if="!isShowEmptyView">
     <mescroll-uni ref="mescrollRef" :fixed="false" :safearea="true" :bottom="0" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="{ auto: false }" :up="{ auto: false }" >
      <view class="order_deduction_item" v-for="(item,index) in orderList" :key="index" >
        <view >
          <!--订单号头部-->
          <view class="order_deduction_item_title">{{ userData.head.orderNoTxt+": "+item.trade_no?item.trade_no:'' }}</view>
          <!--分割灰线-->
          <view class="horizontal_cell_line"></view>
          <!--订单号内容-->
          <horizontalCellComponent :cellStyle="cellStyle" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.head.ceateTimeTxt" :valueTxt="item.create_time"   />
          <horizontalCellComponent :cellStyle="cellStyle" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.head.paymentTimeTxt" :valueTxt="item.pay_time"   />
          <horizontalCellComponent :cellStyle="cellStyle" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.head.userNameTxt" :valueTxt="item.name"   />
          <horizontalCellComponent :cellStyle="cellStyle" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.head.mobileTxt" :valueTxt="item.phone"   />
          <horizontalCellComponent :cellStyle="cellStyle" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.head.orderAmountTxt" :valueTxt="item.origin_fee.toString()"   />
          <horizontalCellComponent :cellStyle="cellStyle" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="userData.head.failureReasonTxt" :valueTxt="item.error_reason"   />
          <!--按钮层-->
          <view class="order_deduction_btns_content">
              <u-button class="order_deduction_btn_style"  :customStyle="customStyleBtn" plain :color="color.colorBtnOrange" @click="cancelOrder(item)">{{ userData.head.btnCancelOrderTxt }}</u-button>
              <u-button  :customStyle="customStyleBtn" class="order_deduction_btn_style" :color="color.colorBtnBlack" @click="deductionPrice(item)">{{ userData.head.btnDeductionPriceTxt }}</u-button>
              <u-button  :customStyle="customStyleBtn" class="order_deduction_btn_style" :color="color.colorBtnOrange" @click="rededuction(item)">{{ userData.head.btnRedeductionTxt }}</u-button>
          </view>
        </view>
      </view>
     </mescroll-uni>
    </view>
    <!-- 空白页 -->
    <emptyComponent :emptyContent="emptyContent" v-if="isShowEmptyView"></emptyComponent>
    <!--#ifdef MP-WEIXIN || MP-ALIPAY -->
    <CustomDialogComponent ref="customDialog"></CustomDialogComponent>
    <!--#endif-->
  </view>
</template>

<script>
import searchComponent from "@/components/SearchComponent/SearchComponent"
import emptyComponent from "@/components/EmptyComponent/EmptyComponent"
import horizontalCellComponent from "@/components/HorizontalCellComponent/HorizontalCellComponent.vue"
import { apiBackgroundOrderOrderOfflineList, apiBackgroundOrderOrderOfflineOrderClose, apiBackgroundOrderOrderOfflineOrderPay } from '@/api/order'
import { mapGetters } from 'vuex'
import { deepClone, divide, getLastDayRange } from "../../utils/util"
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";

export default {
  data() {
    return {
      cellStyle: { height: "50rpx" },
      titleStyle: { 'font-size': "24rpx", color: '#8f9295' },
      valueStyle: { 'font-size': "24rpx", color: '#1d1e20' },
      orderList: [],
      userData: {
        head: {
          searchDataHolder: this.$t('tip.order.please.enter.name.mobile'),
          orderNoTxt: this.$t('page.order.order.number'),
          ceateTimeTxt: this.$t('page.order.create.time'),
          paymentTimeTxt: this.$t('page.approval.order.payment.time'),
          userNameTxt: this.$t('page.order.the.user.name'),
          mobileTxt: this.$t('page.user.mobile'),
          orderAmountTxt: this.$t('page.order.order.amount'),
          failureReasonTxt: this.$t('page.order.failure.reason'),
          btnCancelOrderTxt: this.$t('page.order.btn.cancel.order'),
          btnDeductionPriceTxt: this.$t('page.order.btn.deduction.from.original.price'),
          btnRedeductionTxt: this.$t('page.order.btn.rededuction')
        },
        base: {
          name: this.$t('page.user.name'),
          sex: this.$t('page.user.sex'),
          birthday: this.$t('page.user.birthday'),
          mobile: this.$t('page.user.mobile'),
          nameTxt: "诸葛明亮",
          sexTxt: "女",
          birthdayTxt: "1990-10",
          mobileTxt: "158*****5545"

        }

      },
      customStyleBtn: {
        width: '120rpx',
        height: '48rpx',
        borderRadius: '6rpx',
        fontSize: '24rpx',
        margin: '0 10rpx',
        padding: '0 !important'
      },
      pageNo: 1, // 页码
      pageSize: 10, // 每页显示数据
      parmas: {}, // 参数
      isShowEmptyView: false, // 是否显示空白内容
      emptyContent: this.$t('tip.list.empty'),
      warnTip: this.$t('tip.show.only.last.three.month.order')
    }
  },
  computed: {
    ...mapGetters(['color'])
  },
  mixins: [MescrollMixin],
  components: { searchComponent, horizontalCellComponent, emptyComponent },
  /**
   * 页面加载
   */
  onload(e) {
    // 页面初始化数据
    this.initData()
  },
  created() {
    this.initData()
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      // 获取数据
      console.log("this.parmas", this.parmas);
      this.parmas.page = this.pageNo
      this.parmas.page_size = this.pageSize
      this.parmas.meal_type = 'all'
      // 时间跟产品确认为前三个月的订单
      this.parmas.start_create_time = getLastDayRange(90)[0]
      this.parmas.end_create_time = getLastDayRange(90)[1]
      this.getOrderList(this.parmas)
    },
    /**
     * 下拉刷新返回
     */
    downCallback(page) {
      console.log(" downCallback page", page);
      this.pageNo = 1
      this.parmas.page = this.pageNo
      this.getOrderList(this.parmas)
    },
    /**
     * 上拉加载更多
     * @param {*} page
     */
    upCallback(page) {
      console.log(" upCallback page", page);
      this.pageNo++
      this.parmas.page = this.pageNo
      this.getOrderList(this.parmas)
    },
    /**
     * 获取订单列表
     * @param {*} parmas
     */
    async  getOrderList(parmas) {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      const [error, res] = await this.$to(apiBackgroundOrderOrderOfflineList(parmas))
      if (error) {
        this.mescroll.endErr()
        uni.$u.toast(error.message)
        return
      }
      if (res.code === 0) {
        uni.hideLoading()
        var data = Reflect.has(res, "data") ? res.data : {}
        var resultList = Reflect.has(data, "results") ? data.results : []
        var count = data.count ? data.count : 0
        // 这里要进行处理，数据在order_payment 里面,利用循环把他放出来
        if (resultList) {
          resultList.map(item => {
            var orderPayment = item.order_payment || {}
            for (let key in orderPayment) {
              // 如果包含相同的key，就加上order_前缀
              if (Object.prototype.hasOwnProperty.call(item, key)) {
                item["order_" + key] = orderPayment[key]
              } else {
                item[key] = orderPayment[key]
              }
            }
            if (Reflect.has(item, 'origin_fee')) {
              item.origin_fee = divide(item.origin_fee || 0)
            }
            delete item.order_payment
            return item
          })
        }
        console.log("data", data, resultList);
        // 没有数据
        this.isShowEmptyView = this.pageNo === 1 && (!resultList || resultList.length === 0)
        if (this.pageNo === 1 && resultList && resultList.length > 0) {
          // 首次加载数据
          console.log("首次加载数据");
          this.orderList = deepClone(resultList)
        } else if (this.pageNo !== 1 && resultList && resultList.length > 0) {
          // 加载更多数据
          console.log("加载更多数据");
          this.orderList = this.orderList.concat(resultList)
        } else {
          // 其他情况
          console.log("其他情况");
          this.orderList = []
          uni.hideLoading()
          var message = this.pageNo === 1 ? '找不到对应订单' : '没有更多了'
          uni.$u.toast(res.msg !== '成功' && res.msg !== 'OK' ? res.msg : message)
        }
        this.mescroll.setPageNum(this.pageNo)
        this.mescroll.endBySize(this.pageSize, count)
      } else {
        this.orderList = []
        uni.hideLoading()
        uni.$u.toast(res.msg)
        this.mescroll.endErr()
      }
    },

    /**
     * 点击搜索
     */
    handlerOrderSearch(e) {
      console.log("handlerOrderSearch");
      if (e && e.length) {
        // 根据用户输入的内容重新获取列表
        this.parmas.search_condition = e
        this.pageNo = 1
        this.initData()
      }
    },
    /**
     * 搜索清除
     */
    searchClear() {
      console.log("searchClear");
      delete this.parmas.search_condition
      this.pageNo = 1
      this.initData()
    },
    /**
     * 取消订单
     */
    async cancelOrder(e) {
      console.log("取消订单", e);
      this.showDialogByType(e, 1, "取消订单吗")
    },
    /**
     * 原价扣款
     */
    deductionPrice(e) {
      console.log("原价扣款", e);
      this.showDialogByType(e, 2, "是否原价扣款?")
    },
    /**
     * 重新扣款
     */
    rededuction(e) {
      console.log("重新扣款", e);
      this.showDialogByType(e, 3, "重新发起扣款吗?")
    },
    /**
     * 根据显示警告弹窗
     * @param itemData  每项的数据
     * @param type 1 取消订单  2 原价扣款， 3 重新扣款
     * @param tipMsg  提示语
     */
    showDialogByType(itemData, type, tipMsg) {
      var that = this
      that.$confirm(
        {
          dialogTypeValue: "content", // 弹窗类型
          titleTxt: that.$t('tip.prompt'), // 弹窗标题
          contentTxt: tipMsg,
          isShowDialog: true, // 是否显示弹窗
          cancelCallBack: function() {
            console.log("点击取消");
          },
          confirmCallBack: function(data) {
            console.log("confirmCallback", data);

            switch (type) {
              // 取消订单
              case 1:
                that.setOrderClose(itemData)

                break;
              // 原价扣款，
              case 2:
                that.replayOrder(itemData, true)

                break;
              // 重新扣款
              case 3:
                that.replayOrder(itemData, false)

                break;

              default:
                break;
            }
          }
        }, that)
    },
    /**
     * 调用接口 取消订单
     * @param {*} e
     */
    async setOrderClose(e) {
      this.$showLoading({
        title: '请稍后...',
        mask: true
      })
      var [error, res] = await this.$to(apiBackgroundOrderOrderOfflineOrderClose({ order_payment_id: e.order_id }))
      uni.hideLoading()
      if (error) {
        this.$toast({ title: '取消订单失败，' + error.message })
        return
      }
      if (res.code === 0) {
        console.log("apiBackgroundOrderOrderOfflineOrderClose", res)
        this.$toast({ title: '取消订单成功' })
        this.getListSleep()
      } else {
        this.$toast({ title: res.msg })
      }
    },
    /**
     * 调用重新扣款接口
     * @param {} e
     * @param  isOriginalPrice 是否原价扣款
     */
    async replayOrder(e, isOriginalPrice) {
      this.$showLoading({
        title: '请稍后...',
        mask: true
      })
      var [error, res] = await this.$to(apiBackgroundOrderOrderOfflineOrderPay({ order_payment_id: e.order_id, is_original_price: isOriginalPrice }))
      uni.hideLoading()
      if (error) {
        this.$toast({ title: '扣款失败，' + error.message })
        return
      }
      if (res.code === 0) {
        console.log("apiBackgroundOrderOrderOfflineOrderPay", res)
        this.$toast({ title: '扣款成功' })
        this.getListSleep()
      } else {
        this.$toast({ title: res.msg })
      }
    },
    /**
     * 延迟获取列表，防止toast弹窗没有显示
     */
    getListSleep() {
      setTimeout(() => {
        this.initData()
      }, 500)
    }

  }

}
</script>

<style lang="scss" scoped>
.order_deduction_container{
  .search-container{
    position: fixed;

  }
  .order_deduction_content{
    padding :20rpx;
    height: calc(100vh - 214rpx);
    .order_deduction_item{
      width: 670rpx;
      height: 476rpx;
      background-color: #ffffff;
      border-radius: 12rpx;
      margin :20rpx auto;
      .order_deduction_item_title{
        height: 80rpx;
        line-height: 80rpx;
        font-size: 30rpx;
        color: #1d1e20;
        padding-left: 30rpx;
      }

    }
    .horizontal_cell_line{
      width: 610rpx;
      height: 1rpx;
      background-color: #eae9ed;
      margin:0 30rpx 20rpx ;
    }
    .order_deduction_btns_content{
      display: flex;
      justify-content: flex-end;
      margin:  10rpx  20rpx;
      flex-wrap: wrap;
    .order_deduction_btn_style{
      width: 120rpx;
      height: 48rpx;
      border-radius: 6rpx;
      font-size: 24rpx;
      margin: 0 10rpx;
      padding: 0 !important;
    }
  }

  }
  .warn_tip_style{
    color: $color-primary;
    margin: 10rpx 30rpx;
    font-size: 24rpx;
    padding-top:130rpx;
  }

}
</style>

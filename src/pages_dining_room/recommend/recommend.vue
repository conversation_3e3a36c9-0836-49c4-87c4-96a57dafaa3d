<template>
  <!--评价建议-->
  <view class="recommend_container">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('title.evaluation.recommendations')"
      placeholder
      :autoBack="true"
      :leftIconColor="color.navigation"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    ></u-navbar>
    <!--#endif-->
    <!--头部筛选 -->
    <filterLayoutComponent :filterDataLayoutList="menuDataList" @handlerItemClick="handlerMenuItemClick"></filterLayoutComponent>
    <!--内容列表 -->
    <view class="recommend_content" v-if="!isShowEmptyView">
     <mescroll-uni ref="mescrollRef" :fixed="false" :safearea="true" :bottom="0" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="{ auto: false }" :up="{ auto: false, use: false }" >
      <view class="recommend_content_item" v-for="(item,index) in recommendList" :key="index" >
        <view >
          <!--评价头部-->
          <horizontalCellComponent :cellStyle="cellStyle" :titleStyle="titleStyle" :valueStyle="valueStyle" :titleTxt="item.type=='evaluation'?userData.head.appraiseTxt:userData.head.suggestTxt" :valueTxt="item.create_time"   />
          <!--分割灰线-->
          <view class="horizontal_cell_line"></view>
          <!--订单号内容-->
          <horizontalCellComponent :leftIcon="leftIcon" :iconStyle="iconStyle" :cellStyle="cellStyle" :titleStyle="titleStyle" :valueStyle="item.type=='evaluation'?valueStyle:valueNoReplyStyle" :titleTxt="item.type=='evaluation'?item.organization_name:item.company_name" :valueTxt="getEvaluationTxt(item)"   />
           <!--评价的星星层-->
          <view class="recommend_content_rate" v-if="item.evaluation_score">
            <view class="recommend_content_rate_item" v-for="(rateItem,subIndex) in item.evaluation_score" :key="subIndex">
               <view class="recommend_content_rate_title">{{ rateItem.field_name }}</view>
               <u-rate  :count="userData.head.rateCount" v-model="rateItem.score" readonly size="28rpx" inactiveColor="#e3e4e8" activeColor="#FFC45E" gutter="15"></u-rate>
            </view>
          </view>
           <!--回复文字层-->
           <view class="recommend_content_reply">
            <view class="recommend_content_reply_txt"><span>{{ item.type=='evaluation'?userData.head.evaluationTxt:userData.head.feedbackTxt}}：</span>
              {{ item.type=='evaluation' ? item.evaluation_content_txt : item.remark_txt }}
            </view>
           </view>
          <!--按钮层-->
          <view class="recommend_btns_content">
              <u-button :customStyle="customStyleBtn" class="recommend_btn_style" plain :color="color.colorBtnOrange" @click="viewDetail(item,index)">{{ userData.head.btnViewDetailTxt }}</u-button>
              <u-button :customStyle="customStyleBtn" v-if="item.isShowReplyBtn" class="recommend_btn_style" :color="color.colorBtnOrange" @click="handlerReply(item,index)">{{ userData.head.btnViewReplyTxt }}</u-button>
          </view>
        </view>
      </view>
     </mescroll-uni>
    </view>
     <!-- 空白页 -->
     <emptyComponent :emptyContent="emptyContent" v-if="isShowEmptyView"></emptyComponent>

  </view>
</template>

<script>
import filterLayoutComponent from "@/components/FilterLayoutComponent/FilterLayoutComponent.vue"
import horizontalCellComponent from "@/components/HorizontalCellComponent/HorizontalCellComponent.vue"
import emptyComponent from "@/components/EmptyComponent/EmptyComponent.vue"
import { apiMerchantMobileEvaluationManagementList } from '@/api/appraise'
import { mapGetters } from 'vuex'
import { deepClone, getLastDayRange } from "../../utils/util"
import comDic from "../../common/comDic"
import { getUserOrgsList } from "@/utils/userUtil"
import cache from "@/utils/cache"
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";

export default {
  data() {
    return {
      cellStyle: { height: "80rpx" },
      titleStyle: { 'font-size': "28rpx", color: '#1d1e20' },
      valueStyle: { 'font-size': "24rpx", color: '#8f9295' },
      valueNoReplyStyle: { 'font-size': "24rpx", color: '#fd953c' },
      leftIcon: this.$imgPath.IMG_RECOMMEND_TITLE_GRAY,
      iconStyle: { width: '30rpx', height: '30rpx', marginTop: '-5rpx' },
      customStyleBtn: {
        width: '120rpx',
        height: '48rpx',
        borderRadius: '6rpx',
        fontSize: '24rpx',
        margin: '0 10rpx',
        padding: '0 !important'

      },
      menuDataList: [
        {
          title: '时间',
          chooseItem: '今天',
          dataList: comDic.DIC_RECOMMEND_DATE
        },
        {
          title: '组织',
          chooseItem: '全部',
          dataList: []
        },
        {
          title: '回复状态',
          chooseItem: '全部',
          dataList: comDic.DIC_RECOMMEND_REPLY_STATUS
        },
        {
          title: '类型',
          chooseItem: '全部',
          dataList: comDic.DIC_RECOMMEND_REPLY_TYPE
        }
      ],
      recommendList: [],
      userData: {
        head: {
          appraiseTxt: this.$t('page.recommend.appraise'),
          suggestTxt: this.$t('page.recommend.suggest'),
          btnViewDetailTxt: this.$t('page.user.view.detail'),
          btnViewReplyTxt: this.$t('page.recommend.reply'),
          repliedTxt: this.$t('page.recommend.replied'),
          noReplyTxt: this.$t('page.recommend.no.reply'),
          tasteTxt: this.$t('page.recommend.taste'),
          priceTxt: this.$t('page.recommend.price'),
          serviceTxt: this.$t('page.recommend.service'),
          hygienismTxt: this.$t('page.recommend.hygienism'),
          evaluationTxt: this.$t('page.recommend.evaluation.content'),
          feedbackTxt: this.$t('page.recommend.feedback.content'),
          rateCount: 5
        },
        base: {
          name: this.$t('page.user.name'),
          sex: this.$t('page.user.sex'),
          birthday: this.$t('page.user.birthday'),
          mobile: this.$t('page.user.mobile'),
          nameTxt: "诸葛明亮",
          sexTxt: "女",
          birthdayTxt: "1990-10",
          mobileTxt: "158*****5545"

        }

      },
      pageNo: 1, // 页码
      pageSize: 10, // 每页显示数据
      parmas: {}, // 参数
      isShowEmptyView: false, // 是否显示空白内容
      emptyContent: this.$t('tip.list.empty')
    }
  },
  computed: {
    ...mapGetters(['color'])
  },
  mixins: [MescrollMixin],
  components: { filterLayoutComponent, horizontalCellComponent, emptyComponent },
  /**
   * 页面加载
   */
  onload(e) {
    console.log("用户列表页面加载", e)
    this.initData()
  },
  created() {
    this.initData()
  },
  /**
   * 页面显示
   */
  onShow() {
    console.log("onShow");
    this.updateMessage()
  },
  /**
   * 页面挂载
   */
  mounted() {
    console.log('mounted');
    this.updateMessage()
  },
  /**
   * 页面销毁
   */
  destroyed() {
    console.log('destroyed');
    this.removeMessage()
  },
  /**
   * 页面销毁
   */
  onUnload() {
    console.log("onUnload");
    this.removeMessage()
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      // 获取数据
      console.log("this.parmas", this.parmas);
      this.parmas.page = this.pageNo
      this.parmas.page_size = this.pageSize
      // 默认今天
      this.parmas.start_date = getLastDayRange(1, '{y}-{m}-{d}')[0]
      this.parmas.end_date = getLastDayRange(1, '{y}-{m}-{d}')[1]
      // todo debug
      // this.parmas.start_date = getLastDayRange(29, '{y}-{m}-{d}')[0]
      // this.parmas.end_date = getLastDayRange(1, '{y}-{m}-{d}')[1]
      // 获取字典
      this.getDicList()
    },
    /**
     * 下拉刷新返回
     */
    downCallback(page) {
      console.log(" downCallback page", page);
      this.pageNo = 1
      this.parmas.page = this.pageNo
      this.getRecommendList(this.parmas)
    },
    /**
     * 上拉加载更多
     * @param {*} page
     */
    upCallback(page) {
      console.log(" upCallback page", page);
      // this.pageNo++
      // this.parmas.page = this.pageNo
      // this.getRecommendList(this.parmas)
    },
    /**
     * 获取消息列表
     * @param {*} parmas
     */
    async  getRecommendList(parmas) {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      const [error, res] = await this.$to(apiMerchantMobileEvaluationManagementList(parmas))
      uni.hideLoading()
      if (error) {
        this.mescroll.endErr()
        if (this.pageNo === 1) {
          this.isShowEmptyView = true
        }
        if (error.message) {
          uni.$u.toast(error.message)
        }
        return
      }
      if (res.code === 0) {
        var data = Reflect.has(res, "data") ? res.data : {}
        // var resultList = Reflect.has(data, "results") ? data.results : []
        if (data) {
          var evaluationList = data.evaluation_list || []
          evaluationList = this.setListType('evaluation', evaluationList)
          var feedbackList = data.feedback_list || []
          feedbackList = this.setListType('feedback', feedbackList)
          var resultList = evaluationList.concat(feedbackList)
          // var count = resultList.length
          console.log("data", data, resultList);
          // 没有数据
          this.isShowEmptyView = this.pageNo === 1 && (!resultList || resultList.length === 0)
          if (this.pageNo === 1 && resultList && resultList.length > 0) {
            // 首次加载数据
            console.log("首次加载数据");
            this.recommendList = deepClone(resultList)
          } else if (this.pageNo !== 1 && resultList && resultList.length > 0) {
            // 加载更多数据
            console.log("加载更多数据");
            this.recommendList = this.recommendList.concat(resultList)
          } else {
            // 其他情况
            console.log("其他情况");
            this.recommendList = []
            uni.hideLoading()
            uni.$u.toast(res.msg !== '成功' && res.msg !== "OK" ? res.msg : '暂无数据')
          }
          // this.mescroll.endBySize(this.recommendList.length, count)
          this.mescroll.endErr();
        }
      } else {
        this.recommendList = []
        if (res.msg) {
          uni.$u.toast(res.msg)
        }
        this.mescroll.endErr()
      }
    },

    /**
     * 筛选列表点击
     * @param {*} itemData
     * @param index  选择的列表位置
     */
    handlerMenuItemClick(itemData, index) {
      console.log("handlerChooseItemClick", itemData, index);
      // 设置点击记录
      this.menuDataList[index].chooseItem = itemData.name
      // 根据选择组参
      switch (index) {
        // 时间
        case 0:
          if (itemData.value === 'today') { // 今天
            this.parmas.start_date = getLastDayRange(1, '{y}-{m}-{d}')[0]
            this.parmas.end_date = getLastDayRange(1, '{y}-{m}-{d}')[1]
          }
          if (itemData.value === 'sevenday') {
            this.parmas.start_date = getLastDayRange(7, '{y}-{m}-{d}')[0]
            this.parmas.end_date = getLastDayRange(7, '{y}-{m}-{d}')[1]
          }
          console.log("this.parmas", this.parmas);
          break;
        // 组织
        case 1:
          this.parmas.org_ids = [itemData.id]
          break;
        // 回复状态
        case 2:
          this.parmas.status = itemData.value
          if (!itemData.value) {
            delete this.parmas.status
          }
          break;
        // 类型
        case 3:
          this.parmas.type = itemData.value
          if (!itemData.value) {
            delete this.parmas.type
          }
          break;

        default:
          break;
      }
      this.parmas.page = 1
      this.pageNo = 1
      this.getRecommendList(this.parmas)
    },
    /**
     * 回复
     * @param {*} e
     */
    handlerReply(e, index) {
      console.log("handlerReply", e);
      this.$miRouter.push({
        path: '/pages_dining_room/recommend/recommend_edit',
        query: {
          id: e.id,
          index: index,
          type: e.type
        }
      })
    },
    /**
     * 查看详情
     * @param {*} e
     */
    viewDetail(e, index) {
      console.log("viewDetail", e);
      // 存储到缓存
      cache.set(this.$common.KEY_RECOMMEND_ITEM_INFO, e)
      this.$miRouter.push({
        path: '/pages_dining_room/recommend/recommend_detail',
        query: {
          index: index
        }
      })
    },
    /**
     * 更新信息内容
     */
    updateMessage() {
      uni.$on(this.$common.MSG_REPLY_SUCCESS, data => {
        console.log("收到信息", data);
        var content = Reflect.has(data, 'content') ? data.content : ''
        var index = Reflect.has(data, 'index') ? data.index : -1
        // 有新回复更新页面
        if (content && content.length > 0 && index >= 0) {
          this.recommendList[index].is_reply = true
          this.recommendList[index].reply_content = content
          this.isShowReplyBtnLayout = false
          this.isShowReplyLayout = true
          // 商家回复
          if (this.recommendList[index].type === 'feedback') {
            this.recommendList[index].feedback_status = 'reply'
            this.recommendList[index].merchant_remark = content
          }
        }
      })
    },
    /**
     * 去除消息
     */
    removeMessage() {
      uni.$off(this.$common.MSG_REPLY_SUCCESS)
    },
    /**
     * 获取字典
     */
    async getDicList() {
      var [error, res] = await this.$to(getUserOrgsList(2))
      console.log("getDicList", res, error);
      if (res && !error) {
        this.menuDataList[1].dataList = deepClone(res)
        this.menuDataList[1].chooseItem = res[0].name
        this.parmas.org_ids = [res[0].id]
      }
      this.getRecommendList(this.parmas)
    },
    /**
     * 设置列表的类型
     */
    setListType(type, list) {
      if (Array.isArray(list) && list.length > 0) {
        var newList = list.map(item => {
          item.type = type
          if (item.type === 'evaluation') {
            item.evaluation_content_txt = item.evaluation_content && item.evaluation_content.length > 30 ? item.evaluation_content.substr(0, 30) + "..." : item.evaluation_content
          } else {
            item.remark_txt = item.remark && item.remark.length > 30 ? item.remark.substr(0, 30) + "..." : item.remark
          }
          return item
        })
        return newList
      }
      return []
    },
    /**
     * 获取评价类型
     */
    getEvaluationTxt(data) {
      if (data && data.type === 'evaluation') {
        return data.is_reply ? this.userData.head.repliedTxt : this.userData.head.noReplyTxt
      } else {
        return data.feedback_status_alias || ''
      }
    }
  }

}

</script>

<style lang="scss" scoped>

  .recommend_content{
    padding :20rpx;
    color: #1d1e20;
    height: calc(100vh - 168rpx);
    .recommend_content_item{
      width: 670rpx;
      background-color: #ffffff;
      border-radius: 12rpx;
      margin :20rpx auto;
      padding-bottom: 1rpx;
      .horizontal_cell_line{
      width: 610rpx;
      height: 1rpx;
      background-color: #eae9ed;
      margin:0 auto;
    }
    .recommend_content_rate{
      display: flex;
      flex-wrap: wrap;
      margin :0 30rpx 30rpx 30rpx;

      .recommend_content_rate_item{
        display: flex;
        width: 50%;
        &:nth-child(2){
          justify-content: flex-end;
        }
        &:nth-child(3){
          margin-top: 28rpx;

        }
        &:nth-child(4){
          margin-top: 28rpx;
          justify-content: flex-end;
        }
        .recommend_content_rate_title{
          font-size: 24rpx;
          margin-right:20rpx;

        }

      }

    }
    .recommend_content_reply{
      width: 610rpx;
      height: 102rpx;
      background-color: #f5f5f6;
      border-radius: 8rpx;
      margin :0 30rpx;
      padding:20rpx;
      .recommend_content_reply_txt{
        display:-webkit-box;
        -webkit-box-orient:vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        font-size: 24rpx;
        & span{
          color:#8f9295
        }
      }
    }
    .recommend_btns_content{
      display: flex;
      justify-content: flex-end;
      margin:  30rpx  20rpx;

    .recommend_btn_style{
      width: 120rpx;
      height: 48rpx;
      border-radius: 6rpx;
      font-size: 24rpx;
      margin: 0 10rpx;
      padding: 0 !important;
    }

    }

  }

}

</style>

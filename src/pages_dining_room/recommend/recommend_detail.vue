<template>
  <!--评价详情-->
  <view class="recommend_container">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar :title="titleTxt" placeholder :autoBack="true" :leftIconColor="color.navigation" leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"></u-navbar>
    <!--#endif-->
    <!--头像头部 -->
    <view class="recommend_head">
      <view class="recommend_head_title">
        <image :src="userData.base.headImg ? userData.base.headImg : imgPath.IMG_HEAD_DEFAULT" alt="头像" mode="scaleToFill">
        </image>
        <view class="recommend_head_name">{{ userData.base.type== 'feedback' ?  userData.base.person_name : userData.base.name }}</view>
      </view>
      <view class="recommend_head_time">{{ userData.base.create_time }}</view>
    </view>
    <!--内容 餐厅内容评价-->
    <view class="recommend_content">
      <view class="recommend_content_item">
        <!--评价-->
        <view v-if="userData.base.type == 'evaluation'">
          <!--评价头部-->
          <horizontalCellComponent :cellStyle="cellStyle" :titleStyle="titleStyle"
            :valueStyle="userData.base.is_reply ? valueStyle : valueNoReplyStyle" :titleTxt="userData.base.organization_name"
            :valueTxt="userData.base.is_reply ? userData.head.repliedTxt : userData.head.noReplyTxt" />
          <!--分割灰线-->
          <view class="horizontal_cell_line"></view>
          <!--订单号内容-->
          <horizontalCellComponent :cellStyle="cellStyle" :titleStyle="valueStyle" :valueStyle="valueSmallStyle"
            :titleTxt="userData.head.dishesTxt" :valueTxt="userData.head.allTxt" />
          <!--评价的星星层-->
          <view class="recommend_content_rate">
            <view class="recommend_content_rate_item" v-for="(item, index) in userData.base.evaluation_score" :key="index">
              <view class="recommend_content_rate_title">{{ item.field_name }}</view>
              <u-rate :count="userData.head.rateCount" v-model="item.score" readonly size="28rpx" inactiveColor="#e3e4e8"
                activeColor="#FFC45E" gutter="15"></u-rate>
            </view>
          </view>
        </view>
        <!--建议-->
        <view v-if="userData.base.type == 'feedback'" class="m-b-30">
          <!--评价头部-->
          <horizontalCellComponent :cellStyle="cellStyle" :titleStyle="titleStyle"
            :valueStyle="userData.base.feedback_status=='reply' ? valueStyle : valueNoReplyStyle" :titleTxt="userData.base.company_name"
            :valueTxt="userData.base.feedback_status_alias" />
          <!--分割灰线-->
          <view class="horizontal_cell_line"></view>
          <!--订单号内容-->
          <horizontalCellComponent :cellStyle="cellStyle" :titleStyle="valueStyle" :valueStyle="valueSmallStyle"
            :titleTxt="userData.head.replyType" :valueTxt="userData.base.feedback_type_alias" />
          <!--评价内容-->
          <view class="recommend_content_reply">
            <view class="recommend_content_reply_title">{{ userData.head.feedbackTxt + "：" }}</view>
            <textarea class="recommend_content_reply_txt" :value=" userData.base.remark " disabled
              auto-height></textarea>
          </view>
          <!--评价的星星层-->
          <view class="recommend_content_rate" v-if="userData.base.evaluation_score">
            <view class="recommend_content_rate_item" v-for="(item, index) in userData.base.evaluation_score" :key="index">
              <view class="recommend_content_rate_title">{{ item.field_name }}</view>
              <u-rate :count="userData.head.rateCount" v-model="item.score" readonly size="28rpx" inactiveColor="#e3e4e8"
                activeColor="#FFC45E" gutter="15"></u-rate>
            </view>
          </view>
        </view>
      </view>

    </view>
    <!--内容 菜品内容评价-->
    <view class="recommend_content">
      <view class="recommend_content_item" v-if="userData.base.type =='evaluation' ">
        <view>
          <view v-for="(evaItem, evaIndex) in userData.base.food_evaluation_score" :key="evaIndex">
            <!--评价头部-->
            <horizontalCellComponent :cellStyle="cellStyle" :titleStyle="titleStyle" :valueStyle="valueStyle"
              :titleTxt="showGoodsName(evaItem)" :isShowValue="isShowValue" />
            <!--分割灰线-->
            <view class="horizontal_cell_line"></view>
            <!--评价的星星层-->
            <view class="recommend_content_rate padding_top_30">
              <view class="recommend_content_rate_item" v-for="(rateItem, subIndex) in evaItem.evaluation_score"
                :key="subIndex">
                <view class="recommend_content_rate_title">{{ rateItem.field_name }}</view>
                <u-rate :count="userData.head.rateCount" v-model="rateItem.score" readonly size="28rpx"
                  inactiveColor="#e3e4e8" activeColor="#FFC45E" gutter="15"></u-rate>
              </view>
            </view>
          </view>
          <!--回复文字层-->
          <view :class="['recommend_content_reply', userData.base.food_evaluation_score ? '' : 'padding_top_30']">
            <view class="recommend_content_reply_title">{{ userData.head.evaluationTxt + "：" }}</view>
            <textarea class="recommend_content_reply_txt" :value=" userData.base.evaluation_content " disabled
              auto-height></textarea>
          </view>
          <!--回复图片层-->
          <view class="recommend_content_reply padding_top_30">
            <view class="recommend_content_reply_title">{{ userData.head.dishesPictureTxt + "：" }}</view>
            <view class="recommend_content_reply_pic">
              <view class="recommend_content_img" v-for="(item, index) in userData.base.evaluation_img_list" :key="index">
                <u-image :src="item" :lazy-load="true" radius="8rpx" width="90rpx" height="90rpx"></u-image>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!--商家回复层 -->
      <view class="recommend_content_item" v-if="isShowReplyLayout">
        <view class="recommend_content_reply p-t-20">
          <view class="recommend_content_reply_title">{{ userData.head.replyContentTxt + "：" }}</view>
          <textarea class="recommend_content_reply_txt" :value="userData.base.type== 'evaluation'? userData.base.reply_content:userData.base.merchant_remark" disabled
            auto-height></textarea>
        </view>
         <!--建议 回复图片层-->
        <view class="recommend_content_reply  p-t-20" v-if="userData.base.type== 'feedback' && userData.base.feedback_images.length >0">
            <view class="recommend_content_reply_title">{{ userData.head.feedbackPicTxt + "：" }}</view>
            <view class="recommend_content_reply_pic">
              <view class="recommend_content_img" v-for="(item, index) in userData.base.feedback_images" :key="index">
                <u-image :src="item" :lazy-load="true" radius="8rpx" width="90rpx" height="90rpx"></u-image>
              </view>
            </view>
          </view>
      </view>
    </view>
    <!--按钮层-->
    <view class="recommend_btns_content padding-bottom-h5 padding-bottom-wechat" v-if="isShowReplyBtnLayout">
      <u-button :customStyle="customStyleBtn" class="recommend_btn_style" :color="color.colorBtnOrange"
        @click="handlerReply()">{{ userData.head.btnViewReplyTxt }}</u-button>
    </view>
    <!--弹窗-->
    <customDialogComponent ref="replyDialog" dialogType="input" :customDialogTitle="userData.head.feedbackTxt"
      @handlerCustomConfirm="handlerReplyConfirm"></customDialogComponent>
  </view>
</template>

<script>
import horizontalCellComponent from "@/components/HorizontalCellComponent/HorizontalCellComponent.vue"
import customDialogComponent from "@/components/CustomDialogComponent/CustomDialogComponent.vue"

import { mapGetters } from 'vuex'
import cache from "@/utils/cache"
import { deepClone } from "../../utils/util"
export default {
  data() {
    return {
      imgPath: this.$imgPath,
      cellStyle: { height: "80rpx" },
      titleStyle: { 'font-size': "28rpx", color: '#1d1e20' },
      valueStyle: { 'font-size': "24rpx", color: '#8f9295' },
      valueSmallStyle: { 'font-size': "24rpx", color: '#1d1e20' },
      valueNoReplyStyle: { 'font-size': "24rpx", color: '#fd953c' },
      isShowReplyLayout: false, // 是否显示回复层
      isShowReplyBtnLayout: true, // 是否显示回复按钮
      customStyleBtn: {
        width: '670rpx',
        height: '70rpx',
        borderRadius: '8rpx',
        fontSize: '28rpx',
        padding: '0 !important'
      },
      userData: {
        head: {
          appraiseTxt: this.$t('page.recommend.appraise'),
          suggestTxt: this.$t('page.recommend.suggest'),
          btnViewDetailTxt: this.$t('page.user.view.detail'),
          btnViewReplyTxt: this.$t('page.recommend.reply'),
          repliedTxt: this.$t('page.recommend.replied'),
          noReplyTxt: this.$t('page.recommend.no.reply'),
          tasteTxt: this.$t('page.recommend.taste'),
          priceTxt: this.$t('page.recommend.price'),
          serviceTxt: this.$t('page.recommend.service'),
          hygienismTxt: this.$t('page.recommend.hygienism'),
          evaluationTxt: this.$t('page.recommend.evaluation.content'),
          feedbackTxt: this.$t('page.recommend.feedback.content'),
          feedbackPicTxt: this.$t('page.recommend.reply.pic'),
          dishesPictureTxt: this.$t('page.recommend.dishes.picture'),
          rateCount: 5,
          dishesTxt: this.$t('page.recommend.dishes') + "：",
          allTxt: this.$t('page.recommend.all'),
          replyContentTxt: this.$t('page.recommend.reply.content'),
          replyType: this.$t('page.recommend.reply.type')
        },
        base: {
          name: "",
          create_time: "",
          headImg: this.$imgPath.IMG_HEAD_DEFAULT,
          is_reply: false,
          goods: '脆皮烧鸭',
          evaluation_content: "",
          reply_content: '',
          evaluation_img_list: []
        }

      },
      titleTxt: this.$t('title.details'), // 标题名称
      isShowValue: false,
      index: -1

    }
  },
  computed: {
    ...mapGetters(['color'])
  },
  components: { horizontalCellComponent, customDialogComponent },
  /**
   * 页面加载
   */
  onLoad(e) {
    console.log("用户列表页面加载", e)
    // 初始化数据
    this.initData(e)
  },
  created() {
  },
  /**
   * 页面显示
   */
  onShow() {
    console.log("onShow");
    this.updateMessage()
  },
  /**
   * 页面挂载
   */
  mounted() {
    console.log('mounted');
    this.updateMessage()
  },
  /**
   * 页面销毁
   */
  destroyed() {
    console.log('destroyed');
    this.removeMessage()
  },
  /**
   * 页面销毁
   */
  onUnload() {
    console.log("onUnload");
    this.removeMessage()
  },
  methods: {
    /**
     * 初始化数据
     */
    initData(e) {
      // 设置标题名称
      this.$setNavBarTitle(this.titleTxt)
      // 从缓存里面取出数据
      var recommendInfo = cache.get(this.$common.KEY_RECOMMEND_ITEM_INFO) || {}
      console.log("recommendInfo", recommendInfo, e);
      if (!Array.isArray(recommendInfo.food_evaluation_score)) {
        recommendInfo.food_evaluation_score = null
      }
      if (!Array.isArray(recommendInfo.evaluation_score)) {
        recommendInfo.evaluation_score = null
      }
      if (!Array.isArray(recommendInfo.evaluation_img_list)) {
        recommendInfo.evaluation_img_list = null
      }
      this.userData.base = deepClone(recommendInfo)
      this.isShowReplyLayout = recommendInfo.type === 'feedback' ? recommendInfo.feedback_status === 'reply' : recommendInfo.is_reply
      this.isShowReplyBtnLayout = recommendInfo.type === 'feedback' ? recommendInfo.feedback_status === 'no_reply' : !recommendInfo.is_reply
      // #ifdef H5
      this.index = this.$route.query.index ? this.$route.query.index : -1
      // #endif
      // #ifdef MP-WEIXIN || MP-ALIPAY
      this.index = e.index ? e.index : -1
      // #endif
      console.log("this.index", this.index);
    },
    /**
     * 回复
     * @param {*} e
     */
    handlerReply() {
      console.log("handlerReply");
      // 弹窗
      // if (this.$refs.replyDialog && typeof (this.$refs.replyDialog.showCustomDialog()) === 'function') {
      //   this.$refs.replyDialog.showCustomDialog()
      // }
      this.$miRouter.push({
        path: '/pages_dining_room/recommend/recommend_edit',
        query: {
          id: this.userData.base.id,
          type: this.userData.base.type,
          index: this.index
        }
      })
    },
    /**
     * 更新信息内容
     */
    updateMessage() {
      uni.$on(this.$common.MSG_REPLY_SUCCESS, data => {
        console.log("收到信息", data);
        var content = data.content ? data.content : ''
        // 有新回复更新页面
        if (content && content.length > 0) {
          this.userData.base.reply_content = content
          this.isShowReplyBtnLayout = false
          this.isShowReplyLayout = true
          // 评价状态设置已回复
          this.userData.base.is_reply = true
          if (this.userData.base.type === 'feedback') {
            this.userData.base.feedback_status = 'reply'
            this.userData.base.feedback_status_alias = '已回复'
            this.userData.base.merchant_remark = content
          }
          cache.set(this.$common.KEY_RECOMMEND_ITEM_INFO, this.userData.base)
        }
      })
    },
    /**
     * 去除消息
     */
    removeMessage() {
      uni.$off(this.$common.MSG_REPLY_SUCCESS)
    },
    /**
     * 显示菜品名字
     */
    showGoodsName(data) {
      console.log("showGoodsName", data);
      if (data && Reflect.has(data, "food_name")) {
        return this.userData.head.dishesTxt + data.food_name
      } else {
        return ''
      }
    }
  }

}

</script>

<style lang="scss" scoped>
$padding_30: 30rpx;

.recommend_container {
  padding-bottom: 110rpx;

  .recommend_head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 30rpx 40rpx;

    .recommend_head_title {
      display: flex;
      justify-content: center;

      & image {
        width: 90rpx;
        height: 90rpx;
        border-radius: 15rpx;
      }

      .recommend_head_name {
        font-size: 36rpx;
        color: #1d1e20;
        line-height: 42rpx;
        text-align: center;
        margin: 0 22rpx;
        align-self: center;
      }
    }

    .recommend_head_time {
      font-size: 24rpx;
      color: #8f9295;
    }

  }

  .recommend_content {
    padding: 0 20rpx;
    color: #1d1e20;

    .recommend_content_item {
      width: 670rpx;
      background-color: #ffffff;
      border-radius: 12rpx;
      margin: 20rpx auto;
      padding-bottom: 20rpx;
      padding-top: 20rpx;

      .horizontal_cell_line {
        width: 610rpx;
        height: 1rpx;
        background-color: #eae9ed;
        margin: 0 auto;
      }

      .recommend_content_rate {
        display: flex;
        flex-wrap: wrap;
        margin: 0 30rpx 0rpx 30rpx;

        .recommend_content_rate_item {
          display: flex;
          width: 50%;

          &:nth-child(2) {
            justify-content: flex-end;
          }

          &:nth-child(3) {
            margin-top: 28rpx;

          }

          &:nth-child(4) {
            margin-top: 28rpx;
            justify-content: flex-end;
          }

          .recommend_content_rate_title {
            font-size: 24rpx;
            margin-right: 20rpx;

          }

        }

      }

      .recommend_content_reply {
        width: 610rpx;
        margin: 0 30rpx;
        display: flex;

        .recommend_content_reply_title {
          width: 160rpx;
          font-size: 24rpx;
          color: #8f9295;
        }

        .recommend_content_reply_txt {
          width: 100%;
          font-size: 24rpx;
          color: #1d1e20;
          text-align: right;
        }

        .recommend_content_reply_pic {
          width: 100%;
          display: flex;
          justify-content: flex-end;
          flex-wrap: wrap;

          .recommend_content_img {
            margin-left: 20rpx;
            margin-bottom: 20rpx;
          }
        }

      }

      .padding_top_30 {
        padding-top: $padding_30;
      }

      .padding_bottom_30 {
        padding-bottom: $padding_30;
      }

    }

  }

  .recommend_btns_content {
    position: fixed;
    height: 108rpx;
    background: #ffffff;
    padding: 20rpx 40rpx;
    left: 0;

    .recommend_btn_style {
      width: 670rpx;
      height: 70rpx;
      border-radius: 8rpx;
      font-size: 28rpx;
      padding: 0 !important;
    }

  }
}
/* #ifdef H5 */
.padding-bottom-h5 {
  bottom: 0;
}
/* #endif */
/* #ifdef MP-WEIXIN || MP-ALIPAY */
.padding-bottom-wechat {
  bottom: 40rpx;
}
/* #endif */
</style>

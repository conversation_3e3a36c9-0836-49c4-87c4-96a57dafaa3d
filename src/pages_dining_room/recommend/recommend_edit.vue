<template>
  <!--回复编辑页面-->
  <view class="recommend_edit_container">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar :title="$t('back')" placeholder :autoBack="true" :leftIconColor="color.navigation" leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"></u-navbar>
    <!--#endif-->
    <!--回复输入层-->
    <view class="recommend_edit_content">
      <view class="recommend_edit_content_title">
        {{ replyContentTxt }}
      </view>
      <view class="recommend_edit_txtarea">
        <textarea class="recommend_edit_reply_txt" v-model="replyContent" auto-height maxlength="150"
          @input="inputChange"></textarea>
      </view>
    </view>
    <!--回复按钮层-->
    <!--按钮层-->
    <view class="recommend_btns_content padding-bottom-h5 padding-bottom-wechat">
      <u-button :customStyle="customStyleBtn" class="recommend_btn_style" :color="color.colorBtnOrange"
        @click="handlerReply()">{{ btnViewReplyTxt }}</u-button>
    </view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
import { apiBackgroundOperationManagementOrderEvaluationReplyEvaluation, apiBackgroundFeedbackFeedbackRecordModify } from '@/api/appraise'
export default {
  data() {
    return {
      btnViewReplyTxt: this.$t('page.recommend.confirm.reply'),
      replyContentTxt: this.$t('page.recommend.reply.content'),
      replyContent: '',
      index: -1,
      customStyleBtn: {
        width: '670rpx',
        height: '70rpx',
        borderRadius: '8rpx',
        fontSize: '28rpx',
        padding: '0 !important'
      },
      id: '', // 记录回复的ID
      type: 'recommend'// 回复的类型
    }
  },
  computed: {
    ...mapGetters(['color'])
  },
  /**
   * 页面加载
   */
  onLoad(e) {
    console.log("用户列表页面加载", e)
    // #ifdef MP-WEIXIN || MP-ALIPAY
    this.initData(e)
    // #endif
  },
  created() {
    // #ifdef H5
    this.initData()
    // #endif
  },
  methods: {
    /**
     * 初始化数据
     */
    initData(e) {
      var queryData
      // #ifdef H5
      queryData = this.$route.query
      // #endif
      // #ifdef MP-WEIXIN || MP-ALIPAY
      queryData = e || ''
      // #endif
      if (Reflect.has(queryData, 'index')) {
        this.index = queryData.index
        console.log("index", this.index);
      }
      if (Reflect.has(queryData, 'id')) {
        this.id = queryData.id
      }
      if (Reflect.has(queryData, 'type')) {
        this.type = queryData.type
      }
      console.log("queryData", queryData);
    },
    /**
     * 回复
     */
    async handlerReply() {
      var content = this.replyContent
      var result = []
      if (this.replyContent && this.replyContent.length > 0) {
        // 获取回复内容传回去
        var params = {
          id: this.id,
          reply_content: content
        }
        // 如果是评价
        if (this.type === "evaluation") {
          params.reply_content = content
          result = await this.$to(apiBackgroundOperationManagementOrderEvaluationReplyEvaluation(params))
        } else {
          params.merchant_remark = content
          params.feedback_status = 'reply'
          result = await this.$to(apiBackgroundFeedbackFeedbackRecordModify(params))
        }
        if (result[0]) {
          this.$toast({ title: '回复失败！' + result[0].message })
          return
        }
        if (result[1] && result[1].code === 0) {
          this.$toast({ title: '回复成功！' })
          uni.$emit(this.$common.MSG_REPLY_SUCCESS, { content: content, index: this.index })
          this.$miRouter.back()
        } else {
          this.$toast({ title: result[1].msg })
        }
      } else {
        this.$toast({ title: '亲，请给顾客一个回复吧！' })
      }
    },
    /**
     * 输入改变
     * @param {内容} e
     */
    inputChange(e) {
      if (e.detail.cursor >= 150) {
        this.$toast({ title: "亲，最多只能输入140个字喔！" })
      }
    }

  }

}
</script>

<style lang="scss" scoped>
.recommend_edit_container {

  .recommend_edit_content {
    width: 670rpx;
    min-height: 438rpx;
    background-color: #ffffff;
    border-radius: 12px;
    margin: 40rpx;
    padding-bottom: 10rpx;

    .recommend_edit_content_title {
      display: block;
      font-size: 28rpx;
      color: #8f9295;
      padding: 30rpx 30rpx 0;
    }

    .recommend_edit_txtarea {
      width: 610rpx;
      min-height: 330rpx;
      background-color: #f8fafc;
      border-radius: 8rpx;
      border: solid 1px #e4e4e4;
      margin: 20rpx 30rpx 30rpx 30rpx;

      .recommend_edit_reply_txt {
        width: 566rpx;
        min-height: 330rpx;
        font-size: 28rpx;
        color: #1d1e20;
        line-height: 42rpx;
        padding: 20rpx;

      }
    }

  }

  .recommend_btns_content {
    position: fixed;
    height: 108rpx;
    background: #ffffff;
    padding: 20rpx 40rpx;
    left: 0;

    .recommend_btn_style {
      width: 670rpx;
      height: 70rpx;
      border-radius: 8rpx;
      font-size: 28rpx;
      padding: 0 !important;
    }

  }

}
/* #ifdef H5 */
.padding-bottom-h5 {
  bottom: 0;
}
/* #endif */
/* #ifdef MP-WEIXIN || MP-ALIPAY */
.padding-bottom-wechat {
  bottom: 40rpx;
}
/* #endif */
</style>

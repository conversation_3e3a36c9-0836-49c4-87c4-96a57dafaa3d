<template>
  <u-overlay :show="isShowDialog" catchtouchmove="true">
    <view class="add-good-pic-container" @tap.stop>
      <view class="custom_dialog_item">
        <view class="title-txt p-t-40 p-b-40">{{ $t('tip.prompt') }}</view>
        <view class="flex row-between m-l-40 m-r-40">
          <view class="color-gray">{{ goodsName }}</view>
          <view class="color-green" v-if="imagesList.length < 6" @click="handlerAddPic">{{
            $t('announcement.Upload.image') }} > </view>
        </view>
        <!--图片组-->
        <view class="images m-t-10 m-l-40" v-if="imagesList && imagesList.length > 0">
          <view :class="['img-tag', subIndex % 3 > 0 ? 'm-l-10' : '', subIndex > 2 ? 'm-t-10' : '']"
            v-for="(sub, subIndex) in imagesList" :key="subIndex" @click.stop="handlerShowImg(imagesList, subIndex)">
            <image :src="sub" mode="aspectFill"></image>
            <view class="top-delete" @click.stop="handlerDeleteImg(subIndex)"><u-icon name="close" color="#8f9295"
                size="36"></u-icon>
            </view>
          </view>
        </view>
        <view class="color-gray m-l-40 m-t-40 m-b-20">{{ $t('page.invitation.remarks') }}：</view>
        <u-textarea class="custom_dialog_content_area " v-if='isTxtArea' v-model="customInputTxt"
          :placeholder="customInputTxtHolder" count ref="textarea" :maxlength="maxLength" :height="90"
          @change="handlerInputChange" confirmType="done"></u-textarea>
        <view class="horizontal_cell_line"></view>
        <view class="custom-dialog-btns">
          <u-button :customStyle="customStyleBtn" plain :class="['custom-dialog-btn-style-btns', 'borderRadiusLeft']"
            :color="color.colorBtnGray" @click="handlerCustomCancel()">{{ cancelBtnTxt }}</u-button>
          <view class="vertical_cell_line"></view>
          <u-button plain :customStyle="customStyleBtn" :class="['custom-dialog-btn-style-btns', 'borderRadiusRight']"
            :color="color.colorBtnOrange" @click="handlerCustomConfirm()">{{ confirmBtnTxt }}</u-button>
        </view>
      </view>
      <!--图片预览-->
      <image-preview ref="imgDialogPreview"></image-preview>
    </view>
  </u-overlay>
</template>

<script>
import { mapGetters } from 'vuex'
import { setUploadPic } from '../utils/util'
import Cache from '@/utils/cache';
import { deepClone } from '@/utils/util'
import ImagePreview from '@/components/ImagePreview/ImagePreview.vue';

export default {
  props: {
    confirmBtnTxt: { // 按钮确认名字
      type: String,
      default: '确认'
    },
    cancelBtnTxt: { // 按钮取消名字
      type: String,
      default: "取消"
    },
    remark: {
      type: String,
      default: ""
    }
  },
  name: "AddGoodPicDialog",
  components: {
    ImagePreview
  },
  data() {
    return {
      isShowDialog: false, // 是否显示Diglog
      customStyleBtn: {
        flex: 1,
        width: '100%',
        height: '90rpx',
        fontSize: '36rpx',
        border: 'none !important',
        padding: '0 !important',
        borderRadius: ' 0 0 20rpx 20rpx'
      },
      isTxtArea: true,
      customInputTxt: this.remark,
      customInputTxtHolder: this.$t('tip.please.enter.remark'),
      maxLength: 50,
      currentIndex: -1,
      imagesList: [], // 图片列表
      goodsName: '', // 商品名称
      goodsId: -1 // 商品Id
    };
  },
  computed: {
    ...mapGetters(['color'])
  },

  methods: {
    /**
     * 显示弹窗
     */
    showDialog() {
      console.log("showAddGoodDialog");
      this.isShowDialog = true
    },
    /**
     * 隐藏弹窗
     */
    hideDialog() {
      console.log("hideAddGoodDialog");
      this.isShowDialog = false
    },
    /**
     * 确定按钮
     */
    handlerCustomConfirm() {
      let info = Cache.get(this.$common.KEY_INVENTORY_INFO) || {}
      let goodsList = info.ingredient_data || []
      if (goodsList && goodsList.length > 0) {
        goodsList.forEach(item => {
          if (item.materials === this.goodsId) {
            item.img_json = deepClone(this.imagesList)
            item.remark = this.customInputTxt
            item.status = '不合格'
          }
        })
        info.ingredient_data = goodsList
      }
      console.log("handlerCustomConfirm", this.goodsList);
      Cache.set(this.$common.KEY_INVENTORY_INFO, info)
      this.isShowDialog = false
      this.$emit("handlerConfirm", true)
    },
    /*
     * 取消按钮
     */
    handlerCustomCancel() {
      this.isShowDialog = false
      this.$emit("handlerCancel", true)
    },
    // 输入监听
    handlerInputChange(value) {
      console.log("handlerInputChange", value);
    },
    // 设置当前数据
    setChooseItem(row, index) {
      console.log("setChooseItem", row, index);
      this.currentIndex = index
      this.imagesList = deepClone(row.img_json || [])
      this.goodsName = row.materials_name
      this.goodsId = row.materials
      this.customInputTxt = row.remark
    },
    // 添加照片
    async handlerAddPic() {
      let [err, res] = await this.$to(setUploadPic(6 - this.imagesList.length, 'voucher', this))
      if (err) {
        return this.$u.toast('上传照片失败')
      }
      if (res) {
        console.log("resultUrl", res);
        this.imagesList = this.imagesList.concat(res)
      }
    },
    // 展示图片
    handlerShowImg(imgList, index) {
      if (this.$refs.imgDialogPreview) {
        this.$refs.imgDialogPreview.setImgList(imgList, index)
        this.$refs.imgDialogPreview.showDialog()
      }
    },
    // 删除图片
    handlerDeleteImg(index) {
      var that = this
      this.$confirm(
        {
          dialogTypeValue: "content", // 弹窗类型
          titleTxt: that.$t('tip.prompt'), // 弹窗标题
          contentTxt: "确定去掉该图片吗？",
          isShowDialog: true, // 是否显示弹窗
          cancelCallBack: function () {
            console.log("点击取消");
          },
          confirmCallBack: function () {
            console.log("confirmCallback", index, that.imagesList);
            that.imagesList.splice(index, 1)
          }
        }, that)
    }
  }
}
</script>

<style lang='scss' scoped>
.add-good-pic-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.title-txt {
  font-size: 36rpx;
  color: #1d1e20;
  text-align: center;
}

.custom_dialog_item {
  width: 687rpx;
  min-height: 100rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
}

.custom_dialog_content_area {
  width: 620rpx;
  min-height: 80rpx;
  margin: auto;
}

.horizontal_cell_line {
  width: 100%;
  height: 1px;
  background-color: #e5e5e5;
  margin-top: 45rpx;
}

.custom-dialog-btns {
  display: flex;
  align-items: center;
  flex-grow: 2;

  .custom-dialog-btn-style-btns {
    flex: 1;
    width: 100%;
    height: 90rpx;
    font-size: 36rpx;
    border: none !important;
    padding: 0 !important;
    border-radius: 0 0 20rpx 20rpx;
  }

  .borderRadiusLeft {
    border-radius: 0 0 0 20rpx;
  }

  .borderRadiusRight {
    border-radius: 0 0 20rpx 0;
  }

  .vertical_cell_line {
    width: 1px;
    height: 90rpx;
    background-color: #e5e5e5;
  }
}

.color-gray {
  color: #8f9295;
}

.color-green {
  color: #51d854;
}

.images {
  width: 600rpx;
  display: inline-flex;
  flex-wrap: wrap;
  justify-content: flex-start;

  .img-tag {
    width: 190rpx;
    height: 190rpx;
    border-radius: 6rpx;
    position: relative;

    image {
      width: 100%;
      height: 100%;
    }

    .top-delete {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
}
</style>

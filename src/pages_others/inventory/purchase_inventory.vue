<template>
  <view class="purchase-container">
    <!--标题栏-->
    <!--#ifdef MP-WEIXIN || H5-->
    <u-navbar :title="$t('title.inventory.take.delivery')" placeholder :autoBack="true" :leftIconColor="color.navigation"
      leftIconSize="37rpx" :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"></u-navbar>
    <!--#endif-->
    <view class="purchase-tag" >
      <view class="menu-tag" @click="goDetail">
        <img :src="imgPath.IMG_OTHER_MENU_GOOD_ORANGE" class="menu-img" />
        <view class="menu-text">{{ $t('title.inventory.take.delivery') }}</view>
      </view>
      <view class="menu-tag" @click="goDetailWarehouse">
        <img :src="imgPath.IMG_INDEX_07" class="menu-img" />
        <view class="menu-text">{{ $t('page.inventory.material.warehouse') }}</view>
      </view>
      <view class="menu-tag" @click="goDetailOther">
        <img :src="imgPath.IMG_INDEX_01" class="menu-img" />
        <view class="menu-text">{{ $t('page.inventory.material.selection') }}</view>
      </view>
    </view>
  </view>
</template>
<script>
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      imgPath: this.$imgPath
    }
  },
  computed: {
    ...mapGetters(['color'])
  },
  methods: {
    goDetail() {
      this.$miRouter.push({ path: '/pages_others/inventory/purchase_inventory_order' })
    },
    goDetailWarehouse() {
      this.$miRouter.push({ path: '/pages_others/warehouse/material_warehouse' })
    },
    goDetailOther() {
      this.$miRouter.push({ path: '/pages_others/warehouse/web' })
      // window.location.href = 'https://crmeb.base.packertec.com/pages/goods_cate/goods_cate'
    }
  }
}
</script>
<style lang="scss" scoped>
.purchase-container {
  padding: 20rpx;
  .purchase-tag {
    width: 670rpx;
    background-color: #ffffff;
    border-radius: 20rpx;
    margin: 40rpx auto;
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    cursor: pointer;
    .menu-tag {
      display: inline-flex;
      flex-direction: column;
      align-items: center;
      width: 200rpx;
      height: 240rpx;
      padding-top: 40rpx;
    }
    .menu-text {
      margin-top: 20rpx;
      font-size: 30rpx;
      color: #1d1e20;
      text-align: center;
    }
    .menu-img {
      width: 100rpx;
      height: 100rpx;
      margin: 0 auto;
    }
  }
}
</style>

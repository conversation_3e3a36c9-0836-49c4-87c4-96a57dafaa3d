<template>
  <view class="purchase-camera">
    <!--标题栏-->
    <!--#ifdef MP-WEIXIN || H5-->
    <u-navbar :title="$t('title.details')" placeholder :autoBack="true" :leftIconColor="color.navigation"
      leftIconSize="37rpx" :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"></u-navbar>
    <!--#endif-->
    <!-- #ifdef H5-->
    <!--拍照区域-->
    <view class="camera-box">
      <view class="middle-box">
        <image :src="imgPhoto" mode="scaleToFill" class="img-tag"></image>
        <view class="tig-tag" v-if="errorTip"></view>
        <view :class="['tig-tip', errorTip && errorTip.length > 9 ? 'bottom-10' : 'bottom-40', errorTip && errorTip.length > 18 ? 'small-font':'']" v-if="errorTip">
          <view class="txt"> {{ errorTip }}</view>
        </view>
      </view>
      <view class="tip-txt">{{'请正视摄像头，保持五官清晰' }}</view>
    </view>
    <!--底部按钮-->
    <view class="btn-layout">
      <!-- <u-button :customStyle="customStyleBtn" class="approval_btn_style" :disabled="isBtnDisable"
        :color="color.colorBtnOrange" @click="handlerRefuse()" v-if="!imgPhoto">拍照</u-button> -->
      <u-button :customStyle="customStyleBtn" class="approval_btn_style" :disabled="isBtnDisable"
        :color="color.colorBtnBlack" @click="handlerRetry" v-if="errorTip">重新拍照</u-button>
      <view class="flex" v-if="imgPhoto && !errorTip">
        <u-button :customStyle="customStyleBtnSamll" class="approval_btn_style small" :disabled="isBtnDisable"
          :color="color.colorBtnBlack" @click="handlerRetry">重新拍照</u-button>
        <u-button :customStyle="customStyleBtnSamll" class="approval_btn_style small" :disabled="isBtnDisable"
          :color="color.colorBtnOrange" @click="handlerConfirm">确认</u-button>
      </view>
      <!-- #endif-->
      <!-- #ifdef MP-WEIXIN  -->
      <u-button :customStyle="customStyleBtnSamll" class="approval_btn_style small" :disabled="isBtnDisable"
      :color="color.colorBtnOrange" @click="handlerConfirm">确认</u-button>
      <!-- #endif  -->
    </view>
  </view>
</template>
<script>
import { mapGetters } from 'vuex'
import { setUploadPic } from './utils/util'
import Cache from '../../utils/cache';
import { apiMerchantMobileCommonUploadUserFacePost } from "@/api/user"

export default {
  data() {
    return {
      imgPath: this.$imgPath,
      imagesList: [],
      customStyleBtn: {
        width: '670rpx',
        height: '70rpx',
        borderRadius: '8rpx',
        fontSize: '28rpx',
        margin: '19rpx  40rpx'
      },
      customStyleBtnSamll: {
        width: '320rpx',
        height: '70rpx',
        borderRadius: '8rpx',
        fontSize: '28rpx',
        margin: '19rpx  40rpx'
      },
      isBtnDisable: false, // 按钮无法点击
      imgPhoto: '',
      errorTip: ''
    }
  },
  onLoad() {
    console.log("error length", this.errorTip.length)
    this.initData()
  },
  computed: {
    ...mapGetters(['color'])
  },
  methods: {
    initData() {
      this.imgPhoto = Cache.get(this.$common.KEY_UPLOAD_PERSON_PIC)
      console.log("this.imgPhoto", this.imgPhoto);
    },
    // 重新拍照
    async handlerRetry() {
      let [err, res] = await this.$to(setUploadPic(1, 'penson', this))
      if (err) {
        this.errorTip = err.message || '上传照片失败'
        return this.$u.toast('上传照片失败')
      }
      if (res) {
        Cache.set(this.$common.KEY_UPLOAD_PERSON_PIC, res[0])
        this.imgPhoto = res[0]
        this.handlerConfirm()
      }
    },
    // 确认识别
    async handlerConfirm() {
      this.isBtnDisable = true
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      var userInfo = Cache.get(this.$common.KEY_USER_INFO) || {}
      console.log("userInfo", userInfo);
      let [err, res] = await this.$to(apiMerchantMobileCommonUploadUserFacePost({
        face_url: this.imgPhoto,
        company_id: userInfo.company_id
      }))
      uni.hideLoading()
      this.isBtnDisable = false
      if (err) {
        this.errorTip = err.message || '识别失败'
        return this.$u.toast(this.errorTip)
      }
      if (res && res.code === 0) {
        this.$u.toast('识别成功')
        let data = res.data || {}
        let info = Cache.get(this.$common.KEY_INVENTORY_INFO) || {}
        let personList = info.personList || []
        // 这里要判断是不是人员重复添加
        if (personList && personList.some(item => item.account_id === data.account_id)) {
          return this.$u.toast('人员重复添加')
        }
        personList.push({
          name: data.name,
          account_id: data.account_id,
          mobile: data.mobile,
          username: data.username,
          role_name: data.role_name,
          face_url: this.imgPhoto
        })
        info.personList = personList
        Cache.set(this.$common.KEY_INVENTORY_INFO, info)
        this.$miRouter.back()
      } else {
        this.errorTip = res.msg || '识别失败'
        this.$u.toast(this.errorTip)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.purchase-camera {
  position: relative;
  height: 100vh;

  .camera-box {
    width: 670rpx;
    height: 1100rpx;
    border-radius: 12px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 20rpx auto;

    .middle-box {
      width: 532rpx;
      height: 532rpx;
      border-radius: 266rpx;
      margin-top: 121rpx;
      background-color: #000000;
      position: relative;

      .img-tag {
        width: 532rpx;
        height: 532rpx;
        border-radius: 266rpx;
      }

      .tig-tag {
        position: absolute;
        bottom: 0;
        width: 532rpx;
        height: 532rpx;
        border-radius: 266rpx;
        clip-path: inset(80% 0 0 0);
        background-color: #E56049;
      }

      .tig-tip {
        position: absolute;
        left: 0;
        right: 0;

        .txt {
          margin: 0 auto;
          width: 300rpx;
          color: #fff;
          text-align: center;
          font-size: 32rpx;
          overflow: hidden;
          word-break: break-all;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
        }
      }
      .small-font {
        font-size: 20rpx;
      }

      .bottom-40 {
        bottom: 40rpx;
      }

      .bottom-10 {
        bottom: 10rpx;
      }
    }

    .tip-txt {
      font-size: 40rpx;
      color: #1d1e20;
      margin-top: 68rpx;
    }
  }

  .btn-layout {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    min-height: 118rpx;
    background-color: #fff;

    .approval_btn_style {
      width: 670rpx;
      height: 70rpx;
      border-radius: 8rpx;
      font-size: 28rpx;
      margin: 19rpx 40rpx;
    }

    .small {
      width: 320rpx !important;
    }
  }
}
</style>

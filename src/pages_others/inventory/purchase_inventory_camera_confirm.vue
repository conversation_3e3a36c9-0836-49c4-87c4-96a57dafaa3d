<template>
  <view class="purchase-detail-container">
    <!--标题栏-->
    <!--#ifdef MP-WEIXIN || H5-->
    <u-navbar :title="$t('title.details')" placeholder :autoBack="true" :leftIconColor="color.navigation"
      leftIconSize="37rpx" :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"></u-navbar>
    <!--#endif-->
    <view class="title m-b-20 m-l-40 m-t-20 lg">
      PS24121315325211452
    </view>
    <!--状态栏-->
    <view class="status-tag">
      <image :src="imgPath.IMG_OTHER_ARROW_SUCCESS" class="status-img" />
      <view class="status-txt m-l-20">订单已发货</view>
    </view>
    <!--后货物资-->
    <view :class="['purchase-tag']" v-for="(item, index) in goodsList" :key="index">
      <view class="row flex row-between">
        <view class="left">货物名称：</view>
        <view class="right">大苹果</view>
      </view>
      <view class="row flex row-between m-t-20">
        <view class="left">单价：</view>
        <view class="right">¥10.00</view>
      </view>
      <view class="row flex row-between m-t-20">
        <view class="left">收货数量：</view>
        <view class="right">单价</view>
      </view>
      <view class="row flex row-between m-t-20">
        <view class="left">收货图片：</view>
        <view class="right">
          <view class="images" v-if="imagesList && imagesList.length > 0">
            <view :class="['img-tag', subIndex > 0 ? 'm-l-10' : '', subIndex > 4 ? 'm-t-10' : '']"
              v-for="(sub, subIndex) in imagesList" :key="subIndex">
              <image src="https://img.yzcdn.cn/vant/cat.jpeg" mode="aspectFill"></image>
            </view>
          </view>
          <view v-else>--</view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      imgPath: this.$imgPath,
      imagesList: [{}, {}, {}, {}, {}],
      goodsList: [{}, {}, {}]
    }
  },
  computed: {
    ...mapGetters(['color'])
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
.purchase-detail-container {
  .title {
    color: #8f9295;
  }

  .status-tag {
    width: 670rpx;
    height: 120rpx;
    margin: 20rpx auto;
    border-radius: 12rpx;
    border-top: 10rpx solid #51d854;
    background-color: #ffffff;
    display: flex;
    align-items: center;
    padding: 0 40rpx;

    .status-img {
      width: 42rpx;
      height: 42rpx;
    }

    .status-txt {
      font-size: 40rpx;
      color: #51d854;
    }
  }

  .purchase-tag {
    width: 670rpx;
    background-color: #ffffff;
    border-radius: 20rpx;
    margin: 40rpx auto;
    padding: 30rpx;
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    cursor: pointer;

    .row {
      width: 100%;
      font-size: 24rpx;

      .left {
        color: #8f9295;
      }

      .right {
        color: #1d1e20;

        .images {
          width: 470rpx;
          display: inline-flex;
          flex-wrap: wrap;
          justify-content: flex-end;

          .img-tag {
            width: 80rpx;
            height: 80rpx;
            border-radius: 6rpx;

            image {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
  }
}
</style>

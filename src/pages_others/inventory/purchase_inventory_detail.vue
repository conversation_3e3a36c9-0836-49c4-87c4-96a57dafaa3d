<template>
  <view class="purchase-detail-container">
    <!--标题栏-->
    <!--#ifdef MP-WEIXIN || H5-->
    <u-navbar :title="$t('title.details')" placeholder :autoBack="true" :leftIconColor="color.navigation"
      leftIconSize="37rpx" :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"></u-navbar>
    <!--#endif-->
    <view class="title m-b-20 m-l-40 m-t-20 lg">
      {{ orderInfo.trade_no }}
    </view>
    <!--状态栏-->
    <view class="status-tag">
      <image :src="imgPath.IMG_OTHER_ARROW_SUCCESS" class="status-img" />
      <view class="status-txt m-l-20">订单已收货</view>
    </view>
    <!--后货物资-->
    <view :class="['purchase-tag']" v-for="(item, index) in goodsList" :key="index">
      <view class="row flex row-between">
        <view class="left"></view>
        <view :class="[item.is_pass ? 'color-green' : 'color-gray']">{{ item.is_pass ? "合格" : "不合格" }}</view>
      </view>
      <view class="row flex row-between m-t-20">
        <view class="left">货物名称：</view>
        <view class="right">{{ item.materials_name }}</view>
      </view>
      <view class="row flex row-between m-t-20">
        <view class="left">单价：</view>
        <view class="right">¥{{ getPrice(item) }}</view>
      </view>
      <view class="row flex row-between m-t-20">
        <view class="left">规格：</view>
        <view class="right">{{ getSpecificationRecord(item) }}</view>
      </view>
      <view class="row flex row-between m-t-20">
        <view class="left">收货数量：</view>
        <view class="right">{{ item.receive_count }}</view>
      </view>
      <view class="row flex row-between m-t-20" v-if="!item.is_pass">
        <view class="left">图片依据：</view>
        <view class="right">
          <view class="images" v-if="item.img_json && item.img_json.length > 0">
            <view :class="['img-tag', subErrorIndex > 0 ? 'm-l-10' : '', subErrorIndex > 4 ? 'm-t-10' : '']"
              @click="handlerShowImg(item.img_json, subErrorIndex)" v-for="(subError, subErrorIndex) in item.img_json"
              :key="subErrorIndex">
              <image :src="subError" mode="aspectFill"></image>
            </view>
          </view>
          <view v-else>--</view>
        </view>
      </view>
      <view class="row flex row-between m-t-20" v-else>
        <view class="left">收货图片：</view>
        <view class="right">
          <view class="images" v-if="item.check_file && item.check_file.length > 0">
            <view :class="['img-tag', subIndex > 0 ? 'm-l-10' : '', subIndex > 4 ? 'm-t-10' : '']"
              @click="handlerShowImg(item.check_file, subIndex)" v-for="(sub, subIndex) in item.check_file"
              :key="subIndex">
              <image :src="sub" mode="aspectFill"></image>
            </view>
          </view>
          <view v-else>--</view>
        </view>
      </view>
    </view>
    <image-preview ref="imgPreview"></image-preview>
  </view>
</template>
<script>
import { mapGetters } from 'vuex'
import { apiMerchantMobileVendorReceivingNoteVendorReceivingInfoListPost } from '@/api/inventory'
import { deepClone, divide } from '../../utils/util';
import ImagePreview from '@/components/ImagePreview/ImagePreview.vue';

export default {
  data() {
    return {
      imgPath: this.$imgPath,
      imagesList: [],
      goodsList: [],
      orderInfo: {}
    }
  },
  components: {
    ImagePreview
  },
  computed: {
    ...mapGetters(['color'])
  },
  onLoad(e) {
    console.log("onLoad", e);
    let id = e.id || '';
    this.getDetailList(id)
  },
  methods: {
    getDetailList(currentId) {
      let params = {
        id: currentId || 1
      }
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      apiMerchantMobileVendorReceivingNoteVendorReceivingInfoListPost(params)
        .then(res => {
          if (res.code === 0) {
            uni.hideLoading()
            var data = res.data ? res.data : {}
            console.log("apiMerchantMobileVendorReceivingNoteVendorReceivingInfoListPost data", data);
            this.orderInfo = deepClone(data)
            let goods = data.ingredient_data || []
            this.goodsList = deepClone(goods)
            // 如果是第一页并且没有数据要展示没有数据层
            this.isShowEmptyView = this.goodsList.length === 0
          } else {
            console.log("code !=0");
            this.$set(this, "orderList", [])
            uni.hideLoading()
            uni.$u.toast(res.msg || "")
          }
        })
        .catch(err => {
          uni.hideLoading()
          this.$set(this, "orderList", [])
          console.log("err list", err);
          if (err.message) {
            uni.$u.toast(err.message)
          }
        })
    },
    // 获取价格
    getPrice(row) {
      let price = row.unit_price || 0
      return divide(price)
    },
    // 展示图片
    handlerShowImg(imgList, index) {
      if (this.$refs.imgPreview) {
        this.$refs.imgPreview.setImgList(imgList, index)
        this.$refs.imgPreview.showDialog()
      }
    },
    // 获取规格
    getSpecificationRecord(row) {
      if (row && Reflect.has(row, 'specification_record')) {
        const specificationRecord = row.specification_record || ''
        return specificationRecord || '--'
      }
      return '--'
    }
  }
}
</script>
<style lang="scss" scoped>
.purchase-detail-container {
  .title {
    color: #8f9295;
  }

  .status-tag {
    width: 670rpx;
    height: 120rpx;
    margin: 20rpx auto;
    border-radius: 12rpx;
    border-top: 10rpx solid #51d854;
    background-color: #ffffff;
    display: flex;
    align-items: center;
    padding: 0 40rpx;

    .status-img {
      width: 42rpx;
      height: 42rpx;
    }

    .status-txt {
      font-size: 40rpx;
      color: #51d854;
    }
  }

  .purchase-tag {
    width: 670rpx;
    background-color: #ffffff;
    border-radius: 20rpx;
    margin: 40rpx auto;
    padding: 30rpx;
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    cursor: pointer;

    .row {
      width: 100%;
      font-size: 24rpx;

      .left {
        color: #8f9295;
      }

      .right {
        color: #1d1e20;

        .images {
          width: 470rpx;
          display: inline-flex;
          flex-wrap: wrap;
          justify-content: flex-end;

          .img-tag {
            width: 80rpx;
            height: 80rpx;
            border-radius: 6rpx;

            image {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
  }

  .color-gray {
    color: #8f9295;
  }

  .color-green {
    color: #51d854;
  }
}
</style>

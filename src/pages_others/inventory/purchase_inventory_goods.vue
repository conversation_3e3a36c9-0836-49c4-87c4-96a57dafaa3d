<template>
  <!--货物填写信息，上传图片-->
  <view class="purchase-goods-container">
    <!--标题栏-->
    <!--#ifdef MP-WEIXIN || H5-->
    <u-navbar :title="$t('title.inventory.take.delivery')" placeholder :autoBack="true"
      :leftIconColor="color.navigation" leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"></u-navbar>
    <!--#endif-->
    <!--搜索-->
    <view class="search-layout">
      <view class="search-tag">
        <view class="flex col-center">
          <view @click="handlerChooseDate">{{ chooseDate }}</view>
          <u-icon name="arrow-down" class="m-l-10" @click="handlerChooseDate"></u-icon>
          <view class="line-gray m-l-15 m-r-15"></view>
          <u-icon name="search"></u-icon>
          <view class="search-input"><u-input v-model="searchContent" border="none"
              :placeholder="$t('tip.goods.please.enter.purchase.name')" autocomplete="off"
              @input="handlerSearchChange"></u-input></view>
        </view>
      </view>
      <filter-drop-down id="drowDown" ref="drowDown" @showDropDownChange="showDropDownChange"
        @handlerDropDownClick="handlerDropDownClick" :dataList="chooseDataList" :chooseTarget="chooseDate">
      </filter-drop-down>
    </view>
    <view class="title m-b-20 m-l-40 m-t-140 lg">
      {{ orderInfo.trade_no }}
    </view>
    <view class="list-tag">
      <view class="good-tag" v-for="(item, index) in goodsList" :key="index">
        <view class="flex row-between col-center m-b-30">
          <view class="good-title">{{ item.materials_name }} </view>
          <view :class="[item.status === '不合格' ? 'color-gray' : 'color-green']"> <span class="doit"
              v-if="item.status === '不合格'"></span> {{ item.status }}</view>
        </view>
        <view class="good-content">
          <view class="left flex flex-col col-center row-center">
            <view class="tip-middle">¥ {{ formatPrice(item.unit_price) }}</view>
            <view class="color-gray">{{ $t('page.inventory.goods.price') }}</view>
          </view>
          <view class="line-middle"></view>
          <view class="left flex flex-col col-center row-center">
            <view class="tip-middle">{{ formatSpecifications(item) }}</view>
            <view class="color-gray">{{ $t('page.inventory.goods.specifications') }}</view>
          </view>
          <view class="line-middle"></view>
          <view class="right flex flex-col col-center row-center">
            <view class="input-number">
              <u-input type="text" v-model="item.receive_count" placeholder="请输入数量" border="none" fontSize="28rpx"
                @input="handlerInputChange($event, item)"></u-input>
            </view>
            <view class="color-gray m-t-5">{{ $t('page.inventory.goods.number') }}</view>
          </view>
        </view>
        <!--图片组-->
        <view class="images m-t-10" v-if="item.check_file && item.check_file.length > 0">
          <view :class="['img-tag', subIndex % 3 > 0 ? 'm-l-10' : '', subIndex > 2 ? 'm-t-10' : '']"
            v-for="(sub, subIndex) in item.check_file" @click.stop="handlerShowImg(item.check_file, subIndex)"
            :key="subIndex">
            <image :src="sub.url" mode="aspectFill"></image>
            <view class="top-delete" @click.stop="handlerDeleteImg(item.check_file, subIndex)" v-if="sub.isDelete"><u-icon name="close"
                color="#8f9295" size="36"></u-icon>
            </view>
          </view>
        </view>
        <view class="btn-layout-detail">
          <view>
            <u-button :customStyle="customStyleBtnUpload" class="btn-upload small" :disabled="isBtnDisable"
              :color="color.colorBtnBlack" @click="handlerUploadPic(item, index)">质检报告</u-button>
          </view>
          <view class="m-l-20">
            <u-button :customStyle="customStyleBtnSamll" class="btn-cancel small" :disabled="isBtnDisable"
              v-if="item.status !== '不合格'" :color="color.colorBtnOrange"
              @click="handlerRefuse(item, '不合格', index)">不合格</u-button>
          </view>
          <view class="m-l-20">
            <u-button :customStyle="customStyleBtnSamll" class="btn-cancel small" :disabled="isBtnDisable" :plain="true"
              v-if="item.status === '不合格'" :color="color.colorBtnOrange"
              @click="handlerRefuse(item, '合格', index)">撤回操作</u-button>
          </view>
        </view>
      </view>
    </view>
    <!-- 空白页 -->
    <empty-component :emptyContent="emptyContent" v-if="isShowEmptyView"></empty-component>
    <view class="btn-layout">
      <u-button :customStyle="customStyleBtn" class="approval_btn_style" :disabled="isBtnDisable"
        :color="color.colorBtnOrange" @click="handlerNext">{{ $t('index.Confirm') }}</u-button>
    </view>
    <!--添加弹窗-->
    <add-good-pic-dialog ref="addGoodDialogRef" @handlerConfirm="handlerConfirmGoodDialog"
      @handlerCancel="handlerCancelGoodDialog"></add-good-pic-dialog>
    <!--图片预览-->
    <image-preview ref="imgPreview"></image-preview>
  </view>
</template>
<script>
import { mapGetters } from 'vuex'
import FilterDropDown from '@/components/FilterDropdown/FilterDropdown.vue'
import EmptyComponent from "@/components/EmptyComponent/EmptyComponent"
import AddGoodPicDialog from "./components/AddGoodPicDialog.vue"
import Cache from '@/utils/cache';
import { deepClone, divide } from '@/utils/util'
import { positiveMoney } from '@/utils/validata';
// import { setUploadPic } from './utils/util'
import ImagePreview from '@/components/ImagePreview/ImagePreview.vue';

// import { getCardServiceCardUserList } from '@/api/user'

export default {

  components: {
    FilterDropDown,
    EmptyComponent,
    AddGoodPicDialog,
    ImagePreview
  },
  data() {
    return {
      imgPath: this.$imgPath,
      chooseDataList: [
        {
          name: '全部',
          id: 1
        },
        {
          name: '合格',
          id: 2
        },
        {
          name: '不合格',
          id: 3
        }
      ],
      chooseDate: '全部',
      chooseIndex: 0,
      searchContent: '',
      imagesList: [],
      goodsList: [],
      isShowEmptyView: false,
      emptyContent: this.$t('tip.no.search.content'), // 空数据提示
      params: { // 筛选入参
        page: 1,
        page_size: 10,
        is_self_org: false
      },
      customStyleBtnUpload: {
        width: '180rpx',
        height: '48rpx',
        borderRadius: '8rpx',
        fontSize: '24rpx',
        padding: '0'
      },
      customStyleBtnSamll: {
        width: '140rpx',
        height: '48rpx',
        borderRadius: '8rpx',
        fontSize: '24rpx',
        padding: '0'
      },
      customStyleBtn: {
        width: '670rpx',
        height: '70rpx',
        borderRadius: '8rpx',
        fontSize: '28rpx',
        margin: '19rpx  40rpx'
      },
      isBtnDisable: false,
      orderInfo: {}
    }
  },
  computed: {
    ...mapGetters(['color'])
  },
  onShow() {
    this.updateView()
  },
  methods: {
    // 显示下拉框
    handlerChooseDate() {
      if (this.$refs.drowDown) {
        this.$refs.drowDown.setChooseItem(this.chooseDate)
        this.idShowDrow = !this.$refs.drowDown.getDropDownStatus()
        this.$refs.drowDown.showDropDown(this.idShowDrow)
      }
    },
    /**
    * 下拉列表变化
    * @param {*} e
    */
    showDropDownChange(e) {
      if (!e) {
        this.currentIndex = -1
      }
      this.$emit('showDropDownStatusChange', e)
    },
    /**
     * 下拉项目点击
     */
    handlerDropDownClick(data) {
      console.log("data", data)
      this.chooseDate = data.name
      this.updateGoodsList('status', data.name)
    },
    //  显示上传图片弹窗
    async handlerUploadPic(item, index) {
      this.$miRouter.push({ path: '/pages_others/inventory/purchase_inventory_specifications?index=' + index })
      // let length = item.check_file ? item.check_file.length : 0
      // if (length >= 6) {
      //   return this.$u.toast("图片数量不能超过6张")
      // }
      // let [err, res] = await this.$to(setUploadPic(6 - length, 'goods', this))
      // if (err) {
      //   return this.$u.toast('上传照片失败')
      // }
      // if (res) {
      //   console.log("resultUrl", res);
      //   let checkFile = this.goodsList[index].check_file || []
      //   checkFile = checkFile.concat(res)
      //   this.$set(this.goodsList[index], 'check_file', deepClone(checkFile))
      //   let info = Cache.get(this.$common.KEY_INVENTORY_INFO) || {}
      //   let goodList = info.ingredient_data || []
      //   goodList = this.goodsList
      //   info.ingredient_data = deepClone(goodList)
      //   Cache.set(this.$common.KEY_INVENTORY_INFO, info)
      // }
    },
    // 确认上传图片
    handlerConfirmGoodDialog() {
      this.updateView()
    },
    // 取消上传图片
    handlerCancelGoodDialog() {
    },
    // 下一步
    handlerNext() {
      let flag = true
      console.log("this.goodsList", this.goodsList);
      if (this.goodsList) {
        for (let i = 0; i < this.goodsList.length; i++) {
          if (!this.goodsList[i].receive_count || !positiveMoney(this.goodsList[i].receive_count)) {
            flag = false
            console.log("this.goodList[i]", this.goodsList);
            this.$u.toast((this.goodsList[i].materials_name ? this.goodsList[i].materials_name : '') + ',请输入正确的数量')
            break
          }
        }
      }
      if (flag) {
        this.$miRouter.push({ path: '/pages_others/inventory/purchase_inventory_person' })
      }
    },
    // 更新视图
    updateView() {
      let info = Cache.get(this.$common.KEY_INVENTORY_INFO) || {}
      this.orderInfo = deepClone(info)
      let goodsList = info.ingredient_data || []
      console.log("updateView", info, goodsList);
      if (goodsList && goodsList.length > 0) {
        goodsList.forEach(item => {
          item.img_json = Reflect.has(item, 'img_json') ? item.img_json : []
          item.remark = Reflect.has(item, 'remark') ? item.remark : ''
          item.status = Reflect.has(item, 'status') ? item.status : '合格'
        })
        this.goodsList = deepClone(goodsList)
        info.ingredient_data = deepClone(goodsList)
        Cache.set(this.$common.KEY_INVENTORY_INFO, info)
      } else {
        this.isShowEmptyView = true
      }
    },
    // 不及格和撤回操作
    handlerRefuse(row, statusType, index) {
      console.log("handlerRefuse", row, statusType);
      if (statusType === '不合格') {
        if (this.$refs.addGoodDialogRef) {
          this.$refs.addGoodDialogRef.setChooseItem(row, index)
          this.$refs.addGoodDialogRef.showDialog()
        }
      } else {
        this.updateByType(row.id, 'status', statusType)
        row.status = statusType
      }
    },
    // 更新数据操作 id:要更新的标签的名字，  key:要更新的标签， value:要更新的值
    updateByType(id, key, value) {
      let info = Cache.get(this.$common.KEY_INVENTORY_INFO) || {}
      let goodsList = info.ingredient_data || []
      if (goodsList && goodsList.length > 0) {
        goodsList.forEach(item => {
          if (item.id === id) {
            item[key] = value
            if (key === 'status' && value === '合格') {
              item.remark = ''
              item.img_json = []
            }
          }
        })
        info.ingredient_data = goodsList
      }
      Cache.set(this.$common.KEY_INVENTORY_INFO, info)
    },
    // 监听输入
    handlerInputChange(value, row) {
      console.log("handlerInputChange", value);
      this.updateByType(row.id, 'receive_count', value)
    },
    // 监听搜索输入
    handlerSearchChange(value) {
      this.updateGoodsList('name', value)
    },
    // 更新商品列表
    updateGoodsList(type, value) {
      let info = Cache.get(this.$common.KEY_INVENTORY_INFO) || {}
      let goodsList = deepClone(info.ingredient_data || [])
      if (type === 'status') {
        const filterName = value === '收货' ? '合格' : value === '不合格' ? '不合格' : ''
        if (goodsList && filterName) {
          goodsList = goodsList.filter(item => {
            return item.status === filterName
          })
        }
      } else {
        if (goodsList) {
          goodsList = goodsList.filter(item => {
            return item.materials_name.indexOf(value) !== -1
          })
        }
      }
      this.goodsList = deepClone(goodsList)
      console.log("handlerDropDownClick", this.goodsList)
    },
    // 格式化价格
    formatPrice(price) {
      if (!price) {
        return 0
      }
      return divide(price)
    },
    // 展示图片
    handlerShowImg(imgList, index) {
      if (imgList && Array.isArray(imgList)) {
        let newImageList = imgList.map(item => {
          return item.url
        })
        if (this.$refs.imgPreview) {
          this.$refs.imgPreview.setImgList(newImageList, index)
          this.$refs.imgPreview.showDialog()
        }
      }
    },
    // 删除图片
    handlerDeleteImg(row, index) {
      var that = this
      let info = Cache.get(this.$common.KEY_INVENTORY_INFO) || {}
      this.$confirm(
        {
          dialogTypeValue: "content", // 弹窗类型
          titleTxt: that.$t('tip.prompt'), // 弹窗标题
          contentTxt: "确定去掉该图片吗？",
          isShowDialog: true, // 是否显示弹窗
          cancelCallBack: function () {
            console.log("点击取消");
          },
          confirmCallBack: function () {
            console.log("confirmCallback", index, row);
            row.splice(index, 1)
            info.ingredient_data = that.goodsList
            Cache.set(that.$common.KEY_INVENTORY_INFO, info)
          }
        }, that)
    },
    // 格式化规格
    formatSpecifications(row) {
      if (row && Reflect.has(row, 'specification_record')) {
        const specificationRecord = row.specification_record || ''
        return specificationRecord || '--'
      }
      return '--'
    }
  }
}
</script>
<style lang="scss" scoped>
.purchase-goods-container {
  height: 100vh;

  .title {
    color: #8f9295;
  }

  .m-t-140 {
    margin-top: 140rpx;
  }

  .search-layout {
    position: fixed;
    top: 88rpx;
    left: 0;
    right: 0;
    z-index: 99;
    background-color: #fff;
  }

  .search-tag {
    width: 670rpx;
    background-color: #f0f3f5;
    border-radius: 12rpx;
    margin: 20rpx auto;
    padding: 0rpx 20rpx;
    position: relative;

    .search-input {
      width: 400rpx;
      padding-left: 20rpx;
    }
  }

  .list-tag {
    padding-bottom: 110rpx;
  }

  .good-tag {
    width: 670rpx;
    background-color: #fff;
    border-radius: 12rpx;
    margin: 30rpx auto;
    padding: 30rpx;

    .good-title {
      color: #1d201e;
      font-size: 30rpx;
    }

    .good-content {
      width: 610rpx;
      height: 138rpx;
      background-color: #eff1f6;
      border-radius: 8rpx;
      display: inline-flex;

      .left {
        width: 30%;
      }

      .line-middle {
        width: 1px;
        height: 89rpx;
        background-color: #e4e6ea;
        margin: 25rpx;
      }

      .right {
        width: 40%;

        .input-number {
          width: 180rpx;
          height: 56rpx;
          background-color: #ffffff;
          border-radius: 6rpx;
          border: solid 1px #e4e6ea;
          padding: 0 10rpx;
        }
      }

      .price {
        font-size: 42rpx;
        color: #1d1e20;
      }

      .tip-middle {
        text-align: center;
      }
    }

    .doit {
      display: inline-block;
      width: 12rpx;
      height: 12rpx;
      border-radius: 12rpx;
      background-color: #c8c8c8;
      margin: 5rpx 10rpx;
    }
  }

  .color-gray {
    color: #8f9295;
  }

  .color-green {
    color: #51d854;
  }

  .btn-layout-detail {
    width: 100%;
    display: inline-flex;
    justify-content: flex-end;
    padding-top: 30rpx;
  }

  .btn-upload {
    width: '180rpx';
    height: '48rpx';
    border-radius: '8rpx';
    font-size: '24rpx';
    padding: 0;
  }

  .btn-cancel {
    width: '140rpx';
    height: '48rpx';
    border-radius: '8rpx';
    font-size: '24rpx';
    padding: 0;
  }

  .line-gray {
    width: 1px;
    height: 70rpx;
    background-color: #dce0e3;
  }

  .btn-layout {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    min-height: 118rpx;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;

    .approval_btn_style {
      width: 670rpx;
      height: 70rpx;
      border-radius: 8rpx;
      font-size: 28rpx;
      margin: 40rpx auto;
    }
  }

  .images {
    width: 600rpx;
    display: inline-flex;
    flex-wrap: wrap;
    justify-content: flex-start;

    .img-tag {
      width: 190rpx;
      height: 190rpx;
      border-radius: 6rpx;
      position: relative;

      image {
        width: 100%;
        height: 100%;
      }

      .top-delete {
        position: absolute;
        top: 0;
        right: 0;
      }
    }
  }
}
</style>

<template>
  <view class="purchase-container">
    <!--标题栏-->
    <!--#ifdef MP-WEIXIN || H5-->
    <u-navbar :title="$t('title.inventory.take.delivery')" placeholder :autoBack="true"
      :leftIconColor="color.navigation" leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"></u-navbar>
    <!--#endif-->
    <!--搜索-->
    <view class="search-layout">
      <view class="search-tag">
        <view class="flex col-center">
          <view @click="handlerChooseDate">{{ chooseDate }}</view>
          <u-icon name="arrow-down" class="m-l-10" @click="handlerChooseDate"></u-icon>
          <view class="line-gray m-l-15 m-r-15"></view>
          <u-icon name="search"></u-icon>
          <view class="search-input"><u-input v-model="searchContent" border="none"
              :placeholder="$t('page.service.menu.order.input.tip')" autocomplete="off" @input="handlerInput"></u-input>
          </view>
        </view>
      </view>
      <filterDropDown id="drowDown" ref="drowDown" @showDropDownChange="showDropDownChange"
        @handlerDropDownClick="handlerDropDownClick" :dataList="chooseDataList" :chooseTarget="chooseDate">
      </filterDropDown>
    </view>
    <div class="user_list_main_container" v-if="!isShowEmptyView">
      <mescroll-uni ref="mescrollRef" :fixed="false" :safearea="false" :bottom="0" @init="mescrollInit"
        @down="downCallback" @up="upCallback" :down="{ auto: false }" :up="{ auto: false }">
        <view class="purchase-tag" v-for="(item, index) in orderList" :key="index" @click="goDetail(item, index)">
          <view class="menu-tag">
            <view class="menu-text-title">{{ item.trade_no }}</view>
            <view class="hor-line-gray"></view>
            <view class="menu-text">
              <view>供应商：</view>
              <view class="color-1d">{{ item.supplier_manage_name }}</view>
            </view>
            <view class="menu-text">
              <view>送达时间：</view>
              <view class="color-1d">{{  item.update_time  }}</view>
            </view>
            <view :class="['menu-right', item.order_status == 'confirmed' ? 'active' : 'no-active']">{{
              item.order_status == 'confirmed' ? '已收货':'待收货' }}</view>
          </view>
        </view>
      </mescroll-uni>
    </div>
    <!-- 空白页 -->
    <emptyComponent :emptyContent="emptyContent" v-if="isShowEmptyView"></emptyComponent>
  </view>
</template>
<script>
import { mapGetters } from 'vuex'
import FilterDropDown from '@/components/FilterDropdown/FilterDropdown.vue'
import EmptyComponent from "@/components/EmptyComponent/EmptyComponent"
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import { apiMerchantMobileVendorReceivingNoteVendorDeliveryInfoListPost } from '@/api/inventory'
import { deepClone, getLastDayRange, debounce } from "../../utils/util"
import Cache from '../../utils/cache';
export default {
  components: {
    FilterDropDown,
    EmptyComponent
  },
  data() {
    return {
      imgPath: this.$imgPath,
      chooseDataList: [
        {
          name: '最近3天',
          id: 1
        },
        {
          name: '最近7天',
          id: 2
        },
        {
          name: '最近30天',
          id: 3
        }
      ],
      chooseDate: '最近3天',
      chooseIndex: 0,
      searchContent: '',
      idShowDrow: false,
      topHeightDistance: uni.upx2px(90),
      orderList: [],
      isShowEmptyView: false,
      emptyContent: this.$t('tip.no.search.content'), // 空数据提示
      params: { // 筛选入参
        start_date: getLastDayRange(3, '{y}-{m}-{d}')[0],
        end_date: getLastDayRange(3, '{y}-{m}-{d}')[1]
      },
      pageNo: 1
    }
  },
  mixins: [MescrollMixin],
  computed: {
    ...mapGetters(['color'])
  },
  onLoad(e) {
    console.log("onLoad", e);
  },
  onShow() {
    this.getOrderList()
  },
  methods: {
    // 输入监听
    handlerInput: debounce(function () {
      console.log("handlerInput", this.searchContent);
      this.getData()
    }, 500),
    /**
    * 下拉列表变化
    * @param {*} e
    */
    showDropDownChange(e) {
      if (!e) {
        this.currentIndex = -1
      }
      this.$emit('showDropDownStatusChange', e)
    },
    /**
     * 下拉项目点击
     */
    handlerDropDownClick(data) {
      console.log(data)
      this.chooseDate = data.name
      switch (this.chooseDate) {
        case '最近3天':
          this.params.start_date = getLastDayRange(3, '{y}-{m}-{d}')[0]
          this.params.end_date = getLastDayRange(3, '{y}-{m}-{d}')[1]
          break;
        case '最近7天':
          this.params.start_date = getLastDayRange(7, '{y}-{m}-{d}')[0]
          this.params.end_date = getLastDayRange(7, '{y}-{m}-{d}')[1]
          break;
        case '最近30天':
          this.params.start_date = getLastDayRange(30, '{y}-{m}-{d}')[0]
          this.params.end_date = getLastDayRange(30, '{y}-{m}-{d}')[1]
          break;
        default:
          break;
      }
      this.getData()
    },
    // 显示下拉框
    handlerChooseDate() {
      if (this.$refs.drowDown) {
        this.$refs.drowDown.setChooseItem(this.chooseDate)
        this.idShowDrow = !this.$refs.drowDown.getDropDownStatus()
        this.$refs.drowDown.showDropDown(this.idShowDrow)
      }
    },
    /**
   * 下拉刷新返回
   */
    downCallback(page) {
      console.log(" downCallback page", page);
      // this.params.page = 1
      this.getOrderList(this.searchContent)
    },
    /**
     * 上拉加载更多
     * @param {*} page
     */
    upCallback(page) {
      console.log(" upCallback page", page);
      // this.params.page = this.params.page++
      this.getOrderList(this.searchContent)
    },
    // 获取订单列表
    getData() {
      console.log("getData");
      // this.params.page = 1
      this.getOrderList(this.searchContent)
    },
    /**
     * 获取用户列表
     * @param {*} searchValue  搜索内容
     */
    getOrderList(searchValue) {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      if (searchValue) {
        this.params.trade_no = searchValue
      } else {
        delete this.params.trade_no
      }
      apiMerchantMobileVendorReceivingNoteVendorDeliveryInfoListPost(this.params)
        .then(res => {
          if (res.code === 0) {
            uni.hideLoading()
            var data = res.data ? res.data : {}
            console.log("apiBackgroundAdminUserList data", data);
            var resultList = []
            if (Reflect.has(data, 'results')) {
              resultList = data.results || []
            }
            var count = data.count ? data.count : 0
            console.log("apiBackgroundAdminUserList resultList", resultList);

            if (resultList && resultList.length > 0 && this.pageNo === 1) {
              // 刚开始加载
              this.orderList = deepClone(resultList)
              console.log("apiBackgroundAdminUserList userinfo", this.orderList);
            } else if (resultList && resultList.length > 0 && this.pageNo !== 1) {
              // 增量加载，加载更多
              this.orderList = this.orderList.concat(resultList)
              console.log("apiBackgroundAdminUserList userinfo", this.orderList);
            } else if (this.pageNo !== 1 && resultList.length === 0) {
              console.log("没有加载更多了");
              uni.$u.toast("没有更多了")
            } else {
              // 其他情况
              this.$set(this, "orderList", [])
              uni.hideLoading()
              var resultMsg = res.msg && res.msg !== '成功' ? res.msg : '暂无数据'
              uni.$u.toast(resultMsg)
            }
            // 如果是第一页并且没有数据要展示没有数据层
            this.isShowEmptyView = this.orderList.length === 0 && this.pageNo === 1
            this.mescroll.setPageNum(this.pageNo)
            this.mescroll.endBySize(this.orderList.length, count)
          } else {
            console.log("code !=0");
            this.$set(this, "orderList", [])
            uni.hideLoading()
            uni.$u.toast(res.msg || "")
            this.mescroll.endErr()
          }
        })
        .catch(err => {
          this.$set(this, "orderList", [])
          console.log("err list", err);
          if (err.message) {
            uni.$u.toast(err.message)
          }
          this.mescroll.endErr()
        })
    },
    // 跳转详情
    goDetail(row, index) {
      console.log("goDetail", row, index);
      Cache.set(this.$common.KEY_INVENTORY_INFO, row)
      if (row.order_status === 'confirmed') {
        this.$miRouter.push({ path: '/pages_others/inventory/purchase_inventory_detail', query: { id: row.id } })
      } else {
        this.$miRouter.push({ path: '/pages_others/inventory/purchase_inventory_voucher', query: { id: row.id } })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.purchase-container {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;

  .search-layout {
    position: fixed;
    top: 84rpx;
    left: 0;
    right: 0;
    z-index: 99;
    background-color: #fff;
  }

  .search-tag {
    width: 670rpx;
    background-color: #f0f3f5;
    border-radius: 12rpx;
    margin: 20rpx auto;
    padding: 0rpx 20rpx;
    position: relative;

    .search-input {
      width: 400rpx;
      padding-left: 20rpx;
    }
  }

  .purchase-tag {
    width: 670rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    margin: 30rpx auto;
    overflow: hidden;

    .menu-tag {
      width: 100%;
      min-height: 221rpx;
      position: relative;
    }

    .menu-text-title {
      padding: 30rpx 30rpx 20rpx 30rpx;
      font-size: 32rpx;
      color: #1d1e20;
    }

    .menu-text {
      display: flex;
      justify-content: space-between;
      margin-top: 20rpx;
      font-size: 24rpx;
      color: #8f9295;
      padding: 0 30rpx;
    }

    .menu-right {
      position: absolute;
      top: 0;
      right: 0;
      width: 110rpx;
      height: 44rpx;
      line-height: 50rpx;
      border-top-right-radius: 12rpx;
      font-size: 24rpx;
      border-bottom-left-radius: 12rpx;
      color: #fff;
      text-align: center;
    }

    .active {
      background-color: #57d648;
    }

    .no-active {
      background-color: #fd953c;
    }
  }

  // #ifdef H5
  .user_list_main_container {
    width: 100%;
    height: calc(100% - 110rpx);
    margin-top: 88rpx;
  }

  // #endif
  // #ifdef MP-WEIXIN
  .user_list_main_container {
    width: 100%;
    height: calc(100vh - 160rpx);
    margin-top: 88rpx;
  }

  // #endif
  .line-gray {
    width: 1px;
    height: 70rpx;
    background-color: #dce0e3;
  }

  .color-1d {
    color: #1d1e20;
  }

  .hor-line-gray {
    width: 610rpx;
    height: 1px;
    margin: 0 auto;
    background-color: #eae9ed;
  }
}
</style>

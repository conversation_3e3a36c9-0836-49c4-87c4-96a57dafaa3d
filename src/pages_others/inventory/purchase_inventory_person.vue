<template>
  <!--人员确认提交-->
  <view class="purchase-person-container">
    <!--标题栏-->
    <!--#ifdef MP-WEIXIN || H5-->
    <u-navbar :title="$t('title.inventory.take.delivery')" placeholder :autoBack="true"
      :leftIconColor="color.navigation" leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"></u-navbar>
    <!--#endif-->
    <!--人员添加-->
    <view class="custom-tag add flex row-between col-center">
      <view class="title ">{{ $t('page.inventory.penson.verification.personnel') }}</view>
      <view>
        <u-button :customStyle="customStyleBtnAdd" class="btn-add" :disabled="isBtnDisable"
          :color="color.colorBtnOrange" @click="handlerAdd()" >{{
            $t('page.inventory.penson.verification.add') }}</u-button>
      </view>
    </view>
    <view class="custom-tag person" v-for="(item, index) in personList" :key="index">
      <view class="flex row-between col-center">
        <view class="title">{{ $t('page.inventory.penson.verification.personnel') + (index + 1) }}</view>
        <view>
          <u-button class="btn-red" :customStyle="customStyleRedBtn" :plain="true" :color="color.colorBtnRed"
            @click="deletePerson(item, index)">
            {{ $t('announcement.Delete') }}
          </u-button>
        </view>
      </view>
      <view class="line-hor-gray"></view>
      <view class="flex xs">
        <view class="w-200 color-gray"> {{ item.role_name }}：</view>
        <view> {{ item.name ? item.name :item.username }}</view>
      </view>
      <view class="flex m-t-20 xs">
        <view class="w-200 color-gray">{{ $t('page.inventory.harvest.live.facial.recognition') }}：</view>
        <view>
          <image :src="item.face_url" class="img-person" @click="handlerShowImg([item.face_url],0)" />
        </view>
      </view>
    </view>
    <view class="btn-layout">
      <u-button :customStyle="customStyleBtn" class="approval_btn_style"
        :disabled="isBtnDisable || personList.length === 0" :color="color.colorBtnOrange" @click="handlerSave">{{ $t('page.invitation.submit')
        }}</u-button>
    </view>
    <!--#ifdef MP-WEIXIN || MP-ALIPAY -->
    <CustomDialogComponent ref="customDialog"></CustomDialogComponent>
    <!--#endif-->
    <!--图片预览-->
    <image-preview ref="imgDialogPreview"></image-preview>
  </view>
</template>
<script>
import { mapGetters } from 'vuex'
import { setUploadPic } from './utils/util'
import Cache from '../../utils/cache';
import { deepClone } from '@/utils/util'
import { apiMerchantMobileVendorReceivingNoteReceivingNoteAddPost } from '@/api/inventory'
import ImagePreview from '@/components/ImagePreview/ImagePreview.vue';

export default {
  data() {
    return {
      imgPath: this.$imgPath,
      imagesList: [],
      personList: [],
      customStyleBtnAdd: {
        width: '208rpx',
        height: '48rpx',
        borderRadius: '8rpx',
        padding: '0'
      },
      customStyleRedBtn: {
        width: '120rpx',
        height: '48rpx',
        borderRadius: '6rpx'
      },
      customStyleBtn: {
        width: '670rpx',
        height: '70rpx',
        borderRadius: '8rpx',
        fontSize: '28rpx',
        margin: '19rpx  40rpx'
      },
      isBtnDisable: false
    }
  },
  components: {
    ImagePreview
  },
  computed: {
    ...mapGetters(['color'])
  },
  onShow() {
    this.updataInfo()
    console.log("onShow", this.personList);
  },
  methods: {
    // 添加人员
    async handlerAdd() {
      let [err, res] = await this.$to(setUploadPic(1, 'penson', this))
      if (err) {
        return this.$u.toast('上传照片失败')
      }
      if (res) {
        console.log("resultUrl", res);
        Cache.set(this.$common.KEY_UPLOAD_PERSON_PIC, res[0])
        this.$miRouter.push({ path: '/pages_others/inventory/purchase_inventory_camera' })
      }
    },
    // 保存
    handlerSave() {
      if (this.isBtnDisable) {
        return
      }
      console.log("handlerSave");
      this.saveData()
    },
    // 保存数据
    saveData() {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      this.isBtnDisable = true
      let info = Cache.get(this.$common.KEY_INVENTORY_INFO) || {}
      let ingredientData = []
      if (info.ingredient_data && info.ingredient_data.length > 0) {
        ingredientData = info.ingredient_data.map(item => {
          let checkFile = item.check_file || []
          if (checkFile && checkFile.length > 0) {
            checkFile = checkFile.map(item => {
              return item.url
            })
          }
          return {
            materials_id: item.materials,
            receive_count: item.receive_count,
            purchase_unit: item.purchase_unit,
            purchase_weight: item.purchase_weight,
            unit_price: item.unit_price,
            total_price: item.unit_price * item.receive_count,
            start_valid_date: item.start_valid_date,
            end_valid_date: item.end_valid_date,
            is_pass: item.status === '合格' ? 1 : 0,
            check_file: checkFile,
            img_json: item.img_json,
            remark: item.remark,
            specification_record: item.specification_record, // 规格
            limit_count_record: item.limit_count_record // 最小单位
          }
        })
      }
      let params = {
        vendor_delivery_id: info.id,
        data: ingredientData,
        check_accounts: this.personList,
        purchase_certificate: info.voucherList
      }
      console.log("params", params, info.ingredient_data);
      apiMerchantMobileVendorReceivingNoteReceivingNoteAddPost(params).then(res => {
        uni.hideLoading()
        this.isBtnDisable = false
        console.log("apiMerchantMobileVendorReceivingNoteReceivingNoteAddPost", res)
        this.$u.toast('保存成功')
        this.$miRouter.back(3)
      }).catch(error => {
        uni.hideLoading()
        this.isBtnDisable = false
        console.log("error", error);
        this.$u.toast(error.message)
      })
    },
    // 删除人员
    deletePerson(item, index) {
      var that = this
      this.$confirm(
        {
          dialogTypeValue: "content", // 弹窗类型
          titleTxt: that.$t('tip.prompt'), // 弹窗标题
          contentTxt: "确认要删除该核验人员？",
          isShowDialog: true, // 是否显示弹窗
          cancelCallBack: function () {
            console.log("点击取消");
          },
          confirmCallBack: function (data) {
            console.log("confirmCallback", data);
            that.personList.splice(index, 1)
            let info = Cache.get(that.$common.KEY_INVENTORY_INFO) || {}
            info.personList = that.personList
            Cache.set(that.$common.KEY_INVENTORY_INFO, info)
          }
        }, that)
    },
    updataInfo() {
      let info = Cache.get(this.$common.KEY_INVENTORY_INFO) || {}
      let personList = info.personList || []
      if (personList) {
        this.personList = deepClone(personList)
      }
    },
    // 展示图片
    handlerShowImg(imgList, index) {
      if (this.$refs.imgDialogPreview) {
        this.$refs.imgDialogPreview.setImgList(imgList, index)
        this.$refs.imgDialogPreview.showDialog()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.purchase-person-container {
  padding-bottom: 110rpx;

  .custom-tag {
    width: 670rpx;
    background-color: #fff;
    border-radius: 12rpx;
    min-height: 90rpx;
    margin: 40rpx auto;
    padding: 30rpx;
  }

  .title {
    font-size: 28rpx;
    color: #1d1e20;
  }

  .add {}

  .btn-add {
    width: 208rpx;
    height: 48rpx;
    border-radius: 8rpx;
    padding: 0;
  }

  .btn-red {
    width: 120rpx;
    height: 48rpx;
    border-radius: 6rpx;
  }

  .line-hor-gray {
    margin: 20rpx auto;
    width: 610rpx;
    height: 1px;
    background-color: #eae9ed;
  }

  .img-person {
    width: 120rpx;
    height: 148rpx;
    border-radius: 6rpx;
  }

  .color-gray {
    color: #8f9295;
  }

  .btn-layout {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    min-height: 118rpx;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;

    .approval_btn_style {
      width: 670rpx;
      height: 70rpx;
      border-radius: 8rpx;
      font-size: 28rpx;
      margin: 40rpx auto;
    }
  }
}
</style>

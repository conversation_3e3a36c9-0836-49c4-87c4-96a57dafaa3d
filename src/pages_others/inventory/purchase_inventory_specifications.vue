<template>
  <!--人员确认提交-->
  <view class="purchase-specifications-container">
    <!--标题栏-->
    <!--#ifdef MP-WEIXIN || H5-->
    <u-navbar :title="$t('title.inventory.take.delivery')" placeholder :autoBack="true"
      :leftIconColor="color.navigation" leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"></u-navbar>
    <!--#endif-->
    <!--图片添加-->
    <view :class="['img-list']">
      <view v-for="(item, index) in imagesList" :key="index"
        :class="['img-tag', 'm-t-10', index % 2 != 0 ? 'm-l-10' : '']" @click.stop="handlerShowImg(imagesList, index)">
        <image :src="item.url" mode="aspectFit"></image>
        <view class="top-delete" @click.stop="handlerDeleteImg(index)" v-if="item.isDelete"><u-icon name="close"
            color="#8f9295" size="36"></u-icon>
        </view>
      </view>
    </view>
    <view v-if="!imagesList || imagesList.length === 0">
      <empty-component class="empty-view"></empty-component>
    </view>
    <view class="btn-layout">
      <u-button :customStyle="customStyleBtn" class="approval_btn_style"
        :disabled="isBtnDisable || (imagesList && imagesList.length >= 6)" :color="color.colorBtnOrange"
        @click="handlerUploadPic">{{ $t('announcement.Upload.image')
        }}</u-button>
    </view>
    <!--#ifdef MP-WEIXIN || MP-ALIPAY -->
    <CustomDialogComponent ref="customDialog"></CustomDialogComponent>
    <!--#endif-->
    <!--图片预览-->
    <image-preview ref="imgDialogPreview"></image-preview>
  </view>
</template>
<script>
import { mapGetters } from 'vuex'
import { setUploadPic } from './utils/util'
import Cache from '../../utils/cache';
import { deepClone } from '@/utils/util'
import ImagePreview from '@/components/ImagePreview/ImagePreview.vue';
import EmptyComponent from '@/components/EmptyComponent/EmptyComponent.vue';

export default {
  data() {
    return {
      imgPath: this.$imgPath,
      imagesList: [],
      customStyleBtnAdd: {
        width: '208rpx',
        height: '48rpx',
        borderRadius: '8rpx',
        padding: '0'
      },
      customStyleBtn: {
        width: '670rpx',
        height: '70rpx',
        borderRadius: '8rpx',
        fontSize: '28rpx',
        margin: '19rpx  40rpx'
      },
      isBtnDisable: false,
      positionIndex: 0 // 图片位置
    }
  },
  components: {
    ImagePreview,
    EmptyComponent
  },
  computed: {
    ...mapGetters(['color'])
  },
  onShow() {
    this.updataInfo()
  },
  methods: {
    // 添加人员
    async handlerAdd() {
      let [err, res] = await this.$to(setUploadPic(1, 'penson', this))
      if (err) {
        return this.$u.toast('上传照片失败')
      }
      if (res) {
        console.log("resultUrl", res);
        Cache.set(this.$common.KEY_UPLOAD_PERSON_PIC, res[0])
        this.$miRouter.push({ path: '/pages_others/inventory/purchase_inventory_camera' })
      }
    },
    updataInfo() {
      let query = this.$route.query || {}
      this.positionIndex = query.index || -1
      let info = Cache.get(this.$common.KEY_INVENTORY_INFO) || {}
      let goodList = info.ingredient_data || []
      this.imagesList = this.positionIndex >= 0 ? goodList[this.positionIndex].check_file : []
      console.log("updataInfo", query, this.positionIndex, info, goodList);
    },
    // 展示图片
    handlerShowImg(imgList, index) {
      if (imgList && Array.isArray(imgList)) {
        let newImageList = imgList.map(item => {
          return item.url
        })
        if (this.$refs.imgDialogPreview) {
          this.$refs.imgDialogPreview.setImgList(newImageList, index)
          this.$refs.imgDialogPreview.showDialog()
        }
      }
    },
    // 上传图片
    async handlerUploadPic() {
      let length = this.imagesList ? this.imagesList.length : 0
      if (length >= 6) {
        return this.$u.toast("图片数量不能超过6张")
      }
      let [err, res] = await this.$to(setUploadPic(6 - length, 'goods', this))
      if (err) {
        return this.$u.toast('上传照片失败')
      }
      if (res) {
        console.log("resultUrl", res);
        if (res && res.length > 0) {
          res = res.map(item => {
            return {
              url: item,
              isDelete: true
            }
          })
        }
        let checkFile = deepClone(this.imagesList || [])
        checkFile = checkFile.concat(res)
        this.imagesList = deepClone(checkFile)
        let info = Cache.get(this.$common.KEY_INVENTORY_INFO) || {}
        let goodList = info.ingredient_data || []
        goodList[this.positionIndex].check_file = this.imagesList
        info.ingredient_data = deepClone(goodList)
        Cache.set(this.$common.KEY_INVENTORY_INFO, info)
        console.log("handlerUploadPic", info.ingredient_data);
      }
    },
    // 删除图片
    handlerDeleteImg(index) {
      var that = this
      let info = Cache.get(this.$common.KEY_INVENTORY_INFO) || {}
      this.$confirm(
        {
          dialogTypeValue: "content", // 弹窗类型
          titleTxt: that.$t('tip.prompt'), // 弹窗标题
          contentTxt: "确定去掉该图片吗？",
          isShowDialog: true, // 是否显示弹窗
          cancelCallBack: function () {
            console.log("点击取消");
          },
          confirmCallBack: function () {
            console.log("confirmCallback", index);
            that.imagesList.splice(index, 1)
            let goodList = info.ingredient_data || []
            goodList[that.positionIndex].check_file = that.imagesList
            info.ingredient_data = deepClone(goodList)
            Cache.set(that.$common.KEY_INVENTORY_INFO, info)
          }
        }, that)
    }
  }
}
</script>
<style lang="scss" scoped>
.purchase-specifications-container {
  padding-bottom: 110rpx;

  .img-list {
    display: flex;
    flex-wrap: wrap;
    padding: 20rpx;

    .img-tag {
      width: 350rpx;
      height: 500rpx;
      background-color: #fff;
      position: relative;
      cursor: pointer;

      image {
        width: 100%;
        height: 100%;
      }

      .top-delete {
        position: absolute;
        top: 20rpx;
        right: 20rpx;
      }
    }
  }

  .custom-tag {
    width: 670rpx;
    background-color: #fff;
    border-radius: 12rpx;
    min-height: 90rpx;
    margin: 40rpx auto;
    padding: 30rpx;
  }

  .title {
    font-size: 28rpx;
    color: #1d1e20;
  }

  .btn-add {
    width: 208rpx;
    height: 48rpx;
    border-radius: 8rpx;
    padding: 0;
  }

  .line-hor-gray {
    margin: 20rpx auto;
    width: 610rpx;
    height: 1px;
    background-color: #eae9ed;
  }

  .color-gray {
    color: #8f9295;
  }

  .btn-layout {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    min-height: 118rpx;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;

    .approval_btn_style {
      width: 670rpx;
      height: 70rpx;
      border-radius: 8rpx;
      font-size: 28rpx;
      margin: 40rpx auto;
    }
  }
}
</style>

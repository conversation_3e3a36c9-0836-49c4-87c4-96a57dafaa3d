<template>
  <!--凭证-->
  <view class="purchase-voucher-container">
    <!--标题栏-->
    <!--#ifdef MP-WEIXIN || H5-->
    <u-navbar :title="$t('title.inventory.take.delivery')" placeholder :autoBack="true"
      :leftIconColor="color.navigation" leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"></u-navbar>
    <!--#endif-->
    <view class="color-gray m-b-20 m-l-40 m-t-40 lg">
      {{ orderInfo.trade_no }}
    </view>
    <!--上传本次采购凭证-->
    <view class="custom-tag">
      <view class="title ">{{ $t('tip.please.upload.purchase.voucher') }}</view>
      <view class="upload-tag flex flex-col col-center row-center m-t-10" v-if="!imagesList || !imagesList.length">
        <!--上传图片-->
        <view class="add-pic" @click="handlerAddPic">
          <image :src="imgPath.IMG_OTHER_ADD_ORANGE" />
        </view>
        <view class="color-gray m-t-10">{{ $t('tip.click.upload.image') }}</view>
      </view>
      <!--图片组-->
      <view class="images m-t-10" v-if="imagesList && imagesList.length > 0">
        <view :class="['img-tag', subIndex % 3 > 0 ? 'm-l-10' : '', subIndex > 2 ? 'm-t-10' : '']"
          @click.stop="handlerShowImg(imagesList, subIndex)" v-for="(sub, subIndex) in imagesList" :key="subIndex">
          <image :src="sub" mode="aspectFill"></image>
          <view class="top-delete" @click.stop="handlerDeleteImg(subIndex)"><u-icon name="close" color="#8f9295"
              size="46"></u-icon>
          </view>
        </view>
        <view class="add-pic m-l-10" @click="handlerAddPic" v-if="imagesList.length < 6">
          <image :src="imgPath.IMG_OTHER_ADD_ORANGE" />
        </view>
      </view>
    </view>
    <view class="custom-tag person">
      <view class="flex row-between col-center">
        <view class="title">{{ $t('page.inventory.voucher.delivery.information') }}</view>
      </view>
      <view class="line-hor-gray"></view>
      <!--司机信息-->
      <view v-for="(driverItem, driverIndex) in orderInfo.driver_info" :key="driverIndex">
        <view class="flex xs">
          <view class="w-130 color-gray"> {{ $t('page.inventory.voucher.driver.name') }}：</view>
          <view> {{ driverItem.name }}</view>
        </view>
        <view class="flex xs m-t-20">
          <view class="w-130 color-gray"> {{ $t('page.inventory.voucher.driver.phone') }}：</view>
          <view> {{ driverItem.phone }}</view>
        </view>
        <view class="flex m-t-20 xs">
          <view class="w-130 color-gray">{{ $t('page.inventory.voucher.driver.certificate') }}：</view>
          <view class="flex">
            <view v-if="driverItem.driving_licence && driverItem.driving_licence.length > 0">
              <view class="img-tag" v-for="(driverImg, driverImgIndex) in driverItem.driving_licence"
                :key="driverImgIndex" @click="handlerShowImg(driverItem.driving_licence, driverImgIndex)">
                <image :src="driverImg" class="img-person" />
                <view class="img-type">{{ '驾驶证件' }}</view>
              </view>
            </view>
            <view v-if="driverItem.health_certificate && driverItem.health_certificate.length > 0">
              <view class="img-tag m-l-10" v-for="(healthImg, healthImgIndex) in driverItem.health_certificate"
                :key="healthImgIndex" @click="handlerShowImg(driverItem.health_certificate, healthImgIndex)">
                <image :src="healthImg" class="img-person" />
                <view class="img-type">{{ '健康证件' }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- <view class="flex xs m-t-20">
        <view class="w-130 color-gray"> {{ $t('page.inventory.voucher.delivery.type') }}：</view>
        <view> {{ orderInfo.name || "--"  }}</view>
      </view> -->
      <view class="flex xs m-t-20">
        <view class="w-130 color-gray"> {{ $t('page.inventory.voucher.delivery.temperature') }}：</view>
        <view> {{ orderInfo.delivery_temperature ? orderInfo.delivery_temperature + "°C" : "" }}</view>
      </view>

      <!--车辆信息-->
      <view v-for="(vehicleItem, vehicleIndex) in orderInfo.vehicle_info" :key="vehicleIndex">
        <view class="flex xs m-t-20">
          <view class="w-130 color-gray"> {{ $t('page.inventory.voucher.car.type') }}：</view>
          <view> {{ vehicleItem.car_type_alias }}</view>
        </view>
        <view class="flex xs m-t-20">
          <view class="w-130 color-gray"> {{ $t('page.inventory.voucher.car.number') }}：</view>
          <view> {{ vehicleItem.plate_number }}</view>
        </view>
        <view class="flex m-t-20 xs">
          <view class="w-130 color-gray">{{ $t('page.inventory.voucher.car.pic') }}：</view>
          <view class="flex flex-wrap img-car">
            <view v-for="(carItem, carIndex) in vehicleItem.car_img" :key="carIndex"
              @click="handlerShowImg(vehicleItem.car_img, carIndex)" class="">
              <image :src="carItem"
                :class="['img-person', carIndex % 2 > 0 ? 'm-l-10' : '', carIndex > 1 ? 'm-t-10' : '']" />
            </view>
          </view>
        </view>
      </view>
    </view>
    <!--底部按钮-->
    <view class="btn-layout">
      <u-button :customStyle="customStyleBtn" class="approval_btn_style" :disabled="isBtnDisable"
        :color="color.colorBtnOrange" @click="handlerNext">{{ $t('pages.login.Next') }}</u-button>
    </view>
    <!--图片预览-->
    <image-preview ref="imgPreview"></image-preview>
  </view>
</template>
<script>
import { mapGetters } from 'vuex'
import { setUploadPic } from './utils/util'
import Cache from '@/utils/cache';
import { deepClone } from '@/utils/util'
import { apiMerchantMobileVendorReceivingNoteVendorDeliveryDetailListPost } from '@/api/inventory'
import ImagePreview from '@/components/ImagePreview/ImagePreview.vue';

export default {
  data() {
    return {
      imgPath: this.$imgPath,
      imagesList: [],
      isBtnDisable: false,
      customStyleBtn: {
        width: '670rpx',
        height: '70rpx',
        borderRadius: '8rpx',
        fontSize: '28rpx',
        margin: '19rpx  40rpx'
      },
      orderInfo: {}
    }
  },
  components: {
    ImagePreview
  },
  computed: {
    ...mapGetters(['color'])
  },
  onShow() {
    this.updateInfo()
  },
  methods: {
    // 下一步
    handlerNext() {
      this.$miRouter.push({ path: '/pages_others/inventory/purchase_inventory_goods' })
    },
    // 添加照片
    async handlerAddPic() {
      let [err, res] = await this.$to(setUploadPic(6 - this.imagesList.length, 'voucher', this))
      if (err) {
        return this.$u.toast('上传照片失败')
      }
      if (res) {
        console.log("resultUrl", res);
        let info = Cache.get(this.$common.KEY_INVENTORY_INFO) || {}
        let voucherList = info.voucherList || []
        voucherList = voucherList.concat(res)
        info.voucherList = deepClone(voucherList)
        Cache.set(this.$common.KEY_INVENTORY_INFO, info)
        this.imagesList = deepClone(voucherList)
      }
    },
    // 更新页面信息
    updateInfo() {
      let info = Cache.get(this.$common.KEY_INVENTORY_INFO) || {}
      console.log("info voucher", info);
      this.getDetailList(info.id)
    },
    // 更新数据
    updateListData(info) {
      console.log("info", info);
      if (info && Reflect.has(info, 'ingredient_data')) {
        // 处理 ingredient_data里面的图片
        let ingredientData = info.ingredient_data || []
        if (ingredientData && ingredientData.length > 0) {
          ingredientData.forEach(item => {
            let inspectionReport = item.inspection_report || []
            if (inspectionReport && inspectionReport.length > 0) {
              let checkFile = inspectionReport.map(item => {
                return {
                  url: item,
                  isDelete: false
                }
              })
              item.check_file = deepClone(checkFile)
            }
          })
        }
        info.ingredientData = deepClone(ingredientData)
      }
      this.orderInfo = deepClone(info)
      let voucherList = info.voucherList || []
      if (voucherList) {
        this.imagesList = deepClone(voucherList)
      }
      Cache.set(this.$common.KEY_INVENTORY_INFO, this.orderInfo)
    },
    // 删除图片
    handlerDeleteImg(index) {
      var that = this
      this.$confirm(
        {
          dialogTypeValue: "content", // 弹窗类型
          titleTxt: that.$t('tip.prompt'), // 弹窗标题
          contentTxt: "确定去掉该图片吗？",
          isShowDialog: true, // 是否显示弹窗
          cancelCallBack: function () {
            console.log("点击取消");
          },
          confirmCallBack: function () {
            console.log("confirmCallback", index, that.imagesList);
            that.imagesList.splice(index, 1)
            that.orderInfo.voucherList = that.imagesList
            Cache.set(that.$common.KEY_INVENTORY_INFO, that.orderInfo)
          }
        }, that)
    },
    // 获取物资详情
    getDetailList(currentId) {
      let params = {
        id: currentId
      }
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      apiMerchantMobileVendorReceivingNoteVendorDeliveryDetailListPost(params)
        .then(res => {
          if (res.code === 0) {
            uni.hideLoading()
            var data = res.data ? res.data : {}
            console.log("apiMerchantMobileVendorReceivingNoteVendorDeliveryDetailListPost data", data);
            this.updateListData(data)
          } else {
            console.log("code !=0");
            this.$set(this, "orderList", [])
            uni.hideLoading()
            uni.$u.toast(res.msg || "")
          }
        })
        .catch(err => {
          uni.hideLoading()
          this.$set(this, "orderList", [])
          console.log("err list", err);
          if (err.message) {
            uni.$u.toast(err.message)
          }
        })
    },
    // 展示图片
    handlerShowImg(imgList, index) {
      if (this.$refs.imgPreview) {
        this.$refs.imgPreview.setImgList(imgList, index)
        this.$refs.imgPreview.showDialog()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.purchase-voucher-container {
  padding-bottom: 110rpx;

  .color-gray {
    color: #8f9295;
  }

  .custom-tag {
    width: 670rpx;
    background-color: #fff;
    border-radius: 12rpx;
    min-height: 90rpx;
    margin: 40rpx auto;
    padding: 30rpx;

    .images {
      width: 600rpx;
      display: inline-flex;
      flex-wrap: wrap;
      justify-content: flex-start;

      .img-tag {
        width: 190rpx;
        height: 190rpx;
        border-radius: 6rpx;
        position: relative;

        image {
          width: 100%;
          height: 100%;
        }

        .top-delete {
          position: absolute;
          top: 0;
          right: 0;
        }
      }
    }
  }

  .upload-tag {
    width: 610rpx;
    height: 300rpx;
    border-radius: 8rpx;
    border: solid 1px #d7d7d7;
  }

  .add-pic {
    width: 190rpx;
    height: 190rrpx;
    display: flex;
    justify-content: center;
    align-items: center;

    image {
      width: 80rpx;
      height: 80rpx;
    }
  }

  .title {
    font-size: 28rpx;
    color: #1d1e20;
  }

  .line-hor-gray {
    margin: 20rpx auto;
    width: 610rpx;
    height: 1px;
    background-color: #eae9ed;
  }

  .img-tag {
    width: 180rpx;
    position: relative;

    .img-type {
      width: 180rpx;
      height: 28rpx;
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      text-align: center;
      color: #fff;
      background-color: #fd953c;
      border-radius: 0px 0px 8rpx 8rpx;
      font-size: 20rpx;
    }
  }

  .img-car {
    width: 500rpx;
  }

  .img-person {
    width: 180rpx;
    height: 128rpx;
    border-radius: 6rpx;
  }

  .btn-layout {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    min-height: 118rpx;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;

    .approval_btn_style {
      width: 670rpx;
      height: 70rpx;
      border-radius: 8rpx;
      font-size: 28rpx;
      margin: 40rpx auto;
    }
  }
}
</style>

import cache from "@/utils/cache"
import common from "@/common/common"
import { to, showLoading } from "@/utils/util"
import Exif from 'exif-js'
import config from "@/config/index"
import { compressImage } from '@/utils/uploadFaceImg'

// 定义一个上传的url
let uploadFileUrl = config.baseUrl + "/api/merchant_mobile/common/upload"
/**
 * 设置上传face
 */
export async function setUploadPic(count, type, vm) {
  // var that = vm
  // // #ifdef MP-WEIXIN || MP-ALIPAY
  // that.$miRouter.push({
  //   path: '/pages_common_function/user/face_photo_graph'
  // })
  // // #endif
  return new Promise((resolve, reject) => {
    // #ifdef H5 || MP-WEIXIN
    // 从相册选择1张图
    uni.chooseImage({
      count: count,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      async success(res) {
        console.log("res.tempFilePaths", res);
        let filesList = []
        // #ifdef H5
        filesList = res.tempFiles || []
        // #endif
        // #ifdef MP-WEIXIN
        filesList = res.tempFilePaths || []
        //  #endif
        let newResultList = []
        let uploadCompleteCount = 0
        if (filesList && filesList.length > 0) {
          for (let i = 0; i < filesList.length; i++) {
            let file = filesList[i]
            uploadImage(file, i, function(index, result) {
              uploadCompleteCount++
              if (result && result.length > 0) {
                newResultList.push(result)
              }
              console.log("uploadCompleteCount", uploadCompleteCount, i);
              if (uploadCompleteCount === filesList.length) {
                resolve(newResultList)
              }
            })
          }
        }
      },
      fail(error) {
        reject(error.message)
      }
    });
    // #endif
  })
}
export async function uploadImage(file, index, uploadCallBack, isBase64) {
  let orientation = ''
  if (!isBase64) {
    // #ifdef H5
    Exif.getData(file, () => {
      orientation = Exif.getTag(file, 'Orientation')
    })
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = async e => {
      let imgUrl = await compressImage(e.currentTarget.result, orientation)
      // 先上传文件形成url
      let [error, resultUrl] = await to(uploadFilePromise(imgUrl))
      if (resultUrl && !error) {
        // 调用上传返回上传的图片
        uploadCallBack(index, resultUrl)
      } else {
        uploadCallBack(index, '')
      }
    }
    // #endif
    // #ifdef MP-WEIXIN || MP-ALIPAY
    let [error, resultUrl] = await to(uploadFilePromise(file))
    if (resultUrl && !error) {
      // 调用上传返回上传的图片
      uploadCallBack(index, resultUrl)
    } else {
      uploadCallBack(index, '')
    }
    // #endif
  } else {
    // 提取 Base64 编码部分
    const base64Data = file.replace(/^data:image\/(\w+);base64,/, "");
    // 将 Base64 编码转换为 ArrayBuffer
    const binary = atob(base64Data);
    const array = [];
    for (let i = 0; i < binary.length; i++) {
      array.push(binary.charCodeAt(i));
    }
    const buffer = new Uint8Array(array).buffer;
    // 创建一个 Blob 对象
    // const blob = new Blob([buffer], { type: 'image/png' });
    // 创建带命名的File对象（新增核心逻辑）
    let fileName = 'canvas' + new Date().getTime() + '.png'
    const fileObj = new File([buffer], fileName, { type: 'image/png' }); // 命名关键代码‌:ml-citation{ref="1,2" data="citationList"}
    // 创建一个 URL 指向 Blob 对象
    let url = URL.createObjectURL(fileObj);
    let [error, resultUrl] = await to(uploadFilePromise(url, fileName))
    if (resultUrl && !error) {
      // 调用上传返回上传的图片
      uploadCallBack(index, resultUrl)
    } else {
      uploadCallBack(index, '')
    }
  }
}

/**
 * 上传文件
 * @param {传入文件的本地路径} url
 * @returns
 */
export function uploadFilePromise(url, key) {
  let params = {
    prefix: 'face'
  }
  if (key) {
    params.key = key
  }
  return new Promise((resolve, reject) => {
    showLoading({
      title: '上传中...',
      mask: true
    })
    uni.uploadFile({
      url: uploadFileUrl,
      filePath: url,
      name: 'file',
      header: {
        TOKEN: cache.get(common.API_TOKEN)
      },
      formData: params,
      success: res => {
        console.log("uni.uploadFile", res);
        uni.hideLoading()
        var faceURl = JSON.parse(res.data).data.public_url
        resolve(faceURl)
      },
      fail: error => {
        console.log("uni.uploadFile", error);
        uni.hideLoading()
        reject(error.message)
      }
    })
  })
}

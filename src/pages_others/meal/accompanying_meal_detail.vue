<template>
  <view class="accompanying_detail">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar :title="$t('title.details')" placeholder :autoBack="true" :leftIconColor="color.navigation"
      leftIconSize="37rpx" :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"></u-navbar>
    <!--#endif-->
    <!--陪餐信息-->
    <view class="base-layout">
      <view class="flex row-between">
        <view class="nr gray-light">陪餐时间：</view>
        <view class="nr black right-txt line-1">{{ formData.meal_time }}</view>
      </view>
      <view class="flex row-between m-t-20">
        <view class="nr gray-light">陪餐餐段：</view>
        <view class="nr black right-txt line-1">{{ getMealName(formData.meal_type) }}</view>
      </view>
      <view class="flex row-between m-t-20">
        <view class="nr gray-light">就餐食堂：</view>
        <view class="nr black right-txt">{{ formData.organization_name }}</view>
      </view>
    </view>
    <!--陪餐人信息-->
    <view class="title m-t-30 m-l-40">陪餐人信息（{{ personList.length }}人）</view>
    <view class="base-layout">
      <view :class="['flex', 'row-between', personIndex > 0 ? 'm-t-20' : '']" v-for="(person, personIndex) in personList"
        :key="personIndex">
        <view class="nr gray-light">{{ '陪餐人'+ (personIndex+1) + ':' }}</view>
        <view class="nr black right-txt line-1">{{ person.identity_type_alias  }} <span class='m-l-20'>{{person.name}}</span></view>
      </view>
    </view>
    <!--食堂环境-->
    <view class="title m-t-30 m-l-40">食堂环境/工作情况评价</view>
    <view class="base-layout">
      <view class="flex row-between">
        <view class="nr gray-light">备餐间：</view>
        <view class="nr black right-txt line-1">{{ getEnvironmentName('room_clean_type', formData.room_clean_type) }}
        </view>
      </view>
      <view class="flex row-between m-t-20">
        <view class="nr gray-light">服务态度：</view>
        <view class="nr black right-txt line-1">{{ getEnvironmentName('room_attitude_type',
          formData.room_attitude_type)}}</view>
      </view>
      <view class="flex row-between m-t-20">
        <view class="nr gray-light">就餐区：</view>
        <view class="nr black right-txt line-1">{{ getEnvironmentName('area_clean_type', formData.area_clean_type) }}
        </view>
      </view>
      <view class="flex row-between m-t-20">
        <view class="nr gray-light">餐饮浪费情况：</view>
        <view class="nr black right-txt line-1">{{ getEnvironmentName('area_waste_type', formData.area_waste_type) }}
        </view>
      </view>
      <view class="flex row-between m-t-20">
        <view class="nr gray-light">其他加工操作区：</view>
        <view class="nr black right-txt line-1">{{ getEnvironmentName('oa_clean_type', formData.oa_clean_type) }}</view>
      </view>
      <view class="flex row-between m-t-20">
        <view class="nr gray-light">规范：</view>
        <view class="nr black right-txt line-1">{{ getEnvironmentName('oa_operate_type', formData.oa_operate_type) }}
        </view>
      </view>
      <view class="flex row-between m-t-20">
        <view class="nr gray-light">餐具消杀区：</view>
        <view class="nr black right-txt line-1">{{ getEnvironmentName('tda_clean_type', formData.tda_clean_type) }}</view>
      </view>
      <view class="flex row-between m-t-20">
        <view class="nr gray-light">消杀情况：</view>
        <view class="nr black right-txt line-1">{{ getEnvironmentName('tda_disinfection_type',
          formData.tda_disinfection_type)}}</view>
      </view>
      <view class="flex row-between m-t-20">
        <view class="nr gray-light">名厨亮灶运作：</view>
        <view class="nr black right-txt line-1">{{ getEnvironmentName('operation_type', formData.operation_type) }}</view>
      </view>
    </view>
    <!--菜品评价-->
    <view class="title m-t-30 m-l-40">菜品评价</view>
    <view class="base-layout">
      <view class="base-bg-gray flex row-between col-center p-l-20 p-r-20">
        <view v-for="(typeName, typeNameIndex) in typeTitleList" :key="typeNameIndex" class="title">
          {{ typeName.name }}
        </view>
      </view>
      <view v-for="(goodItem, goodItemIndex) in goodList" :key="goodItemIndex" class="">
        <view class="flex m-t-20">
          <view class="nr">{{ goodItem.name }}</view>
          <view class="m-l-10">¥{{ getPrice(goodItem.price) }}/份</view>
        </view>
        <view class="flex row-between m-t-20">
          <view class="base-bg-orange nr" v-for="(typeValue, typeValueIndex) in goodItem.typeValueList"
            :key="typeValueIndex">
            {{ typeValue.name }}
          </view>
        </view>
        <view class="base-bg-gray nr p-20 m-t-20 ">
          <span class="gray-light suggest-tag">学生意见和建议： </span>
          <span class="right-break-text ps-inline black">{{ goodItem.remark }}</span>
        </view>
      </view>
    </view>
    <!--陪餐图片-->
    <view class="title m-t-30 m-l-40">陪餐图片/视频</view>
    <view class="flex flex-wrap">
      <view class="m-l-40 m-t-20">
        <image v-for="(item, index) in imgList" :key="index" :src="item" @click="handlerShowImg(imgList,index)"
          :class="['image-pic', index > 0 ? 'm-l-20' : '']"></image>
      </view>
    </view>
    <!--陪餐员签字-->
    <view class="title m-t-30 m-l-40">陪餐员签字</view>
    <view class="base-layout flex flex-center">
      <view class="img-tag">
        <image :src="signImg"
          class="image-sign" @click="handlerShowImg([signImg], 0)"></image>
      </view>

    </view>
    <!--备注-->
    <view class="title m-t-30 m-l-40">备注</view>
    <view class="base-layout">
      <view class="flex row-between">
        <view class="nr gray-light">备注内容：</view>
        <view class="nr black right-txt">{{ formData.remark }}</view>
      </view>
    </view>
    <!--图片预览-->
    <image-preview ref="imgDetailPreview"></image-preview>
  </view>
</template>
<script>
import { mapGetters } from 'vuex'
import Cache from "@/utils/cache"
import { deepClone, divide } from '@/utils/util'
import { getMealTypeName, getNameByType } from "./utils"
import ImagePreview from '@/components/ImagePreview/ImagePreview.vue';

export default {
  data() {
    return {
      imgpath: this.$imgPath,
      formData: {}, // 详情数据
      personList: [], // 陪餐人列表
      imgList: [],
      typeTitleList: [
        {
          name: '感观评价'
        },
        {
          name: '质量评价'
        },
        {
          name: '份量评价'
        },
        {
          name: '价格评价'
        }
      ],
      goodList: [],
      signImg: ''
    }
  },
  components: {
    ImagePreview
  },
  computed: {
    ...mapGetters(['color'])
  },
  created() {
    this.initData()
  },
  methods: {
    // 初始化数据
    initData() {
      let itemData = Cache.get(this.$common.KEY_ACCOMPANYING_ITEM_INFO)
      console.log('initData')
      if (itemData && typeof itemData === 'object') {
        this.formData = deepClone(itemData)
        this.personList = deepClone(itemData.person_record_list || [])
        this.imgList = deepClone(itemData.images || [])
        this.signImg = itemData.face_url || ''
        let foodRecordList = itemData.food_record_list || []
        let setMealRecordList = itemData.set_meal_record_list || []
        let newList = foodRecordList.concat(setMealRecordList)
        newList = newList.map(item => {
          item.typeValueList = [
            {
              name: item.gg_excellent_type_alias
            },
            {
              name: item.zl_excellent_type_alias
            },
            {
              name: item.quantity_type_alias
            },
            {
              name: item.price_type_alias
            }
          ]
          return item
        })
        this.goodList = deepClone(newList)
      }
    },
    // 获取餐段
    getMealName(type) {
      return getMealTypeName(type)
    },
    // 获取食堂环境类型名称
    getEnvironmentName(type, value) {
      return getNameByType(type, value)
    },
    // 获取价格
    getPrice(price) {
      if (!price) {
        return 0
      }
      return divide(price)
    },
    // 展示图片
    handlerShowImg(imgList, index) {
      if (this.$refs.imgDetailPreview) {
        this.$refs.imgDetailPreview.setImgList(imgList, index)
        this.$refs.imgDetailPreview.showDialog()
      }
    }
  }
}

</script>
<style lang="scss" scoped>
@import './base.css';

.accompanying_detail {
  padding-bottom: 30rpx;

  .image-sign {
    width: 311rpx;
    min-height: 153rpx;
    cursor: pointer;
  }
  .img-tag{
    image {
      width: 311rpx;
      height: 153rpx;
    }
  }

  .image-pic {
    width: 152rpx;
    height: 152rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
  }

}
</style>

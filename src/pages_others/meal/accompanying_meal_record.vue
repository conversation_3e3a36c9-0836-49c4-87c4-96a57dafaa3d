<template>
  <!--陪餐记录-->
  <view class="meal_management_container">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar :title="$t('page.service.menu.accompanying.meal.record')" placeholder :autoBack="true"
      :leftIconColor="color.navigation" leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"></u-navbar>
    <!--#endif-->
    <!--menu层-->
    <view class="menu-layout">
      <u-tabs :current="currentIndex" :list="menuList" lineWidth="60rpx" lineHeight="5"
        :activeStyle="{ color: color.themeColor }" :lineColor="color.themeColor" @click="switchMenu"></u-tabs>
    </view>
    <!--内容列表-->
    <view v-if="currentIndex === 0">
       <meal-record-add @save="saveConfirm"/>
    </view>
    <view v-else-if="currentIndex === 1">
      <record-history-list />
    </view>
    <!--#ifdef MP-WEIXIN || MP-ALIPAY -->
    <CustomDialogComponent ref="customDialog"></CustomDialogComponent>
    <!--#endif-->
    <!--图片预览-->
    <image-preview ref="imgPreview"></image-preview>
  </view>
</template>
<script>
import { mapGetters } from 'vuex'
import { divide } from "../../utils/util"
import ImagePreview from '@/components/ImagePreview/ImagePreview.vue';
import RecordHistoryList from './components/RecordHistoryList.vue'
import MealRecordAdd from './components/MealRecordAdd.vue'

export default {
  data() {
    return {
      imgPath: this.$imgPath,
      cellStyle: { height: "50rpx" },
      titleStyle: { 'font-size': "24rpx", color: '#8f9295' },
      valueStyle: { 'font-size': "24rpx", color: '#1d1e20' },
      menuList: [
        {
          name: this.$t('page.service.menu.accompanying.meal.record'),
          type: 'record'
        },
        {
          name: this.$t('page.service.menu.accompanying.meal.history'),
          type: 'history'
        }
      ],
      currentIndex: 0,
      activeStyle: {
        color: '#fff',
        background: '#FD953C',
        width: ' 110rpx',
        height: '52rpx',
        textAlign: 'center',
        lineHeight: '52rpx',
        borderRadius: '8rpx'
      },
      inactiveStyle: {
        color: '#000',
        background: '#fff',
        width: ' 110rpx',
        height: '52rpx',
        textAlign: 'center',
        lineHeight: '52rpx',
        borderRadius: '8rpx'
      }
    }
  },
  computed: {
    ...mapGetters(['color'])
  },
  components: { ImagePreview, RecordHistoryList, MealRecordAdd },

  created() {
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      // 获取数据
      console.log("this.parmas", this.parmas);
      this.parmas.page = this.pageNo
      this.parmas.page_size = this.pageSize
      this.parmas.supplier_manage_id = '57'
      this.getOrderList(this.parmas)
    },
    /**
     * 切换菜单
     * @param {*} data
     */
    switchMenu(data) {
      console.log("switchMenu", data);
      const type = data.type
      this.currentIndex = data.index
      if (type === "history") {
        // this.getOrderList()
      }
    },

    /**
     * 点击搜索
     */
    handlerOrderSearch(e) {
      console.log("handlerOrderSearch");
      if (e && e.length) {
        // 根据用户输入的内容重新获取列表
        this.parmas.name = e
        this.pageNo = 1
        this.initData()
      }
    },
    /**
     * 搜索清除
     */
    searchClear() {
      console.log("searchClear");
      delete this.parmas.name
      this.pageNo = 1
      this.initData()
    },
    // 供应商名称
    getSupplieName(item) {
      if (!item) {
        return ""
      }
      let qrCode = item.qr_code || {}
      let supplierManageName = qrCode.supplier_manage_name || ''
      return supplierManageName
    },
    // 根据key返回值
    getSupplieByType(item, key) {
      if (!item) {
        return ""
      }
      let qrCode = item.qr_code || {}
      let keyName = qrCode[key] || ''
      if (key === 'ref_unit_price') {
        keyName = divide(keyName)
      }
      return keyName
    },
    // 展示图片
    handlerShowImg(imgList, index) {
      if (!imgList || imgList.length === 0) {
        return this.$u.toast('暂无预览图片')
      }
      if (this.$refs.imgPreview) {
        this.$refs.imgPreview.setImgList(imgList, index)
        this.$refs.imgPreview.showDialog()
      }
    },
    // 保存成功
    saveConfirm(value) {
      if (value) {
        this.currentIndex = 1
      }
    }
  }

}
</script>

<style lang="scss" scoped>
.meal_management_container {
  .menu-layout {
    width: 100%;
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  .horizontal_cell_line {
    width: 610rpx;
    height: 1rpx;
    background-color: #eae9ed;
    margin: 20rpx auto;
  }

}
</style>

<template>
  <view class="accompanying_photo">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar :title="$t('page.meal.take.photo')" placeholder :autoBack="true" :leftIconColor="color.navigation"
      leftIconSize="37rpx" :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"></u-navbar>
    <!--#endif-->
    <view class="img-tag mp-bottom-high">
      <canvas canvas-id="watermarkCanvas" class="img-tag mp-bottom-high" ></canvas>
    </view>
    <!--底部按钮-->
    <!--#ifdef H5 -->
    <view class="btn-layout flex">
      <u-button :customStyle="customStyleBtnSamll" class="approval_btn_style small"
        :color="color.colorBtnBlack" @click="handlerRetry">重新拍照</u-button>
      <u-button :customStyle="customStyleBtnSamll" class="approval_btn_style small"
        :color="color.colorBtnOrange" @click="handlerConfirm">确认</u-button>
    </view>
    <!--#endif-->
    <!--#ifdef MP-WEIXIN -->
    <cover-view class="btn-layout flex row-around" >
      <cover-view class="reset-btn m-t-20"  @click="handlerRetry" >重新拍照</cover-view>
      <cover-view class="comfirm-btn m-t-20" @click="handlerConfirm">确认</cover-view>
    </cover-view>
    <!--#endif-->
  </view>
</template>
<script>
import { mapGetters } from 'vuex'
import Cache from '@/utils/cache';
import { takePhoto, addWatermark, saveImgByCanvas } from './utils.js'

export default {
  data() {
    return {
      imgPath: this.$imgPath,
      customStyleBtnSamll: {
        width: '320rpx',
        height: '70rpx',
        borderRadius: '8rpx',
        fontSize: '28rpx',
        margin: '19rpx  40rpx'
      },
      imgPhoto: '',
      height: uni.getSystemInfoSync().windowHeight - uni.upx2px(118),
      orgName: ''
    }
  },
  computed: {
    ...mapGetters(['color'])
  },
  created() {
    this.initData()
  },
  onLoad() {
  },
  onShow() {
  },
  methods: {
    initData() {
      console.log("拍照页面加载");
      let imgUrl = Cache.get(this.$common.KEY_ACCOMPANYING_TAKE_IMG)
      this.orgName = Cache.get(this.$common.KEY_ACCOMPANYING_TAKE_IMG_NAME)
      if (imgUrl) {
        this.imgPhoto = imgUrl
        this.sighPhoto(imgUrl)
      }
    },
    // 重拍
    async handlerRetry() {
      let imgUrl = await takePhoto()
      Cache.set(this.$common.KEY_ACCOMPANYING_TAKE_IMG, imgUrl)
      this.imgPhoto = imgUrl
      this.sighPhoto(imgUrl)
    },
    sighPhoto(imgUrl) {
      let ctx = uni.createCanvasContext('watermarkCanvas', this)
      addWatermark(ctx, imgUrl, this.orgName)
    },
    // 确认
    handlerConfirm() {
      saveImgByCanvas('watermarkCanvas', this)
    }
  }
}

</script>
<style lang="scss" scoped>
.accompanying_photo {
  position: relative;
  height: 100vh;

  .img-tag {
    width: 100%;
    height: calc(100vh  - 206rpx);
  }
  // #ifdef MP-WEIXIN
  .mp-bottom-high {
    height: calc(100vh  - 246rpx) !important;
  }
  // #endif

  .btn-layout {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    min-height: 118rpx;
    background-color: #fff;
    z-index: 9999;

    .approval_btn_style {
      width: 670rpx;
      height: 70rpx;
      border-radius: 8rpx;
      font-size: 28rpx;
      margin: 19rpx 40rpx;
    }

    .small {
      width: 320rpx !important;
    }
    .reset-btn {
      width: 320rpx;
      height: 70rpx;
      line-height: 70rpx;
      border-radius: 8rpx;
      font-size: 28rpx;
      text-align: center;
      background-color: #000;
      color: #fff;
    }
    .reset-btn:active {
      opacity: 0.8;
    }
    .comfirm-btn {
      width: 320rpx;
      height: 70rpx;
      line-height: 70rpx;
      border-radius: 8rpx;
      font-size: 28rpx;
      text-align: center;
      background-color: #FD953C;
      color: #fff;
    }
    .comfirm-btn:active {
      opacity: 0.8;
    }
  }
}
</style>

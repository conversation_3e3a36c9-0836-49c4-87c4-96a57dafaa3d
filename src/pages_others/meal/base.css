.base-layout {
  width: 670rpx;
  min-height: 60rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 20rpx auto;
  padding: 30rpx;
}

.base-bg-gray {
  width: 610rpx;
  min-height: 64rpx;
  background-color: #eff1f6;
  border-radius: 8rpx;
}

.base-bg-orange {
  min-width: 110rpx;
  height: 44rpx;
  line-height: 44rpx;
  text-align: center;
  background-color: #FFF4EB;
  border-radius: 6rpx;
  color: #fd953c;
}
.base-bg-border-dash {
  width: 610rpx;
	min-height: 80rpx;
	border-radius: 8rpx;
	border: dashed 1px #d7d7d7;
}
.base-bg-border-dash:active {
  background-color: #eff1f6;
}

.title {
  font-size: 24rpx;
  color: #8f9295;
}

.right-txt {
  max-width: 450rpx;
  word-break: break-word;

}
.right-break-text {
  white-space: pre-wrap;
  word-break: break-word;
}
.suggest-tag {
  width: 240rpx;
  display: inline-block;
}

.orange-xing ::after {
  content: "*";
  color: #fd953c;
  margin-left: 10rpx;
  position: relative;
  top: 5rpx;
  font-size: 24rpx;
}
.right-delete {
  width: 36rpx;
  height: 36rpx;
}
.add-img {
  width: 28rpx;
	height: 28rpx;
}

.vertial_cell_line {
  width: 1px;
  height: 28rpx;
  background-color: #eae9ed;
  margin-left:20rpx;
}
.horizontal_cell_line {
  width: 610rpx;
  height: 1rpx;
  background-color: #eae9ed;
  margin: 20rpx auto;
}
.image-pic {
  width: 152rpx;
  height: 152rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
}
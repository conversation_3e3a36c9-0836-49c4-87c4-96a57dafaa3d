<template>
  <u-overlay :show="isShowDialog" catchtouchmove="true">
    <view class="add-good-container" @tap.stop>
      <view class="custom_dialog_item">
        <view class="title-txt p-t-40 p-b-40">{{ dialogTitle }}</view>
        <!--筛选-->
        <view v-if="type === 'default'">
          <view>
            <search-component searchPlaceholder="请输入菜品名称" @searchChange="searchChange"
              @handlerSearch="handlerUserSearch" type="search_good" width="610rpx" ref='searchComponentRef'></search-component>
          </view>
          <!--菜品列表-->
          <view class="table-header-tag flex col-center ">
            <view class="header-item w-400">菜品名称</view>
            <view class="header-item w-200">规格</view>
            <view class="header-item w-200">价格</view>
          </view>
          <view class="table-main">
            <view :class="['table-body-tag', 'flex', 'col-center', 'm-t-20', item.checked ? 'active' : 'de-active']"
              v-for="(item, index) in goodsList" :key="index" @click="handlerCheckout(item, index)">
              <view class="header-item w-400">{{ item.food_name }}</view>
              <view class="header-item w-200">{{ item.food_spec_name }}</view>
              <view class="header-item w-200">{{ "¥" + item.price + '/份' }}</view>
            </view>
          </view>
        </view>
        <!--菜品直观评价-->
        <view v-if="type !== 'default'">
          <view class="table-header-tag flex col-center ">
            <view class="header-item w-400">{{ chooseGood.food_name }}</view>
            <view class="header-item w-200">{{ chooseGood.food_spec_name }}</view>
            <view class="header-item w-200">{{ "¥" + chooseGood.price + '/份' }}</view>
          </view>
          <view class="table-main">
            <view class="flex">
              <view class="tag-title">感观评价</view>
              <htz-rate v-model="chooseGood.lookFeel" :size="60" :gutter="20" :type="1" :count="3"></htz-rate>
              <view class="tag-right">{{ getRateNameByType(chooseGood.lookFeel, 'quality_type', 'lookFeelName') }}
              </view>
            </view>
            <view class="flex">
              <view class="tag-title">质量评价</view>
              <htz-rate v-model="chooseGood.quality" :size="60" :gutter="20" :type="1" :count="3"></htz-rate>
              <view class="tag-right">{{ getRateNameByType(chooseGood.quality, 'quality_type', 'qualityName') }}</view>
            </view>
            <view class="flex">
              <view class="tag-title">份量评价</view>
              <htz-rate v-model="chooseGood.amount" :size="60" :gutter="20" :type="1" :count="3"></htz-rate>
              <view class="tag-right">{{ getRateNameByType(chooseGood.amount, 'amount_type', 'amountName') }}</view>
            </view>
            <view class="flex">
              <view class="tag-title">价格评价</view>
              <htz-rate v-model="chooseGood.priceType" :size="60" :gutter="20" :type="1" :count="3"></htz-rate>
              <view class="tag-right">{{ getRateNameByType(chooseGood.priceType, 'price_type', 'priceTypeName') }}
              </view>
            </view>
            <view>
              <view class="title m-t-30 m-l-40 orange-xing">
                <view>学生的意见和建议</view>
              </view>
              <view class="m-l-40 m-r-40 m-t-40">
                <u-textarea v-model="chooseGood.remark" confirmType="done" placeholder="请输入文字" height="200rpx"
                ref="textarea"  maxlength="50" count></u-textarea>
              </view>
            </view>
          </view>
        </view>
        <view class="horizontal_cell_line"></view>
        <view class="custom-dialog-btns">
          <u-button :customStyle="customStyleBtn" plain :class="['custom-dialog-btn-style-btns', 'borderRadiusLeft']"
            :color="color.colorBtnGray" @click="handlerCustomCancel()">{{ cancelBtnTxt }}</u-button>
          <view class="vertical_cell_line"></view>
          <u-button plain :customStyle="customStyleBtn" :class="['custom-dialog-btn-style-btns', 'borderRadiusRight']"
            :color="color.colorBtnOrange" @click="handlerCustomConfirm()">{{ confirmBtnTxt }}</u-button>
        </view>
      </view>
      <!--图片预览-->
      <image-preview ref="imgDialogPreview"></image-preview>
    </view>
  </u-overlay>
</template>

<script>
import { mapGetters } from 'vuex'
// import Cache from '@/utils/cache';
import ImagePreview from '@/components/ImagePreview/ImagePreview.vue';
import SearchComponent from "@/components/SearchComponent/SearchComponent"
import { apiMerchantMobileMealAccompanyingGetFoodListPost } from '@/api/meal'
import { formateDate } from "@/utils/time.js"
import { divide, deepClone } from "@/utils/util"
import htzRate from '@/components/htz-rate/htz-rate.vue'
import { QUALITY_TYPE_LIST, AMOUNT_TYPE_LIST, PRICE_TYPE_LIST } from '../constants'
export default {
  props: {
    confirmBtnTxt: { // 按钮确认名字
      type: String,
      default: '确认'
    },
    cancelBtnTxt: { // 按钮取消名字
      type: String,
      default: "取消"
    },
    remark: {
      type: String,
      default: ""
    },
    title: { // 标题
      type: String,
      default: '请选择菜品'
    },
    mealType: { // 餐段
      type: String,
      default: ''
    },
    orgId: { // 组织id
      type: Number,
      default: 0
    },
    chooseList: { // 选择的菜品列表
      type: Array,
      default: () => {
        return []
      }
    }
  },
  name: "AddGoodDialog",
  components: {
    ImagePreview,
    SearchComponent,
    htzRate
  },
  data() {
    return {
      isShowDialog: false, // 是否显示Diglog
      customStyleBtn: {
        flex: 1,
        width: '100%',
        height: '90rpx',
        fontSize: '36rpx',
        border: 'none !important',
        padding: '0 !important',
        borderRadius: ' 0 0 20rpx 20rpx'
      },
      searchValue: '', // 搜索值
      goodsList: [], // 菜品列表
      checkOldIndex: -1, // 记录上次选中的索引
      checkNewIndex: -1, // 记录本次选中的索引
      type: 'default', // 类型
      dialogTitle: this.title, // 标题
      chooseGood: {
        lookFeel: 0,
        lookFeelName: '',
        quality: 0,
        qualityName: '',
        amount: 0,
        amountName: '',
        priceType: 0,
        priceTypeName: '',
        remark: '',
        typeValueList: []
      } // 选择商品
    };
  },
  created() {
  },
  computed: {
    ...mapGetters(['color'])
  },

  methods: {
    /**
     * 显示弹窗
     */
    showDialog() {
      console.log("showAddGoodDialog");
      this.isShowDialog = true
      this.getGoodsList(this.searchValue)
    },
    /**
     * 隐藏弹窗
     */
    hideDialog() {
      console.log("hideAddGoodDialog");
      this.resetData()
      this.isShowDialog = false
    },
    // 取消
    handlerCustomCancel() {
      this.$emit('cancel')
      this.resetData()
      this.isShowDialog = false
    },
    // 重置
    resetData() {
      this.type = 'default'
      this.dialogTitle = '请选择菜品'
      this.checkOldIndex = -1 // 记录上次选中的索引
      this.checkNewIndex = -1
      this.goodsList = []
      this.chooseGood = {
        lookFeel: 0,
        lookFeelName: '',
        quality: 0,
        qualityName: '',
        amount: 0,
        amountName: '',
        priceType: 0,
        priceTypeName: '',
        remark: '',
        typeValueList: []
      }
      if (this.$refs.searchComponentRef) {
        this.$refs.searchComponentRef.handlerClear()
      }
    },
    // 确认
    handlerCustomConfirm() {
      if (this.type === 'food') {
        if (!this.chooseGood.remark || !this.chooseGood.lookFeel || !this.chooseGood.quality || !this.chooseGood.amount || !this.chooseGood.priceType) {
          return this.$u.toast("请填写完整")
        }
        let typeValueList = [
          {
            type: 'lookFeel',
            name: this.chooseGood.lookFeelName,
            score: this.chooseGood.lookFeel
          },
          {
            type: 'quality',
            name: this.chooseGood.qualityName,
            score: this.chooseGood.quality
          },
          {
            type: 'amount',
            name: this.chooseGood.amountName,
            score: this.chooseGood.amount
          },
          {
            type: 'priceType',
            name: this.chooseGood.priceTypeName,
            score: this.chooseGood.priceType
          }
        ]
        this.chooseGood.typeValueList = typeValueList
        this.$emit('confirm', deepClone(this.chooseGood))
        this.resetData()
        this.isShowDialog = false
      } else
      if (this.type === 'default') {
        if (this.checkNewIndex === -1) {
          return this.$u.toast("请选择菜品")
        }
        let findGood = this.chooseList.find(item => item.food_id === this.chooseGood.food_id)
        console.log("findGood", findGood, this.chooseGood);
        if (findGood) {
          this.$u.toast("该菜品已评价")
          return
        }
        this.type = 'food'
        this.dialogTitle = '菜品直观评价'
      }
    },
    // 展示图片
    handlerShowImg(imgList, index) {
      if (this.$refs.imgDialogPreview) {
        this.$refs.imgDialogPreview.setImgList(imgList, index)
        this.$refs.imgDialogPreview.showDialog()
      }
    },

    // 搜索改变
    searchChange() {
    },
    // 点击搜索
    handlerUserSearch(value) {
      console.log("handlerUserSearch", value);
      this.getGoodsList(value)
    },
    // 获取产品列表
    async getGoodsList(searchValue) {
      let params = {
        date: formateDate(new Date()),
        meal_type: this.mealType,
        org_id: this.orgId
      }
      if (searchValue) {
        params.name = searchValue
      }
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      const [error, res] = await this.$to(apiMerchantMobileMealAccompanyingGetFoodListPost(params))
      uni.hideLoading()
      if (error) {
        return
      }
      if (res && res.code === 0) {
        let data = res.data || {}
        console.log("data", data);
        let foodData = data.food_data || []
        let setMealData = data.set_meal_data || []
        if (foodData && foodData.length > 0) {
          foodData.forEach(item => {
            item.price = divide(item.price || 0)
            const foodSpec = item.food_spec || []
            item.food_spec_name = foodSpec && foodSpec.length > 0 ? foodSpec[0].name : ''
          })
        }
        if (setMealData && setMealData.length > 0) {
          setMealData.forEach(item => {
            item.food_name = item.set_meal_name
            item.food_id = item.set_meal_id
            item.price = divide(item.price || 0)
            item.food_spec_name = "套餐"
          })
        }
        this.goodsList = foodData.concat(setMealData)
      }
    },
    // 设置选中
    handlerCheckout(item, index) {
      this.checkOldIndex = parseInt(this.checkNewIndex)
      if (this.checkOldIndex !== -1) {
        this.$set(this.goodsList[this.checkOldIndex], 'checked', false)
      }
      this.checkNewIndex = parseInt(index)
      this.$set(this.goodsList[this.checkNewIndex], 'checked', true)
      item.remark = ''
      this.chooseGood = deepClone(item)
    },
    // 根据类型获取评价名称 质量 quality_type
    getRateNameByType(value, type, typeName) {
      let str = ''
      const listQ = deepClone(QUALITY_TYPE_LIST);
      const listA = deepClone(AMOUNT_TYPE_LIST);
      const listP = deepClone(PRICE_TYPE_LIST);
      let list = []
      switch (type) {
        case 'quality_type':
          list = listQ
          break;
        case 'amount_type':
          list = listA
          break;
        case 'price_type':
          list = listP
          break;
        default:
          break;
      }
      let findItem = list.find(item => {
        return item.value === value
      })
      str = findItem ? findItem.name : ''
      if (typeName && findItem) {
        this.$set(this.chooseGood, typeName, str)
        this.$set(this.chooseGood, typeName + "Key", findItem.label)
      }
      return str
    }
  }
}
</script>

<style lang='scss' scoped>
.add-good-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.title-txt {
  font-size: 36rpx;
  color: #1d1e20;
  text-align: center;
}

.custom_dialog_item {
  width: 687rpx;
  min-height: 100rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
}

.horizontal_cell_line {
  width: 100%;
  height: 1px;
  background-color: #e5e5e5;
  margin-top: 45rpx;
}

.custom-dialog-btns {
  display: flex;
  align-items: center;
  flex-grow: 2;

  .custom-dialog-btn-style-btns {
    flex: 1;
    width: 100%;
    height: 90rpx;
    font-size: 36rpx;
    border: none !important;
    padding: 0 !important;
    border-radius: 0 0 20rpx 20rpx;
  }

  .borderRadiusLeft {
    border-radius: 0 0 0 20rpx;
  }

  .borderRadiusRight {
    border-radius: 0 0 20rpx 0;
  }

  .vertical_cell_line {
    width: 1px;
    height: 90rpx;
    background-color: #e5e5e5;
  }
}

.color-gray {
  color: #8f9295;
}

.color-green {
  color: #51d854;
}

.w-400 {
  width: 400rpx;
}

.w-200 {
  width: 200rpx;
}

.table-main {
  max-height: 600rpx;
  overflow-y: auto;
}

.table-header-tag {
  width: 610rpx;
  min-height: 64rpx;
  margin: 0 auto;
  background-color: #eff1f6;
  border-radius: 8rpx;
  padding: 20rpx;

  .header-item {
    color: #8f9295;
    font-size: 24rpx;
  }

}

.table-body-tag {
  width: 610rpx;
  min-height: 64rpx;
  margin: 20rpx auto;
  padding: 0 20rpx;
  border-radius: 8rpx;
}

.active {
  background-color: #fd953c;
  color: #ffffff;
}

.de-active {
  background-color: #ffffff;
  color: #1d1e20;
  border: solid 1px #dbdada;
}

.tag-title {
  width: 260rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
}

.tag-right {
  width: 150rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
}

.orange-xing ::after {
  content: "*";
  color: #fd953c;
  margin-left: 10rpx;
  position: relative;
  top: 5rpx;
  font-size: 24rpx;
}
</style>

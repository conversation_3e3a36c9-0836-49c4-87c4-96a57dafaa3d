<template>
  <!--新增陪餐记录-->
  <view class="add_meal_container">
    <!--陪餐信息-->
    <view class="base-layout">
      <view class="flex row-between">
        <view class="nr gray-light">陪餐时间</view>
        <view class="nr gray-light right-txt line-1">{{ formData.meal_date }}</view>
      </view>
      <view class="flex row-between m-t-20">
        <view class="nr gray-light orange-xing">
          <view>陪餐餐段</view>
        </view>
        <view class="nr black right-txt line-1 flex col-center"
          @click="handlerShowActiveSheet('meal_type', formData.meal_type)">
          <view>{{ formData.meal_type_name ? formData.meal_type_name : '请选择' }}</view>
          <view><u-icon name="arrow-right" size="22"></u-icon></view>
        </view>
      </view>
      <view class="flex row-between m-t-20">
        <view class="nr gray-light orange-xing">
          <view>就餐食堂</view>
        </view>
        <view class="nr black right-txt line-1 flex col-center"
          @click="handlerShowActiveSheet('org_name', formData.org_name)">
          <view>{{ formData.org_name ? formData.org_name : '请选择' }}</view>
          <view><u-icon name="arrow-right" size="22"></u-icon></view>
        </view>
      </view>
    </view>
    <!--陪餐人信息-->
    <view class="title m-t-30 m-l-40 orange-xing">
      <view>陪餐人信息（{{ personList.length }}人）</view>
    </view>
    <view class="base-layout">
      <view v-for="(personItem, personItemIndex) in personList" :key="personItemIndex">
        <view class="flex row-between">
          <!--陪餐人选择-->
          <view class="nr gray-light flex col-center">
            <view @click="handlerShowActiveSheet('person', formData.person_records, personItemIndex)">{{ personItem.type
              ? personItem.type : '请选择' }}</view>
            <view><u-icon name="arrow-down" size="22"></u-icon></view>
            <view class="vertial_cell_line"></view>
          </view>
          <!--陪餐人-->
          <view class="nr black right-txt line-1  flex col-center">
            <view><u-input v-model="personItem.name" border="bottom" placeholder="请输入陪餐人姓名" maxlength="10"></u-input></view>
            <view class="m-l-20"><img :src="imgPath.IMG_IC_DELETE_RED" class="right-delete"
                @click="handlerDeletePerson(personItemIndex)" /></view>
          </view>
        </view>
        <view class="horizontal_cell_line"></view>
      </view>
      <view class="base-bg-border-dash m-t-20 flex flex-center" @click="handlerAddPerson">
        <view class="flex col-center"><img :src="imgPath.IMG_OTHER_ADD_ORANGE" class="add-img" />
          <view class="yellow nr m-l-20">添加陪餐人</view>
        </view>
      </view>
    </view>

    <!--陪餐人信息-->
    <view class="title m-t-30 m-l-40 orange-xing">
      <view>食堂环境/工作情况评价</view>
    </view>
    <view class="base-layout">
      <view class="flex row-between">
        <view class="nr gray-light">
          <view>备餐区</view>
        </view>
        <view class="nr black right-txt line-1 flex col-center"
          @click="handlerShowActiveSheet('room_clean_type', formData.room_clean_type)">
          <view>{{ formData.room_clean_type_name ? formData.room_clean_type_name : '请选择' }}</view>
          <view><u-icon name="arrow-right" size="22"></u-icon></view>
        </view>
      </view>
      <view class="flex row-between m-t-20">
        <view class="nr gray-light">
          <view>服务态度</view>
        </view>
        <view class="nr black right-txt line-1 flex col-center"
          @click="handlerShowActiveSheet('room_attitude_type', formData.room_attitude_type)">
          <view>{{ formData.room_attitude_type_name ? formData.room_attitude_type_name : '请选择' }}</view>
          <view><u-icon name="arrow-right" size="22"></u-icon></view>
        </view>
      </view>
      <view class="horizontal_cell_line"></view>
      <view class="flex row-between m-t-20">
        <view class="nr gray-light">
          <view>就餐区</view>
        </view>
        <view class="nr black right-txt line-1 flex col-center"
          @click="handlerShowActiveSheet('area_clean_type', formData.area_clean_type)">
          <view>{{ formData.area_clean_type_name ? formData.area_clean_type_name : '请选择' }}</view>
          <view><u-icon name="arrow-right" size="22"></u-icon></view>
        </view>
      </view>
      <view class="flex row-between m-t-20">
        <view class="nr gray-light">
          <view>餐饮浪费情况</view>
        </view>
        <view class="nr black right-txt line-1 flex col-center"
          @click="handlerShowActiveSheet('area_waste_type', formData.area_waste_type)">
          <view>{{ formData.area_waste_type_name ? formData.area_waste_type_name : '请选择' }}</view>
          <view><u-icon name="arrow-right" size="22"></u-icon></view>
        </view>
      </view>
      <view class="horizontal_cell_line"></view>
      <view class="flex row-between m-t-20">
        <view class="nr gray-light">
          <view>其他加工操作区</view>
        </view>
        <view class="nr black right-txt line-1 flex col-center"
          @click="handlerShowActiveSheet('oa_clean_type', formData.oa_clean_type)">
          <view>{{ formData.oa_clean_type_name ? formData.oa_clean_type_name : '请选择' }}</view>
          <view><u-icon name="arrow-right" size="22"></u-icon></view>
        </view>
      </view>
      <view class="flex row-between m-t-20">
        <view class="nr gray-light">
          <view>规范</view>
        </view>
        <view class="nr black right-txt line-1 flex col-center"
          @click="handlerShowActiveSheet('oa_operate_type', formData.oa_operate_type)">
          <view>{{ formData.oa_operate_type_name ? formData.oa_operate_type_name : '请选择' }}</view>
          <view><u-icon name="arrow-right" size="22"></u-icon></view>
        </view>
      </view>
      <view class="horizontal_cell_line"></view>
      <view class="flex row-between m-t-20">
        <view class="nr gray-light">
          <view>餐具消杀区</view>
        </view>
        <view class="nr black right-txt line-1 flex col-center"
          @click="handlerShowActiveSheet('tda_clean_type', formData.tda_clean_type)">
          <view>{{ formData.tda_clean_type_name ? formData.tda_clean_type_name : '请选择' }}</view>
          <view><u-icon name="arrow-right" size="22"></u-icon></view>
        </view>
      </view>
      <view class="flex row-between m-t-20">
        <view class="nr gray-light">
          <view>消杀情况</view>
        </view>
        <view class="nr black right-txt line-1 flex col-center"
          @click="handlerShowActiveSheet('tda_disinfection_type', formData.tda_disinfection_type)">
          <view>{{ formData.tda_disinfection_type_name ? formData.tda_disinfection_type_name : '请选择' }}</view>
          <view><u-icon name="arrow-right" size="22"></u-icon></view>
        </view>
      </view>
      <view class="horizontal_cell_line"></view>
      <view class="flex row-between m-t-20">
        <view class="nr gray-light">
          <view>明厨亮灶运作</view>
        </view>
        <view class="nr black right-txt line-1 flex col-center"
          @click="handlerShowActiveSheet('operation_type', formData.operation_type)">
          <view>{{ formData.operation_type_name ? formData.operation_type_name : '请选择' }}</view>
          <view><u-icon name="arrow-right" size="22"></u-icon></view>
        </view>
      </view>
    </view>
    <!--菜品评价-->
    <view class="title m-t-30 m-l-40 orange-xing">
      <view>菜品评价</view>
    </view>
    <view class="base-layout">
      <view class="base-bg-gray flex row-between col-center p-l-20 p-r-20">
        <view v-for="(typeName, typeNameIndex) in typeTitleList" :key="typeNameIndex" class="title">
          {{ typeName.name }}
        </view>
      </view>
      <view v-for="(goodItem, goodItemIndex) in goodList" :key="goodItemIndex" class="">
        <view class="flex m-t-20 row-between">
          <view>
            <view class="nr">{{ goodItem.food_name }} <span class="m-l-10">¥{{ goodItem.price }}/份</span></view>
          </view>
          <view>
            <img :src="imgPath.IMG_IC_DELETE_RED_LAJI" class="icon-md" @click="handlerDeleteGood(goodItemIndex)" />
          </view>
        </view>
        <view class="flex row-between m-t-20">
          <view class="base-bg-orange nr" v-for="(typeValue, typeValueIndex) in goodItem.typeValueList"
            :key="typeValueIndex">
            {{ typeValue.name }}
          </view>
        </view>
        <view class="base-bg-gray nr p-20 m-t-20 ">
          <span class="gray-light suggest-tag">学生意见和建议：</span>
          <view class="ps-inline right-break-text">{{ goodItem.remark }}</view>
        </view>
      </view>
      <view class="base-bg-border-dash m-t-20 flex flex-center" @click="handlerAddGood">
        <view class="flex col-center">
          <view class="yellow nr m-r-20">请选择菜品</view><img :src="imgPath.IMG_IC_ORANGE_ARROW_DOWN" class="add-img" />
        </view>
      </view>
    </view>
    <!--陪餐图片-->
    <view class="title m-t-30 m-l-40 orange-xing">
      <view>陪餐图片</view>
    </view>
    <view class="flex flex-wrap m-l-40">
      <view :class="['pic-layout', 'm-t-20', index > 0 ? 'm-l-20' : '']" v-for="(item, index) in imgList" :key="index">
        <img :src="item" class="image-pic" @click="handlerShowImg(imgList, index)" />
        <img :src="imgPath.IMG_IC_DELETE_BLACK" class="icon-40 delete-tag" @click="handlerDeletePic(index)" />
      </view>
      <view class="m-t-20 m-l-20" @click="handlerAddPic">
        <img :src="imgPath.IMG_IC_CAMERA_KUANG" class="image-pic" />
      </view>
    </view>
    <!--陪餐员签字-->
    <view class="title m-t-30 m-l-40 orange-xing">
      <view>陪餐员签字</view>
    </view>
    <view class="base-layout flex flex-center">
      <view class="base-bg-border-dash flex flex-col col-center" @click="handlerGoSign">
        <view class="m-t-20" v-if="!showSignImg"><img :src="imgPath.IMG_OTHER_ADD_ORANGE" class="icon-xxl" />
        </view>
        <view class="yellow nr m-b-20" v-if="!showSignImg">点击签名</view>
        <!-- #ifdef H5 -->
        <img :src="signImg" v-if="showSignImg" class="image-sign" />
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN -->
        <image :src="signImg" v-if="showSignImg" class="image-sign-weixin"> </image>
        <!-- #endif -->
      </view>
    </view>
    <!--备注-->
    <view class="title m-t-30 m-l-40">备注</view>
    <view class="base-layout">
      <u-textarea v-model="formData.remark" confirmType="done" placeholder="请输入文字" height="280rpx" ref="textarea"
        maxlength="100" count></u-textarea>
    </view>
    <view class="btn-layout">
      <u-button :customStyle="customStyleBtn" class="approval_btn_style" plain :color="color.colorBtnOrange"
        @click="handlerCancel">{{ $t('page.btn.cancel') }}</u-button>
      <u-button :customStyle="customStyleBtn" class="approval_btn_style" :disabled="isBtnDisable"
        :color="color.colorBtnOrange" @click="handlerNext">{{ $t('index.Confirm') }}</u-button>
    </view>
    <!--底部弹出框-->
    <!-- #ifdef H5 -->
    <u-action-sheet :actions="dicList" @select="selectClick" :title="title" :show="showActiveSheet"
      @close="closeActiveSheet"></u-action-sheet>
    <!-- #endif -->
    <!-- #ifdef MP-WEIXIN -->
     <u-action-sheet  :closeOnClickOverlay="true" :closeOnClickAction="true"  :title="title" :show="showActiveSheet" @close="closeActiveSheet" >
       <!--底部弹出框-->
       <scroll-view scroll-y show-scrollbar :style="{ minHeight:'10vh', maxHeight:'50vh', height: dicList.length * 82 + 'rpx'  }">
          <view class ='' v-for="(itemDic,indexDic) in dicList" :key="indexDic" @click="selectClick(itemDic)">
            <view class="lg m-b-20 m-l-20 m-t-20 text-center ">{{itemDic.name}}</view>
            <view class="horizontal_cell_line" v-if="indexDic < dicList.length - 1"></view>
          </view>
        </scroll-view>
      </u-action-sheet>
      <!-- #endif -->
    <add-good-dialog ref="addGoodDialog" :confirmBtnTxt="$t('page.btn.confirm')" :cancelBtnTxt="$t('page.btn.cancel')"
      :meal-type='formData.meal_type' :org-id='formData.org_id' :remark="$t('page.remark')"
      @confirm="handlerConfirmAddGood" @cancel="handlerCancelAddGood" :choose-list="goodList"></add-good-dialog>
    <!--图片预览-->
    <image-preview ref="imgPreview"></image-preview>
  </view>
</template>
<script>
import { formateDate } from "@/utils/time.js"
import { mapGetters } from 'vuex'
import comDic from '@/common/comDic.js'
import { deepClone } from "@/utils/util.js"
import { getOrgsList, saveRecord, takePhoto } from '../utils.js'
import Cache from '@/utils/cache';
import { PERSON_TYPE_LIST, CLEAN_TYPE_LIST, ATTITUDE_TYPE_LIST, WASTE_TYPE_LIST, OPERATE_TYPE_LIST, DISINFECTION_TYPE_LIST, OPERATION_TYPE_LIST } from '../constants.js'
import AddGoodDialog from './AddGoodDialog.vue'
import ImagePreview from '@/components/ImagePreview/ImagePreview.vue';

export default {
  data() {
    return {
      imgPath: this.$imgPath,
      formData: {
        meal_date: '',
        meal_type: '',
        meal_type_name: '', // 餐段
        meal_name: '',
        remark: '',
        org_name: '',
        org_id: -1,
        room_clean_type: '', //  备餐间(干净类型)
        room_clean_type_name: '', // 备餐间(干净类型) 名字
        room_attitude_type: '', // 备餐间(服务态度)
        room_attitude_type_name: '', // 备餐间(服务态度) 名字
        area_clean_type: '', // 就餐区(干净类型)
        area_clean_type_name: '', // 就餐区(干净类型) 名字
        area_waste_type: '', // 就餐区(饮食浪费情况)
        area_waste_type_name: '', // 就餐区(饮食浪费情况) 名字
        oa_clean_type: '', // 操作区(干净类型)
        oa_clean_type_name: '', // 操作区(干净类型) 名字
        oa_operate_type: '', // 操作区(操作规范类型)
        oa_operate_type_name: '', // 操作区(操作规范类型) 名字
        tda_clean_type: '', // 餐具消杀区(干净类型)
        tda_clean_type_name: '', // 餐具消杀区(干净类型) 名字
        tda_disinfection_type: '', // 餐具消杀区(消杀类型)
        tda_disinfection_type_name: '', // 餐具消杀区(消杀类型) 名字
        operation_type: '', // 明厨亮灶运作(运作类型)
        operation_type_name: '' // 明厨亮灶运作(运作类型) 名字
      },
      typeTitleList: [
        {
          name: '感观评价'
        },
        {
          name: '质量评价'
        },
        {
          name: '份量评价'
        },
        {
          name: '价格评价'
        }
      ],
      typeValueList: [
        {
          name: '优秀'
        },
        {
          name: '良好'
        },
        {
          name: '较多'
        },
        {
          name: '偏高'
        }
      ],
      goodList: [],
      showSignImg: false, // 是否显示签到图片
      signImg: '', // 签到图片
      imgList: [],
      customStyleBtn: {
        width: '320rpx',
        height: '70rpx',
        borderRadius: '8rpx',
        fontSize: '28rpx',
        margin: '19rpx  40rpx'
      },
      isBtnDisable: false,
      orgsList: [], // 组织列表
      dicList: [], // 弹出框列表
      title: '请选择菜品', // 弹出框标题
      showActiveSheet: false, // 弹出框是否显示
      dicType: '', // 弹出框类型
      dicPositionIndex: '', // 弹出框位置索引
      personList: [] // 陪餐人列表
    }
  },
  components: {
    AddGoodDialog, ImagePreview
  },
  computed: {
    ...mapGetters(['color'])
  },
  created() {
    this.initData()
    this.emitMessage()
  },
  destroyed() {
    uni.$off(this.$common.KEY_ACCOMPANYING_SIGN_IMG)
    uni.$off(this.$common.KEY_ACCOMPANYING_TAKE_IMG_LIST)
    Cache.remove(this.$common.KEY_ACCOMPANYING_TAKE_IMG_LIST)
  },
  methods: {
    /**
     * 初始化数据
     */
    async initData() {
      // 获取当前日期
      let mealTime = formateDate(new Date())
      console.log("mealTime", mealTime);
      this.$set(this.formData, 'meal_date', mealTime)
      let orgsId = Cache.get(this.$common.KEY_USER_ORGAN)
      this.orgsList = await getOrgsList(orgsId)
    },
    // 监听消息
    emitMessage() {
      var that = this
      uni.$on(this.$common.KEY_ACCOMPANYING_SIGN_IMG, (data) => {
        console.log("KEY_ACCOMPANYING_SIGN_IMG", data)
        that.signImg = data
        that.showSignImg = true
      })
      uni.$on(this.$common.KEY_ACCOMPANYING_TAKE_IMG_LIST, (dataList) => {
        console.log("KEY_ACCOMPANYING_TAKE_IMG_LIST", dataList);
        let list = Cache.get(this.$common.KEY_ACCOMPANYING_TAKE_IMG_LIST) || []
        that.imgList = list
      })
    },
    // 取消
    handlerCancel() {
      this.$Router.back()
    },
    /**
     * 下一步 / 保存
     */
    async handlerNext() {
      console.log("handlerNext", this.formData, this.goodList, this.personList, this.signImg)
      let flag = await saveRecord(this.formData, this.imgList, this.signImg, this.personList, this.goodList)
      this.$emit('save', flag)
    },
    /**
     * 下拉框件点击事件
     * @param value
     */
    selectClick(value) {
      console.log("selectClick", value)
      switch (this.dicType) {
        case "meal_type":
          // 如果选的餐段不一样，要清空选的菜品
          if (this.formData.meal_type !== value.mealType) {
            this.goodList = []
          }
          this.$set(this.formData, 'meal_type', value.mealType)
          this.$set(this.formData, 'meal_type_name', value.name)
          break;
        case "org_name":
          // 如果选的组织不一样，要清空选的照片与菜品
          if (this.formData.org_id !== value.id) {
            this.imgList = []
            Cache.set(this.$common.KEY_ACCOMPANYING_TAKE_IMG_LIST, [])
            this.goodList = []
          }
          this.$set(this.formData, 'org_name', value.name)
          this.$set(this.formData, 'org_id', value.id)
          break;
        case "person":
          if (this.personList && this.dicPositionIndex < this.personList.length) {
            this.$set(this.personList[this.dicPositionIndex], 'type', value.name)
            this.$set(this.personList[this.dicPositionIndex], 'typeKey', value.value)
          }
          break;
        case "room_clean_type":
          this.$set(this.formData, 'room_clean_type', value.value)
          this.$set(this.formData, 'room_clean_type_name', value.name)
          break;
        case "room_attitude_type":
          this.$set(this.formData, 'room_attitude_type', value.value)
          this.$set(this.formData, 'room_attitude_type_name', value.name)
          break;
        case "area_clean_type":
          this.$set(this.formData, 'area_clean_type', value.value)
          this.$set(this.formData, 'area_clean_type_name', value.name)
          break;
        case "area_waste_type":
          this.$set(this.formData, 'area_waste_type', value.value)
          this.$set(this.formData, 'area_waste_type_name', value.name)
          break;
        case "oa_clean_type":
          this.$set(this.formData, 'oa_clean_type', value.value)
          this.$set(this.formData, 'oa_clean_type_name', value.name)
          break;
        case "oa_operate_type":
          this.$set(this.formData, 'oa_operate_type', value.value)
          this.$set(this.formData, 'oa_operate_type_name', value.name)
          break;
        case "tda_clean_type":
          this.$set(this.formData, 'tda_clean_type', value.value)
          this.$set(this.formData, 'tda_clean_type_name', value.name)
          break;
        case "tda_disinfection_type":
          this.$set(this.formData, 'tda_disinfection_type', value.value)
          this.$set(this.formData, 'tda_disinfection_type_name', value.name)
          break;
        case "operation_type":
          this.$set(this.formData, 'operation_type', value.value)
          this.$set(this.formData, 'operation_type_name', value.name)
          break;
        default:
          break;
      }
      this.showActiveSheet = false
    },
    /**
     * 显示底部弹出框
     * @param type 类型
     * @param value 值
     */
    handlerShowActiveSheet(type, value, index) {
      console.log("handlerShowActiveSheet", type, value);
      this.dicType = type
      switch (type) {
        case "meal_type":
          this.title = '请选择餐段'
          this.dicList = deepClone(comDic.MEALSEGMENTS_LIST)
          break;
        case "org_name":
          this.title = '请选择食堂'
          this.dicList = deepClone(this.orgsList)
          break;
        case "person":
          this.title = '请选择职位'
          this.dicList = deepClone(PERSON_TYPE_LIST)
          this.dicPositionIndex = index
          break;
        case "room_clean_type":
          this.title = '请选择'
          this.dicList = deepClone(CLEAN_TYPE_LIST)
          break;
        case "room_attitude_type":
          this.title = '请选择'
          this.dicList = deepClone(ATTITUDE_TYPE_LIST)
          break;
        case "area_clean_type":
          this.title = '请选择'
          this.dicList = deepClone(CLEAN_TYPE_LIST)
          break;
        case "area_waste_type":
          this.title = '请选择'
          this.dicList = deepClone(WASTE_TYPE_LIST)
          break;
        case "oa_clean_type":
          this.title = '请选择'
          this.dicList = deepClone(CLEAN_TYPE_LIST)
          break;
        case "oa_operate_type":
          this.title = '请选择'
          this.dicList = deepClone(OPERATE_TYPE_LIST)
          break;
        case "tda_clean_type":
          this.title = '请选择'
          this.dicList = deepClone(CLEAN_TYPE_LIST)
          break;
        case "tda_disinfection_type":
          this.title = '请选择'
          this.dicList = deepClone(DISINFECTION_TYPE_LIST)
          break;
        case "operation_type":
          this.title = '请选择'
          this.dicList = deepClone(OPERATION_TYPE_LIST)
          break;
        default:
          break;
      }
      this.showActiveSheet = true
    },
    // 关闭底部弹出框
    closeActiveSheet() {
      this.showActiveSheet = false
    },
    // 添加陪餐人
    handlerAddPerson() {
      if (this.personList.length === 10) {
        return this.$u.toast('最多添加10人')
      }
      this.personList.push({
        name: '',
        type: ''
      })
    },
    // 添加照片
    async handlerAddPic() {
      if (!this.formData.org_name) {
        return this.$u.toast('请选择组织')
      }
      // let [err, res] = await this.$to(setUploadPic(6 - this.imgList.length, 'person', this))
      // if (err) {
      //   return this.$u.toast('上传照片失败')
      // }
      // if (res) {
      //   console.log("resultUrl", res);
      //   this.imgList = deepClone(res)
      // }
      let imgUrl = await takePhoto()
      if (!imgUrl) {
        return this.$u.toast('拍照失败或者用户取消，请重试！')
      }
      Cache.set(this.$common.KEY_ACCOMPANYING_TAKE_IMG, imgUrl)
      Cache.set(this.$common.KEY_ACCOMPANYING_TAKE_IMG_NAME, this.formData.org_name)
      this.$miRouter.push({ path: '/pages_others/meal/accompanying_photo' })
    },
    // 跳转签名
    handlerGoSign() {
      this.$Router.push({
        path: '/pages_others/meal/sign'
      })
    },
    // 添加菜品
    handlerAddGood() {
      if (!this.formData.meal_type) {
        return this.$u.toast('请选择餐段')
      }
      if (!this.formData.org_name) {
        return this.$u.toast('请选择组织')
      }
      if (this.$refs.addGoodDialog) {
        this.$refs.addGoodDialog.showDialog()
      }
    },
    // 取消
    handlerCancelAddGood() {

    },
    // 菜品确认添加
    handlerConfirmAddGood(data) {
      console.log("data", data);
      if (data && typeof data === 'object') {
        this.goodList.push(data)
      }
    },
    // 删除菜品
    handlerDeleteGood(index) {
      if (index >= 0 && index < this.goodList.length) {
        this.goodList.splice(index, 1)
      }
    },
    // 展示图片
    handlerShowImg(imgList, index) {
      if (!imgList || imgList.length === 0) {
        return this.$u.toast('暂无预览图片')
      }
      if (this.$refs.imgPreview) {
        this.$refs.imgPreview.setImgList(imgList, index)
        this.$refs.imgPreview.showDialog()
      }
    },
    // 刪除图片
    handlerDeletePic(index) {
      if (index > -1 && index < this.imgList.length) {
        this.imgList.splice(index, 1)
      }
    },
    // 删除人员
    handlerDeletePerson(index) {
      if (index > -1 && index < this.personList.length) {
        this.personList.splice(index, 1)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import "../base.css";

.add_meal_container {
  padding-bottom: 138rpx;

  .pic-layout {
    position: relative;

    .delete-tag {
      position: absolute;
      top: -10rpx;
      right: -10rpx;
    }
  }

  .image-sign {
    width: 500rpx;
    height: auto;
  }

  .image-sign-weixin {
    width: 500rpx;
    height: 200rpx;
  }

  .btn-layout {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    min-height: 118rpx;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;

    .approval_btn_style {
      width: 320rpx;
      height: 70rpx;
      border-radius: 8rpx;
      font-size: 28rpx;
      margin: 40rpx auto;
    }
  }

  ::v-deep.u-action-sheet__item-wrap {
    overflow: auto;
    max-height: 50vh;
  }
  .horizontal_cell_line {
    width: 100%;
    height: 1px;
    background-color: #e5e5e5;
    margin: 0;
  }
}
</style>

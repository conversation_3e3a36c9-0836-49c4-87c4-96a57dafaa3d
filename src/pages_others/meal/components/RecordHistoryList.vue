<template>
  <!--陪餐历史记录-->
  <view>
    <!-- 筛选层 -->
    <view>
      <filterLayoutComponent v-if="isShowFilterLayout" :filterDataLayoutList="filterDataList"
        @handlerItemClick="handlerChooseItemClick"></filterLayoutComponent>
    </view>
    <view class="meal_management_record">
      <mescroll-uni ref="mescrollRef" :fixed="false" :safearea="true" :bottom="0" @init="mescrollInit"
        @down="downCallback" @up="upCallback" :down="{ auto: false }" :up="{ auto: false }">
        <view class="meal-tag" v-for="(item, index) in orderList" :key="index">
          <view class="flex row-between">
            <!--头部-->
            <view class="lg black">{{ item.create_time }}</view>
            <view class="lg yellow">{{ getMealName(item.meal_type) }}</view>
          </view>
          <!--分割灰线-->
          <view class="horizontal_cell_line"></view>
          <view>
            <view class="flex row-between">
              <view class="nr gray-light">{{ canteenText }}</view>
              <view class="nr black right-txt line-1">{{ item.organization_name }}</view>
            </view>
            <view class="flex row-right m-t-20">
              <u-button :customStyle="customStyleBtn" class="btn_style" :color="color.colorBtnOrange"
                @click="viewDetail(item, index)">{{ btnTxt }}</u-button>
            </view>
          </view>
        </view>
      </mescroll-uni>
    </view>
    <!-- 空白页 -->
    <emptyComponent :emptyContent="emptyContent" v-if="isShowEmptyView"></emptyComponent>
  </view>
</template>
<script>
import emptyComponent from "@/components/EmptyComponent/EmptyComponent"
import { mapGetters } from 'vuex'
import { deepClone, divide, getLastDayRange } from "../../../utils/util"
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import { apiMerchantMobileMealAccompanyingMealAccompanyingListPost } from '@/api/meal'
import FilterLayoutComponent from "@/components/FilterLayoutComponent/FilterLayoutComponent"
import Cache from "@/utils/cache"
import { getOrgsList, getMealTypeName } from '../utils.js'
export default {
  data() {
    return {
      imgPath: this.$imgPath,
      pageNo: 1, // 页码
      pageSize: 10, // 每页显示数据
      parmas: {
        date_type: 'create_date',
        page: 1,
        page_size: 10
      }, // 参数
      isShowEmptyView: false, // 是否显示空白内容
      emptyContent: this.$t('tip.list.empty'),
      orderList: [], // 订单列表
      isShowFilterLayout: true, // 是否显示下拉筛选layout
      filterDataList: [
        {
          title: '全部时间',
          chooseItem: '全部时间',
          dataList: [
            {
              name: '全部时间',
              value: 0
            },
            {
              name: '近3天',
              value: 3
            },
            {
              name: '近7天',
              value: 7
            },
            {
              name: '近30天',
              value: 30
            }
          ]
        },
        {
          title: '全部',
          chooseItem: '全部',
          dataList: [
            {
              name: '全部',
              value: ''
            }
          ]
        }
      ],
      customStyleBtn: { // 按钮自定义样式这里为了兼容微信与支付宝
        width: '120rpx',
        height: '48rpx',
        borderRadius: '6rpx',
        fontSize: '24rpx',
        margin: '0 10rpx',
        padding: '0 !important'
      },
      btnTxt: this.$t('title.details'),
      canteenText: this.$t('page.meal.accompanying.dining.cafeteria')
    }
  },
  mixins: [MescrollMixin],
  computed: {
    ...mapGetters(['color'])
  },
  components: { emptyComponent, FilterLayoutComponent },
  created() {
    this.initData()
  },
  methods: {
    /**
     * 初始化数据
     */
    async initData() {
      // 获取数据
      console.log("this.parmas", this.parmas);
      this.parmas.page = this.pageNo
      this.parmas.page_size = this.pageSize
      this.getOrderList(this.parmas)
      let orgsId = Cache.get(this.$common.KEY_USER_ORGAN)
      let orgsList = await getOrgsList(orgsId)
      if (orgsList && orgsList.length > 0) {
        let orgsListData = orgsList.map(item => {
          return {
            name: item.name,
            value: item.id
          }
        })
        this.filterDataList[1].dataList = this.filterDataList[1].dataList.concat(orgsListData)
      }
    },
    /**
     * 下拉刷新返回
     */
    downCallback(page) {
      console.log(" downCallback page", page);
      this.pageNo = 1
      this.parmas.page = this.pageNo
      this.getOrderList(this.parmas)
    },
    /**
     * 上拉加载更多
     * @param {*} page
     */
    upCallback(page) {
      console.log(" upCallback page", page);
      this.pageNo++
      this.parmas.page = this.pageNo
      this.getOrderList(this.parmas)
    },
    /**
     * 切换菜单
     * @param {*} data
     */
    switchMenu(data) {
      console.log("switchMenu", data);
      const type = data.type
      this.currentIndex = data.index
      if (type === "history") {
        // this.getOrderList()
      }
    },
    /**
     * 获取订单列表
     * @param {*} parmas
     */
    async getOrderList(parmas) {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      const [error, res] = await this.$to(apiMerchantMobileMealAccompanyingMealAccompanyingListPost(parmas))
      uni.hideLoading()
      if (error) {
        this.mescroll.endErr()
        uni.$u.toast(error.message)
        return
      }
      if (res.code === 0) {
        var data = Reflect.has(res, "data") ? res.data : {}
        var resultList = Reflect.has(data, "results") ? data.results : []
        var count = data.count ? data.count : 0
        // 没有数据
        this.isShowEmptyView = this.parmas.page === 1 && (!resultList || resultList.length === 0)
        if (this.parmas.page === 1 && resultList && resultList.length > 0) {
          // 首次加载数据
          console.log("首次加载数据");
          this.orderList = deepClone(resultList)
        } else if (this.parmas.page !== 1 && resultList && resultList.length > 0) {
          // 加载更多数据
          console.log("加载更多数据");
          this.orderList = this.orderList.concat(resultList)
        } else {
          // 其他情况
          console.log("其他情况");
          if (this.parmas.page === 1) {
            this.orderList = []
          }
          uni.hideLoading()
          var message = this.parmas.page === 1 ? '找不到对应数据' : '没有更多了'
          uni.$u.toast(res.msg !== '成功' && res.msg !== 'OK' ? res.msg : message)
        }
        this.mescroll.setPageNum(this.parmas.page)
        this.mescroll.endBySize(this.pageSize, count)
      } else {
        if (this.parmas.page === 1) {
          this.orderList = []
        }
        uni.hideLoading()
        uni.$u.toast(res.msg)
        this.mescroll.endErr()
      }
      console.log("data", data, resultList, this.orderList);
    },

    /**
     * 点击搜索
     */
    handlerOrderSearch(e) {
      console.log("handlerOrderSearch");
      if (e && e.length) {
        // 根据用户输入的内容重新获取列表
        this.parmas.name = e
        this.pageNo = 1
        this.initData()
      }
    },
    /**
     * 搜索清除
     */
    searchClear() {
      console.log("searchClear");
      delete this.parmas.name
      this.pageNo = 1
      this.initData()
    },
    // 供应商名称
    getSupplieName(item) {
      if (!item) {
        return ""
      }
      let qrCode = item.qr_code || {}
      let supplierManageName = qrCode.supplier_manage_name || ''
      return supplierManageName
    },
    // 根据key返回值
    getSupplieByType(item, key) {
      if (!item) {
        return ""
      }
      let qrCode = item.qr_code || {}
      let keyName = qrCode[key] || ''
      if (key === 'ref_unit_price') {
        keyName = divide(keyName)
      }
      return keyName
    },
    // 展示图片
    handlerShowImg(imgList, index) {
      if (!imgList || imgList.length === 0) {
        return this.$u.toast('暂无预览图片')
      }
      if (this.$refs.imgPreview) {
        this.$refs.imgPreview.setImgList(imgList, index)
        this.$refs.imgPreview.showDialog()
      }
    },
    /**
     * 每个筛选选项的点击
     * @param {每项的数据} itemData
     * @param {点击的项所在列表的位置} index
     */
    handlerChooseItemClick(itemData, index) {
      console.log("handlerChooseItemClick", itemData, index, this.parmas);
      // 设置点击记录
      this.filterDataList[index].chooseItem = itemData.name
      this.filterDataList[index].title = itemData.name
      const chooseValue = itemData.value
      // 筛选列表
      switch (index) {
        case 0:// 日期
          if (!itemData.value) {
            delete this.parmas.start_date
            delete this.parmas.end_date
            this.filterDataList[index].title = '全部时间'
          } else {
            this.filterDataList[index].title = itemData.name
            this.parmas.start_date = getLastDayRange(chooseValue, '{y}-{m}-{d}')[0]
            this.parmas.end_date = getLastDayRange(chooseValue, '{y}-{m}-{d}')[1]
          }
          break;
        case 1:// 组织
          this.parmas.organization_ids = [itemData.value]
          if (!itemData.value) {
            delete this.parmas.organization_ids
            this.filterDataList[index].title = '全部'
          } else {
            this.filterDataList[index].title = itemData.name
          }
          break;
        default:
          break;
      }
      this.parmas.page = 1
      this.getOrderList(this.parmas)
    },
    // 查看详情
    viewDetail(e, index) {
      // 存储到缓存
      Cache.set(this.$common.KEY_ACCOMPANYING_ITEM_INFO, e)
      this.$miRouter.push({
        path: '/pages_others/meal/accompanying_meal_detail',
        query: {
          index: index
        }
      })
    },
    // 获取餐段
    getMealName(type) {
      return getMealTypeName(type)
    }
  }
}

</script>
<style lang="scss" scoped>
.meal_management_record {
  margin-top: 20rpx;

  .meal-tag {
    width: 670rpx;
    height: 243rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    margin: 20rpx 40rpx;
    padding: 26rpx 30rpx;
  }

  .horizontal_cell_line {
    width: 100%;
    height: 1px;
    background-color: #e5e5e5;
    margin: 26rpx 0;
  }

  .right-txt {
    max-width: 480rpx;
  }

  .btn_style {
    width: 120rpx;
    height: 48rpx;
    border-radius: 6rpx;
  }

}
</style>

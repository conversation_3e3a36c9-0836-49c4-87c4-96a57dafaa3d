import {
  apiBackgroundOrganizationOrganizationListPost,
  apiMerchantMobileMealAccompanyingMealAccompanyingSavePost
} from '@/api/meal'
import cache from '@/utils/cache'
import common from '@/common/common'
import { to, showLoading, deepClone, parseTime, times } from '@/utils/util'
// import Exif from 'exif-js'
import config from '@/config/index'
import comDic from '@/common/comDic.js'
import {
  CLEAN_TYPE_LIST,
  ATTITUDE_TYPE_LIST,
  WASTE_TYPE_LIST,
  OPERATE_TYPE_LIST,
  DISINFECTION_TYPE_LIST,
  OPERATION_TYPE_LIST
} from './constants.js'

// 定义一个上传的url
let uploadFileUrl = config.baseUrl + '/api/merchant_mobile/common/upload'
export const getOrgsList = async orgId => {
  return new Promise(resolve => {
    apiBackgroundOrganizationOrganizationListPost({
      is_subordinate_organization: true
    })
      .then(res => {
        if (res && res.code === 0) {
          let data = res.data || {}
          let results = data.results || []
          return resolve(results)
        } else {
          resolve([])
        }
      })
      .catch(error => {
        resolve([])
        console.log('error', error)
      })
  })
}

// 保存记录
export const saveRecord = async (formData, imagesList, signUrl, personList, foodList) => {
  console.log('formData', formData)
  if (!checkoutData(formData, imagesList, signUrl, personList, foodList)) {
    return null
  }
  let newPersonList = []
  if (personList && personList.length > 0) {
    newPersonList = personList.map(item => {
      return {
        name: item.name,
        identity_type: item.typeKey
      }
    })
  }
  let newFoodList = []
  let setMealList = []
  if (foodList && foodList.length > 0) {
    foodList.forEach(item => {
      let tag = {
        price: times(item.price),
        remark: item.remark,
        name: item.food_name,
        gg_excellent_type: item.lookFeelNameKey,
        zl_excellent_type: item.qualityNameKey,
        quantity_type: item.amountNameKey,
        price_type: item.priceTypeNameKey
      }
      let setMealName = item.obj_name
      if (setMealName === 'set_meal') {
        tag.set_meal_id = item.food_id
        tag.name = item.food_name
        setMealList.push(tag)
        // 套餐
      } else {
        tag.food_id = item.food_id
        tag.spec = item.food_spec_name
        newFoodList.push(tag)
      }
    })
  }
  // 入参
  let params = {
    meal_time: formData.meal_date,
    meal_type: formData.meal_type,
    organization: formData.org_id,
    room_clean_type: formData.room_clean_type,
    room_attitude_type: formData.room_attitude_type,
    area_clean_type: formData.area_clean_type,
    area_waste_type: formData.area_waste_type,
    oa_clean_type: formData.oa_clean_type,
    oa_operate_type: formData.oa_operate_type,
    tda_clean_type: formData.tda_clean_type,
    tda_disinfection_type: formData.tda_disinfection_type,
    operation_type: formData.operation_type,
    images: imagesList, // 陪餐图片
    remark: formData.remark,
    person_records: newPersonList, // 陪餐人信息
    food_records: newFoodList, // 菜品
    set_meal_records: setMealList, // 套餐
    face_url: signUrl // 签名入参
  }
  return new Promise(resolve => {
    showLoading({
      title: '保存中...',
      mask: true
    })
    apiMerchantMobileMealAccompanyingMealAccompanyingSavePost(params)
      .then(res => {
        uni.hideLoading()
        if (res && res.code === 0) {
          uni.$u.toast('保存成功')
          return resolve(true)
        } else {
          uni.$u.toast(res.msg || '保存失败')
          resolve(false)
        }
      })
      .catch(error => {
        uni.hideLoading()
        console.log('error', error)
        resolve(false)
      })
  })
}
// 检测数据
export const checkoutData = (formData, imagesList, signUrl, personList, foodList) => {
  if (!formData.meal_type) {
    uni.$u.toast('请选择陪餐餐段')
    return false
  }
  if (!formData.org_id) {
    uni.$u.toast('请选择组织')
    return false
  }
  if (!personList || personList.length === 0) {
    uni.$u.toast('请选择陪餐人信息')
    return false
  }
  if (personList && personList.length > 0) {
    for (let i = 0; i < personList.length; i++) {
      if (!personList[i].name) {
        uni.$u.toast('请填写陪餐人姓名')
        return false
      }
      if (!personList[i].typeKey) {
        uni.$u.toast('请选择陪餐人岗位类型')
        return false
      }
    }
  }
  if (!formData.room_clean_type) {
    uni.$u.toast('请选择备餐区评价')
    return false
  }
  if (!formData.room_attitude_type) {
    uni.$u.toast('请选择备餐区服务态度评价')
    return false
  }
  if (!formData.area_clean_type) {
    uni.$u.toast('请选择就餐区评价')
    return false
  }
  if (!formData.area_waste_type) {
    uni.$u.toast('请选择就餐区餐饮浪费情况评价')
    return false
  }
  if (!formData.oa_clean_type) {
    uni.$u.toast('请选择其他加工操作区评价')
    return false
  }
  if (!formData.oa_operate_type) {
    uni.$u.toast('请选择其他加工操作区规范评价')
    return false
  }
  if (!formData.tda_clean_type) {
    uni.$u.toast('请选择餐具消毒区评价')
    return false
  }
  if (!formData.tda_disinfection_type) {
    uni.$u.toast('请选择餐具消毒区效率评价')
    return false
  }
  if (!formData.operation_type) {
    uni.$u.toast('请选择明厨亮灶运作评价')
    return false
  }
  if (!foodList || foodList.length === 0) {
    uni.$u.toast('请选择菜品')
    return false
  }
  if (!imagesList || imagesList.length === 0) {
    uni.$u.toast('请上传陪餐图片')
    return false
  }
  if (!signUrl) {
    uni.$u.toast('请填写签名')
    return false
  }
  return true
}

/**
 * 设置上传face
 */
export async function setUploadPic(count, type, vm) {
  // var that = vm
  // // #ifdef MP-WEIXIN || MP-ALIPAY
  // that.$miRouter.push({
  //   path: '/pages_common_function/user/face_photo_graph'
  // })
  // // #endif
  return new Promise((resolve, reject) => {
    // #ifdef H5 || MP-WEIXIN
    // 从相册选择1张图
    uni.chooseImage({
      count: count,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      async success(res) {
        console.log('res.tempFilePaths', res)
        let filesList = res.tempFiles || []
        let newResultList = []
        let uploadCompleteCount = 0
        if (filesList && filesList.length > 0) {
          for (let i = 0; i < filesList.length; i++) {
            let file = filesList[i]
            uploadImage(file, i, function (index, result) {
              uploadCompleteCount++
              if (result && result.length > 0) {
                newResultList.push(result)
              }
              console.log('uploadCompleteCount', uploadCompleteCount, i)
              if (uploadCompleteCount === filesList.length) {
                resolve(newResultList)
              }
            })
          }
        }
      },
      fail(error) {
        reject(error.message)
      }
    })
    // #endif
  })
}
export async function uploadImage(file, index, uploadCallBack, isBase64) {
  if (!isBase64) {
    // 先上传文件形成url
    let [error, resultUrl] = await to(uploadFilePromise(file))
    if (resultUrl && !error) {
      // 调用上传返回上传的图片
      uploadCallBack(index, resultUrl)
    } else {
      uploadCallBack(index, '')
    }
  } else {
    // 提取 Base64 编码部分
    const base64Data = file.replace(/^data:image\/(\w+);base64,/, '')
    // 将 Base64 编码转换为 ArrayBuffer
    const binary = atob(base64Data)
    const array = []
    for (let i = 0; i < binary.length; i++) {
      array.push(binary.charCodeAt(i))
    }
    const buffer = new Uint8Array(array).buffer
    // 创建一个 Blob 对象
    // const blob = new Blob([buffer], { type: 'image/png' });
    // 创建带命名的File对象（新增核心逻辑）
    let fileName = 'canvas' + new Date().getTime() + '.png'
    const fileObj = new File([buffer], fileName, { type: 'image/png' }) // 命名关键代码‌:ml-citation{ref="1,2" data="citationList"}
    // 创建一个 URL 指向 Blob 对象
    let url = URL.createObjectURL(fileObj)
    let [error, resultUrl] = await to(uploadFilePromise(url, fileName))
    if (resultUrl && !error) {
      // 调用上传返回上传的图片
      uploadCallBack(index, resultUrl)
    } else {
      uploadCallBack(index, '')
    }
  }
}

/**
 * 上传文件
 * @param {传入文件的本地路径} url
 * @returns
 */
export function uploadFilePromise(url, key) {
  let params = {
    prefix: 'face'
  }
  if (key) {
    params.key = key
  }
  return new Promise((resolve, reject) => {
    showLoading({
      title: '上传中...',
      mask: true
    })
    uni.uploadFile({
      url: uploadFileUrl,
      filePath: url,
      name: 'file',
      header: {
        TOKEN: cache.get(common.API_TOKEN)
      },
      formData: params,
      success: res => {
        console.log('uni.uploadFile', res)
        uni.hideLoading()
        var faceURl = JSON.parse(res.data).data.public_url
        resolve(faceURl)
      },
      fail: error => {
        console.log('uni.uploadFile', error)
        uni.hideLoading()
        reject(error.message)
      }
    })
  })
}
// 获取餐段名称
export const getMealTypeName = type => {
  let dicList = deepClone(comDic.MEALSEGMENTS_LIST)
  let result = dicList.find(item => item.mealType === type)
  if (result) {
    return Reflect.has(result, 'name') ? result.name : ''
  }
  return ''
}

// 获取名字
export const getNameByType = (type, value) => {
  let dicList = []
  switch (type) {
    case 'room_clean_type':
      dicList = deepClone(CLEAN_TYPE_LIST)
      break
    case 'room_attitude_type':
      dicList = deepClone(ATTITUDE_TYPE_LIST)
      break
    case 'area_clean_type':
      dicList = deepClone(CLEAN_TYPE_LIST)
      break
    case 'area_waste_type':
      dicList = deepClone(WASTE_TYPE_LIST)
      break
    case 'oa_clean_type':
      dicList = deepClone(CLEAN_TYPE_LIST)
      break
    case 'oa_operate_type':
      dicList = deepClone(OPERATE_TYPE_LIST)
      break
    case 'tda_clean_type':
      dicList = deepClone(CLEAN_TYPE_LIST)
      break
    case 'tda_disinfection_type':
      dicList = deepClone(DISINFECTION_TYPE_LIST)
      break
    case 'operation_type':
      dicList = deepClone(OPERATION_TYPE_LIST)
      break

    default:
      break
  }
  let result = dicList.find(item => item.value === value)
  if (result) {
    return Reflect.has(result, 'name') ? result.name : ''
  }
  return ''
}

// 纯拍照
export const takePhoto = () => {
  return new Promise((resolve, reject) => {
    // #ifdef H5 || MP-WEIXIN
    // 从相册选择1张图
    uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera'],
      async success(res) {
        console.log('res.tempFilePaths', res)
        let filesList = res.tempFilePaths || []
        if (filesList && filesList.length > 0) {
          resolve(filesList[0])
        } else {
          resolve(false)
        }
      },
      fail(error) {
        console.log('error', error)
        resolve(false)
      }
    })
    // #endif
  })
}
// 添加地址水印等
export const addWatermark = (ctx, imgPath, address) => {
  uni.getImageInfo({
    src: imgPath,
    success: imgRes => {
      let canvas = null
      if (!ctx) {
        // #ifdef H5
        canvas = document.createElement('watermarkCanvas')
        canvas = uni.createCanvasContext('watermarkCanvas')
        ctx = canvas.getContext('2d')
        // #endif
      }
      // 设置canvas尺寸与原图一致
      let width = uni.getSystemInfoSync().windowWidth
      let height = uni.getSystemInfoSync().windowHeight - uni.upx2px(118 + 88)
      console.log('width:' + width, 'height:' + height, uni.getSystemInfoSync().windowHeight)
      ctx.canvasWidth = width
      ctx.canvasHeight = height

      // 绘制原始图片
      ctx.drawImage(imgRes.path, 0, 0, width, height)
      // #ifdef H5
      // 添加水印文字（左下角）
      ctx.setFontSize(20)
      ctx.setFillStyle('#ffffff') // 白色字体
      ctx.fillText(address, 10, height - 10) // 调整位置到左下角
      // 打印时间
      let timeDate = parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}')
      ctx.fillText(timeDate, 10, height - 40)
      // #endif
      // #ifdef MP-WEIXIN
      // 添加水印文字（左下角）
      ctx.setFontSize(20)
      ctx.setFillStyle('#ffffff') // 白色字体
      ctx.fillText(address, 10, height - 50) // 调整位置到左下角
      // 打印时间
      let timeDateW = parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}')
      ctx.fillText(timeDateW, 10, height - 80)
      // #endif

      ctx.draw(false, () => {
        console.log('图片绘制完成')
      })
    }
  })
}
// 保存图片
export const saveImgByCanvas = (canvasId, that) => {
  uni.canvasToTempFilePath({
    canvasId: canvasId,
    fileType: 'png',
    quality: 1, // 图片质量
    success: async res => {
      that.$showLoading({
        title: '',
        mask: true
      })
      console.log('canvasToTempFilePath', res)
      let type = false
      // #ifdef H5
      type = true
      // #endif
      uploadImage(
        res.tempFilePath,
        0,
        function (index, result) {
          console.log('uploadImage', result)
          uni.hideLoading()
          let resultList = cache.get(that.$common.KEY_ACCOMPANYING_TAKE_IMG_LIST) || []
          resultList.push(result)
          cache.set(that.$common.KEY_ACCOMPANYING_TAKE_IMG_LIST, resultList)
          uni.$emit(that.$common.KEY_ACCOMPANYING_TAKE_IMG_LIST, resultList)
          that.$miRouter.back()
        },
        type
      )
    }
  })
}

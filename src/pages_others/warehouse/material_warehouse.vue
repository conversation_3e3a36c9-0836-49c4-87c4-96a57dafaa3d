<template>
  <!--物资库-->
  <view class="material_warehouse_container" catchtouchmove="true">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar :title="$t('page.inventory.material.warehouse')" placeholder :autoBack="true"
      :leftIconColor="color.navigation" leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"></u-navbar>
    <!--#endif-->
    <!--筛选层-->
    <view class="search-container">
      <searchComponent searchPlaceholder="请输入物资名称" @searchClear="searchClear" @handlerSearch="handlerOrderSearch"
        type="material_warehouse"></searchComponent>
    </view>
    <!--内容列表-->
    <view class="material_warehouse_content" v-if="!isShowEmptyView">
      <mescroll-uni ref="mescrollRef" :fixed="false" :safearea="true" :bottom="0" @init="mescrollInit"
        @down="downCallback" @up="upCallback" :down="{ auto: false }" :up="{ auto: false }">
        <view class="material_warehouse_item" v-for="(item, index) in orderList" :key="index">
          <view class="flex">
            <view @click="handlerShowImg(item.material_feature_list, 0)">
              <image class="img-tag"
                :src="item.material_feature_list && item.material_feature_list.length > 0 ? item.material_feature_list[0] : imgPath.IMG_GOOD_DEFAULT" alt="图像"
                mode="scaleToFill"></image>
            </view>
            <!--订单号头部-->
            <view class="m-l-30 m-t-10">
              <view class="material_warehouse_item_title">{{ item.materials_name }}</view>
              <view class="price-tag">{{ '¥ ' + getSupplieByType(item, 'ref_unit_price') + ' 元' + (item.unit_name ? " / " + item.unit_name : "") }}</view>
            </view>
          </view>
          <!--分割灰线-->
          <view class="horizontal_cell_line"></view>
          <view class="">供应商：{{ getSupplieName(item) }}</view>
        </view>
      </mescroll-uni>
    </view>
    <!-- 空白页 -->
    <emptyComponent :emptyContent="emptyContent" v-if="isShowEmptyView"></emptyComponent>
    <!--#ifdef MP-WEIXIN || MP-ALIPAY -->
    <CustomDialogComponent ref="customDialog"></CustomDialogComponent>
    <!--#endif-->
    <!--图片预览-->
    <image-preview ref="imgPreview"></image-preview>
  </view>
</template>
<script>
import searchComponent from "@/components/SearchComponent/SearchComponent"
import emptyComponent from "@/components/EmptyComponent/EmptyComponent"
import { mapGetters } from 'vuex'
import { deepClone, divide } from "../../utils/util"
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import { apiBackgroundDrpMaterialsSupplierManageDetails } from '@/api/inventory'
import ImagePreview from '@/components/ImagePreview/ImagePreview.vue';

export default {
  data() {
    return {
      imgPath: this.$imgPath,
      cellStyle: { height: "50rpx" },
      titleStyle: { 'font-size': "24rpx", color: '#8f9295' },
      valueStyle: { 'font-size': "24rpx", color: '#1d1e20' },
      orderList: [],
      userData: {
        head: {
          searchDataHolder: this.$t('tip.order.please.enter.name.mobile'),
          orderNoTxt: this.$t('page.order.order.number'),
          ceateTimeTxt: this.$t('page.order.create.time'),
          paymentTimeTxt: this.$t('page.approval.order.payment.time'),
          userNameTxt: this.$t('page.order.the.user.name'),
          mobileTxt: this.$t('page.user.mobile'),
          orderAmountTxt: this.$t('page.order.order.amount'),
          failureReasonTxt: this.$t('page.order.failure.reason'),
          btnCancelOrderTxt: this.$t('page.order.btn.cancel.order'),
          btnDeductionPriceTxt: this.$t('page.order.btn.deduction.from.original.price'),
          btnRedeductionTxt: this.$t('page.order.btn.rededuction')
        },
        base: {
          name: this.$t('page.user.name'),
          sex: this.$t('page.user.sex'),
          birthday: this.$t('page.user.birthday'),
          mobile: this.$t('page.user.mobile'),
          nameTxt: "诸葛明亮",
          sexTxt: "女",
          birthdayTxt: "1990-10",
          mobileTxt: "158*****5545"
        }

      },
      customStyleBtn: {
        width: '120rpx',
        height: '48rpx',
        borderRadius: '6rpx',
        fontSize: '24rpx',
        margin: '0 10rpx',
        padding: '0 !important'
      },
      pageNo: 1, // 页码
      pageSize: 10, // 每页显示数据
      parmas: {
        page: 1,
        page_size: 10,
        supplier_manage_id: '57'
      }, // 参数
      isShowEmptyView: false, // 是否显示空白内容
      emptyContent: this.$t('tip.list.empty')
    }
  },
  computed: {
    ...mapGetters(['color'])
  },
  mixins: [MescrollMixin],
  components: { searchComponent, emptyComponent, ImagePreview },

  created() {
    this.initData()
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      // 获取数据
      console.log("this.parmas", this.parmas);
      this.parmas.page = this.pageNo
      this.parmas.page_size = this.pageSize
      this.parmas.supplier_manage_id = '57'
      this.getOrderList(this.parmas)
    },
    /**
     * 下拉刷新返回
     */
    downCallback(page) {
      console.log(" downCallback page", page);
      this.pageNo = 1
      this.parmas.page = this.pageNo
      this.getOrderList(this.parmas)
    },
    /**
     * 上拉加载更多
     * @param {*} page
     */
    upCallback(page) {
      console.log(" upCallback page", page);
      this.pageNo++
      this.parmas.page = this.pageNo
      this.getOrderList(this.parmas)
    },
    /**
     * 获取订单列表
     * @param {*} parmas
     */
    async getOrderList(parmas) {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      const [error, res] = await this.$to(apiBackgroundDrpMaterialsSupplierManageDetails(parmas))
      if (error) {
        this.mescroll.endErr()
        uni.$u.toast(error.message)
        return
      }
      if (res.code === 0) {
        uni.hideLoading()
        var data = Reflect.has(res, "data") ? res.data : {}
        var resultList = Reflect.has(data, "results") ? data.results : []
        var count = data.count ? data.count : 0
        // 没有数据
        this.isShowEmptyView = this.pageNo === 1 && (!resultList || resultList.length === 0)
        if (this.pageNo === 1 && resultList && resultList.length > 0) {
          // 首次加载数据
          console.log("首次加载数据");
          this.orderList = deepClone(resultList)
        } else if (this.pageNo !== 1 && resultList && resultList.length > 0) {
          // 加载更多数据
          console.log("加载更多数据");
          this.orderList = this.orderList.concat(resultList)
        } else {
          // 其他情况
          console.log("其他情况");
          this.orderList = []
          uni.hideLoading()
          var message = this.pageNo === 1 ? '找不到对应数据' : '没有更多了'
          uni.$u.toast(res.msg !== '成功' && res.msg !== 'OK' ? res.msg : message)
        }
        this.mescroll.setPageNum(this.pageNo)
        this.mescroll.endBySize(this.pageSize, count)
      } else {
        this.orderList = []
        uni.hideLoading()
        uni.$u.toast(res.msg)
        this.mescroll.endErr()
      }
      console.log("data", data, resultList, this.orderList);
    },

    /**
     * 点击搜索
     */
    handlerOrderSearch(e) {
      console.log("handlerOrderSearch");
      if (e && e.length) {
        // 根据用户输入的内容重新获取列表
        this.parmas.name = e
        this.pageNo = 1
        this.initData()
      }
    },
    /**
     * 搜索清除
     */
    searchClear() {
      console.log("searchClear");
      delete this.parmas.name
      this.pageNo = 1
      this.initData()
    },
    // 供应商名称
    getSupplieName(item) {
      if (!item) {
        return ""
      }
      let qrCode = item.qr_code || {}
      let supplierManageName = qrCode.supplier_manage_name || ''
      return supplierManageName
    },
    // 根据key返回值
    getSupplieByType(item, key) {
      if (!item) {
        return ""
      }
      let qrCode = item.qr_code || {}
      let keyName = qrCode[key] || ''
      if (key === 'ref_unit_price') {
        keyName = divide(keyName)
      }
      return keyName
    },
    // 展示图片
    handlerShowImg(imgList, index) {
      if (!imgList || imgList.length === 0) {
        return this.$u.toast('暂无预览图片')
      }
      if (this.$refs.imgPreview) {
        this.$refs.imgPreview.setImgList(imgList, index)
        this.$refs.imgPreview.showDialog()
      }
    }
  }

}
</script>

<style lang="scss" scoped>
.material_warehouse_container {
  .search-container {
    position: fixed;
  }

  .material_warehouse_content {
    padding: 140rpx 20rpx 20rpx 20rpx;
    height: calc(100vh - 114rpx);

    .material_warehouse_item {
      width: 670rpx;
      height: 216rpx;
      background-color: #ffffff;
      border-radius: 12rpx;
      margin: 20rpx auto;
      padding: 20rpx 40rpx;

      .material_warehouse_item_title {
        font-size: 36rpx;
        color: #1d1e20;
      }

    }

    .horizontal_cell_line {
      width: 610rpx;
      height: 1rpx;
      background-color: #eae9ed;
      margin: 20rpx auto;

    }

    .material_warehouse_btns_content {
      display: flex;
      justify-content: flex-end;
      margin: 10rpx 20rpx;
      flex-wrap: wrap;

      .material_warehouse_btn_style {
        width: 120rpx;
        height: 48rpx;
        border-radius: 6rpx;
        font-size: 24rpx;
        margin: 0 10rpx;
        padding: 0 !important;
      }
    }

  }

  .warn_tip_style {
    color: $color-primary;
    margin: 10rpx 30rpx;
    font-size: 24rpx;
    padding-top: 130rpx;
  }

  .img-tag {
    width: 100rpx;
    height: 100rpx;
    border: 10rpx;
  }

}
</style>

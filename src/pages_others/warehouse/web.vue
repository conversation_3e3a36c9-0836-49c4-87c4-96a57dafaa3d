<template>
  <view>
     <!--#ifdef MP-WEIXIN || H5 -->
     <u-navbar title="物资选购" placeholder :autoBack="true"
      :leftIconColor="color.navigation" leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"></u-navbar>
    <!--#endif-->
    <web-view :webview-styles="webviewStyles" style="margin-top: 90rpx;"
      src="https://crmeb.base.packertec.com/pages/goods_cate/goods_cate"></web-view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      webviewStyles: {
        progress: {
          color: '#FF3333'
        }
      }
    }
  },
  computed: {
    ...mapGetters(['color'])
  }
}
</script>

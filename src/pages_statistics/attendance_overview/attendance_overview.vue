<template>
  <view class="attendance">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('business.Attendance.overview')"
      :autoBack="true"
      :leftIconColor="color.navigation"
      placeholder
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx',zIndex :'99' }"
    ></u-navbar>
    <!--#endif-->
    <!--筛选  -->
    <filterLayoutComponent
      ref="filterLayout"
      :filterDataLayoutList="drowDownDatalist"
      @handlerItemClick="handlerItemClick"
    ></filterLayoutComponent>
    <view class="attendance_fig">
      <view class="attendance_figs">
        <view>
          <qiunDataCharts type="ring" :opts="opts" :chartData="chartData" v-show="isShowChart" />
        </view>
        <view>出勤率：77%</view>
      </view>
      <view class="attendance_data">
        <view class="app_ul">
          <view class="app_li" v-for="item in fold" :key="item.id">
            <view>{{ item.value }}</view>
            <view>
              <u-badge :isDot="true" :bgColor="item.color"></u-badge>
              {{ item.name }}人数
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="attendance_personnel" v-for="item in fold" :key="item.id">
      <u-collapse class="u-collapse-item" @close="close" @open="open">
        <u-collapse-item>
          <text slot="title" class="u-page__item__title__slot-title">{{ item.name }}</text>
          <text slot="value" class="u-page__item__title__slot-title">2人</text>
          <view class="u-collapse-content">
            <view class="align_self_left">诸葛明亮</view>
            <view>创意1班</view>
            <view class="align_self_right">2022-8-9 07：15：45</view>
          </view>
          <view class="u-collapse-content">
            <view class="align_self_left">诸葛明亮</view>
            <view>创意1班</view>
            <view class="align_self_right">2022-8-9 07：15：45</view>
          </view>
        </u-collapse-item>
      </u-collapse>
    </view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
import { getAttendanceGroup } from '@/api/overview.js'
import { getDateRange } from '@/utils/time.js'
import filterLayoutComponent from '@/components/FilterLayoutComponent/FilterLayoutComponent.vue'
import qiunDataCharts from '@/pages_statistics/components/qiun-data-charts/components/qiun-data-charts/qiun-data-charts'

export default {
  computed: {
    ...mapGetters(['img', 'color'])
  },
  components: {
    filterLayoutComponent, qiunDataCharts
  },
  data() {
    return {
      isShowChart: true, // 是否显示图表
      startDate: '', // 开始时间
      endDate: '', // 结束时间
      drowDownDatalist: [
        {
          title: '今天',
          dataList: [
            {
              name: this.$t('That.Today'),
              time: 'day'
            },
            {
              name: this.$t('That.sevendays'),
              time: 'week'
            },
            {
              name: this.$t('That.thirtydays'),
              time: 'month'
            }
          ]
        },
        {
          title: '全部考勤组',
          dataList: []
        }
      ],
      fold: [
        {
          id: 1,
          name: this.$t('Attendance.sign'),
          value: 23.568,
          color: ' #5acf36'
        },
        {
          id: 2,
          name: this.$t('Attendance.out'),
          value: 7.568,
          color: ' #fd7358'
        },
        {
          id: 3,
          name: this.$t('Attendance.late'),
          value: 3.568,
          color: ' #fd953c'
        },
        {
          id: 4,
          name: this.$t('Attendance.early'),
          value: 768,
          color: ' #f6c80e'
        },
        {
          id: 5,
          name: this.$t('Attendance.leave'),
          value: 568,
          color: ' #4da8f5'
        },
        {
          id: 6,
          name: this.$t('Attendance.card'),
          value: 235,
          color: ' #fd6f8d'
        }
      ],
      chartData: {},
      // 您可以通过修改 config-ucharts.js 文件中下标为 ['ring'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。
      opts: {
        rotate: false,
        rotateLock: false,

        color: [
          '#1890FF',
          '#91CB74',
          '#FAC858',
          '#EE6666',
          '#73C0DE',
          '#3CA272',
          '#FC8452',
          '#9A60B4',
          '#ea7ccc'
        ],
        padding: [5, 5, 5, 5],
        dataLabel: true,
        enableScroll: false,
        legend: {
          show: false,
          position: 'right',
          lineHeight: 25
        },
        title: {
          name: '2.236.00',
          fontSize: 20,
          color: '#000'
        },
        subtitle: {
          name: '合计人数',
          fontSize: 10,
          color: '#666'
        },
        extra: {
          ring: {
            ringWidth: 10,
            activeOpacity: 0.5,
            activeRadius: 6,
            offsetAngle: 0,
            labelWidth: 151,
            border: true,
            borderWidth: 3,
            borderColor: '#FFFFFF',
            customRadius: 60
          }
        }
      }
    }
  },
  mounted() {
    this.getServerData()
    this.getAttendanceGroupList()
  },
  methods: {
    getServerData() {
      // 模拟从服务器获取数据时的延时
      setTimeout(() => {
        // 模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
        let res = {
          series: [
            {
              data: [
                { name: '一班', value: 50, labelShow: false },
                { name: '二班', value: 30, labelShow: false },
                { name: '三班', value: 20, labelShow: false },
                { name: '四班', value: 18, labelShow: false },
                { name: '五班', value: 8, labelShow: false }
              ]
            }
          ]
        }
        this.chartData = JSON.parse(JSON.stringify(res))
      }, 500)
    },
    /**
     * 获取考勤组列表
     */
    getAttendanceGroupList() {
      getAttendanceGroup()
        .then(res => {
          if (res.code === 0) {
            if (res.data.results.length > 0) {
              this.drowDownDatalist[1].dataList = res.data.results
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },
    /**
     *
     * @param {*} date 切换下拉列表时间
     */
    handlerItemClick(timename, index) {
      this.drowDownDatalist[index].title = timename.name
      if (timename.time) {
        if (timename.time === 'day') {
          let list = getDateRange(0, true)
          this.startDate = list[0]
          this.endDate = list[1]
        } else if (timename.time === 'week') {
          let list = getDateRange(6, true)
          this.startDate = list[0]
          this.endDate = list[1]
        } else if (timename.time === 'month') {
          let list = getDateRange(30, true)
          this.startDate = list[0]
          this.endDate = list[1]
        }
        console.log(this.startDate, this.endDate)
      }
    }
  }
}
</script>

<style lang="scss" scoped>

.choose {
  width: 750rpx;
  height: 80rpx;
  background-color: $background;
  display: flex;
  justify-content: center;
  align-items: center;
  .choose-organization,
  .choose-time {
    width: 375rpx;
    height: 80rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    select {
      border: none;
    }
  }
}
.attendance_fig {
  width: 670rpx;
  height: 370rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 40rpx;
  display: flex;
  .attendance_figs {
    width: 300rpx;
    height: 370rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    view:nth-child(1) {
      width: 300rpx;
      height: 280rpx;
    }
    view:nth-child(2) {
      font-size: 28rpx;
    }
  }
  .attendance_data {
    width: 400rpx;
    height: 370rpx;
    .app_ul {
      display: flex;
      flex-wrap: wrap;
      .app_li {
        width: 180rpx;
        height: 125rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: center;
        font-size: 30rpx;
        view:nth-child(2) {
          width: 180rpx;
          display: flex;
          justify-content: space-evenly;
          align-items: center;
          font-size: 20rpx;
          color: #666;
        }
      }
    }
  }
}
.attendance_personnel {
  width: 670rpx;
  //  height: 86rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 20rpx auto;
  .u-collapse-item {
    margin-top: 20rpx;
  }
  .u-collapse-content {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    margin-top: 6rpx;
    .align_self_left {
      align-self: flex-start;
    }
    .align_self_right {
      align-self: flex-end;
    }
  }
}
</style>

<template>
  <view id="business">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('business.reports')"
      :autoBack="true"
      :leftIconColor="color.navigation"
      placeholder
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx',zIndex :'99' }"
    ></u-navbar>
    <!--#endif -->
    <!-- <view class="choose"> -->
    <!--头部筛选 -->
    <filterLayoutComponent
      :filterDataLayoutList="menuDataList"
      @handlerItemClick="handlerMenuItemClick"
    ></filterLayoutComponent>
    <!-- 当天 当周 当月组件 -->
    <view class="times">
      <view class="times_henr">
        <u-tabs
          :list="timelist"
          lineWidth="0"
          :activeStyle="activeStyle"
          :itemStyle="{ textAlign: 'center' }"
          :inactiveStyle="inactiveStyle"
          @click="switchTime"
        ></u-tabs>
      </view>
      <view>
        <uni-calendar ref="calendar" range :insert="false" @confirm="formatter" @close="calendarClose" />
      </view>
      <view class="times_time">
        {{ TimePeriod }}
      </view>
    </view>
    <!-- 营收金额 -->
    <view class="turnover" >
      <image class="turnover_img" :src="imgPath.IMG_INDEX_BG_ORANGE_BIG" mode="scaleToFill"></image>

      <view class="turnover_left">
        <view class="li">
          <!-- {{ $t('Turnover.day') }}（{{ $t('RMB') }}） -->
          {{ thenDay }}{{ $t('business.turnover') }}（{{ $t('RMB') }}）
          <u-icon name="question-circle" color="#fff" size="24" @click="turnover = true"></u-icon>

        </view>
        <view class="li">{{ businessData.turnover }}</view>
        <view class="li" v-if="isShowCompareYes != 'all'">
          {{ yesterDay }} {{ businessData.compareTurnover }}

          <image  v-if="businessData.compareTurnover!=0" class ='arrow_style' :src="businessData.compareTurnover > 0 ? imgPath.IMG_ARROW_TOP_WHITE:imgPath.IMG_ARROW_BOTTOM_WHITE"></image>

        </view>
        <view class="li">
          {{ $t('business.Refund.amount') }}（{{ $t('RMB') }}）{{ businessData.refundAmount }}
        </view>
        <view class="li">
          {{ $t('business.number.of.consumption') }} {{ businessData.consumersNumber }}
        </view>
      </view>
      <view class="turnover_right">
        <view class="li">
          {{ $t('business.actual.amount.received') }}（{{ $t('RMB') }}）
          <u-icon name="question-circle" color="#fff" size="24" @click="received = true"></u-icon>

        </view>
        <view class="li">{{ businessData.received }}</view>
        <view class="li" v-if="isShowCompareYes != 'all'">
          {{ yesterDay }} {{ businessData.compareReceived }}
          <image  v-if="businessData.compareReceived!=0" class ='arrow_style' :src="businessData.compareReceived > 0 ? imgPath.IMG_ARROW_TOP_WHITE:imgPath.IMG_ARROW_BOTTOM_WHITE"></image>
        </view>
        <view class="li">
          {{ $t('business.how.many.consumed') }} {{ businessData.purchasesNumber }}
        </view>
        <!-- 你不加空白内容两边对不齐的，请注意-->
        <view class="li empty"></view>
      </view>
    </view>
    <!-- 营收详情 -->
    <view class="details">
      <view class="details_top">{{ $t('business.Revenue.details') }}</view>
      <emptyComponent
        :emptyContent="emptyContent"
        v-if="notData"
        :customEmptyStyle="customEmptyStyle"
      ></emptyComponent>
      <view v-if="!notData">
        <view class="detailser" v-for="(item, index) in revenueDetailsData" :key="index">
          <view class="revenue_method">{{ item.payway }}</view>
          <view class="amount_revenue">
            <view class="amount_revenue_li">
              <text>{{ $t('business.Revenue') }}</text>
              <text>￥{{ item.turnover }}</text>
            </view>
            <view class="amount_revenue_li">
              <text>{{ $t('business.actual.amount.received') }}</text>
              <text>￥{{ item.real_income }}</text>
            </view>
            <view class="amount_revenue_li">
              <text>{{ $t('business.how.many.consumed') }}</text>
              <text>{{ item.payway_count }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 档口对比 -->
    <organizationalComparison v-if="isShowOrganizationView"
      :organizationalComparisonData="organizationalComparisonData"
    ></organizationalComparison>
    <!-- 退款汇总 -->
    <view class="refund">
      <view class="refund_top">{{ $t('business.Refund.Summary') }}</view>
      <view class="refund_data">
        <view class="refund_data_left">
          <text>{{ $t('business.Refund.amount') }}</text>
          <text>￥{{ businessData.refundAmount }}</text>
        </view>
        <view class="refund_data_conter"></view>
        <view class="refund_data_right">
          <text>{{ $t('business.Number.of.refunds') }}</text>
          <text>{{ businessData.refundsNumber }}</text>
        </view>
      </view>
    </view>
    <!-- 餐段统计 -->
    <segments
      :dataList="dataList"
      @getSegments="getMealSegments"
      :current="currentIndex"
      :title="segmentsTitle"
      :menuList="mealTypeInfoList"
      ref="segments"
    ></segments>
    <u-modal
      :show="turnover"
      :content="turnovers.content"
      @confirm="turnover = false"
    ></u-modal>
    <u-modal
      :show="received"
      :content="receiveds.content"
      @confirm="received = false"
    ></u-modal>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
import emptyComponent from '@/components/EmptyComponent/EmptyComponent'
import { numberToThousands, divide, deepClone } from '@/utils/util'
import organizationalComparison from '@/pages_statistics/components/OrganizationalComparison/OrganizationalComparison.vue'
import segments from '@/pages_statistics/components/Segments/Segments.vue'
import { getBeginToday, getBeginWeek, getBeginMonth } from '@/utils/time'
import filterLayoutComponent from '@/components/FilterLayoutComponent/FilterLayoutComponent.vue'
import comDic from '@/common/comDic'
import { apiBusinessReportsList, apiBackgroundReportCenterDataReportGetMealType } from '@/api/report.js'
import { getUserOrgsList } from '@/utils/userUtil'
import cache from '@/utils/cache'
export default {
  components: {
    organizationalComparison,
    segments,
    filterLayoutComponent,
    emptyComponent
  },
  computed: {
    ...mapGetters(['img', 'color'])
  },
  data() {
    return {
      organizationalComparisonData: [], // 组织对比数据
      notData: false,
      dataList: comDic.STATISTICS_DATA_LIST, // 餐段统计的数据
      menuDataList: [
        {
          title: '全部组织',
          dataList: []
        },
        {
          title: '消费时间',
          chooseItem: '消费时间',
          dataList: [
            {
              name: '消费时间',
              type: '1'
            },
            {
              name: '支付时间',
              type: '2'
            }
          ]
        }
      ],
      radiolist: [], // 各项组织
      currentIndex: 0, // 餐段统计选中table项值
      segmentsTitle: this.$t('business.Segment.statistics'),
      orgIds: -1, // 所选组织
      selectedTime: 'day', // 所选时间段
      date_type: '1', // 时间类型
      startDate: NaN, // 开始时间
      endDate: NaN, // 结束时间
      imgPath: this.$imgPath,
      mealTypeInfoList: [], // 餐段表头列表
      revenueDetailsData: [], // 营收详情数据
      businessData: {
        // 营业的数据
        turnover: '', // 营业额
        received: '', // 实收金额
        compareTurnover: '', // 较上次营业额
        compareReceived: '', // 较上次实收金额
        refundAmount: '', // 退款金额
        refundsNumber: '', // 退款笔数
        purchasesNumber: '', // 消费笔数
        consumersNumber: '' // 消费人数
      },
      mealStatistics: {}, // 报表数据
      turnover: false, // 营业额弹窗
      received: false, // 实收金额弹窗
      thenDay: this.$t('That.day'),
      yesterDay: this.$t('Compared.yesterday'),
      turnovers: {
        // 营业额弹窗内容
        content: this.$t('business.Amount.paid')
      },
      receiveds: {
        // 实收金额弹窗内容
        content: this.$t('business.Turnover.refund')
      },
      customTime: false,
      TimePeriod: uni.$u.timeFormat(getBeginToday(), 'yyyy-mm-dd'),
      activeStyle: {
        color: '#fff',
        background: '#FD953C',
        width: ' 110rpx',
        height: '52rpx',
        textAlign: 'center',
        lineHeight: '52rpx',
        borderRadius: '8rpx'
      },
      inactiveStyle: {
        color: '#000',
        background: '#fff',
        width: ' 110rpx',
        height: '52rpx',
        textAlign: 'center',
        lineHeight: '52rpx',
        borderRadius: '8rpx'
      },
      timelist: [
        {
          name: this.$t('That.day'),
          type: 'day'
        },
        {
          name: this.$t('That.Week'),
          type: 'week'
        },
        {
          name: this.$t('That.Month'),
          type: 'month'
        },
        {
          name: this.$t('That.definition'),
          type: 'all'
        }
      ],
      // dateType: 'day',
      emptyContent: this.$t('tip.list.empty'),
      customEmptyStyle: {
        // 自定义空内容style
        marginTop: '50rpx',
        marginBottom: '50rpx'
      },
      isShowAmount: true, // 是否显示营业额层
      isShowOrganizationView: true, // 是否显示组织对比，默认是
      isShowCompareYes: true // 是否显示较昨日，默认显示
    }
  },

  mounted() {
    this.initData()
  },
  methods: {
    /**
     * 初始化数据
     */
    async initData() {
      var organId = cache.get(this.$common.KEY_USER_ORGAN)
      console.log('organId', organId)
      this.getOrgan()
      await this.getMealType(organId)
    },
    // /**
    //  * 获取该用户餐段的配置数据
    //  * @param organId  组织ID
    //  */
    // getMealType() {
    //   this.$showLoading({
    //     title: this.$t('tip.loading'),
    //     mask: true
    //   })
    //   let organId = cache.get(this.$common.KEY_USER_ORGAN)
    //   apiBackgroundReportCenterDataReportGetMealType({ org_id: organId })
    //     .then(res => {
    //       uni.hideLoading()
    //       if (res.code === 0) {
    //         console.log(res)
    //         let data = res.data || {}
    //         if (Reflect.has(data, 'meal_type_info')) {
    //           this.mealTypeInfoList = deepClone(data.meal_type_info)
    //           this.mealTypeInfoList.map(item => {
    //             item.name = item.meal_type_alias
    //             item.value = item.meal_type
    //             return item
    //           })
    //           if (this.$refs.segments && Reflect.has(this.$refs.segments, 'setMenuList')) {
    //             this.$refs.segments.setMenuList(this.mealTypeInfoList)
    //           }
    //         }
    //       }
    //     })
    //     .catch(error => {
    //       uni.hideLoading()
    //       console.log('error', error)
    //     })
    // },
    /**
     * 筛选列表点击
     * @param {*} e
     */
    handlerMenuItemClick(timename, index) {
      this.menuDataList[index].title = timename.name
      this.menuDataList[index].chooseItem = timename.name
      console.log('handlerMenuItemClick', timename)
      // 全部组织的时候显示组织对比，其他情况的时候隐藏
      if (index === 0) {
        this.isShowOrganizationView = timename.name === '全部组织'
      }
      if (timename.id) {
        this.orgIds = timename.id
        this.getBusinessReportsList()
      }
      if (timename.type) {
        this.date_type = timename.type
        this.getBusinessReportsList()
      }
    },
    /**
     * 获取时间段
     * @param {*} n
     * 根据所选的时间段获取开始和结束时间
     */
    switchTime(item) {
      switch (item.type) {
        case 'day': // 当天
          this.TimePeriod = uni.$u.timeFormat(getBeginToday(), 'yyyy-mm-dd')
          this.selectedTime = 'day'
          this.startDate = NaN // 开始时间
          this.endDate = NaN // 结束时间
          this.thenDay = this.$t('That.day')
          this.yesterDay = this.$t('Compared.yesterday')
          this.getBusinessReportsList()
          break
        case 'week': // 本周
          this.TimePeriod =
            uni.$u.timeFormat(getBeginWeek(), 'yyyy-mm-dd') +
            ' 至 ' +
            uni.$u.timeFormat(getBeginToday(), 'yyyy-mm-dd')
          this.selectedTime = 'weeky'
          this.startDate = NaN // 开始时间
          this.endDate = NaN // 结束时间
          this.thenDay = this.$t('That.Week')
          this.yesterDay = this.$t('Compared.yesterweek')
          this.getBusinessReportsList()
          break
        case 'month': // 本月
          this.TimePeriod =
            uni.$u.timeFormat(getBeginMonth(), 'yyyy-mm-dd') +
            ' 至 ' +
            uni.$u.timeFormat(getBeginToday(), 'yyyy-mm-dd')
          this.selectedTime = 'month'
          this.startDate = NaN // 开始时间
          this.endDate = NaN // 结束时间
          this.thenDay = this.$t('That.Month')
          this.yesterDay = this.$t('Compared.yestermonth')
          this.getBusinessReportsList()
          break
        case 'all': // 自定义
          this.selectedTime = 'all'
          this.$refs.calendar.open()
          this.isShowAmount = false
          break
      }
      this.isShowCompareYes = item.type
    },
    /**
     * 自定义选项确认
     * @param {*} day
     */
    formatter(day) {
      //  console.log(day)
      this.isShowAmount = true
      if (day.range.before !== '' && day.range.after !== '') {
        let startDateer = new Date(day.range.before)
        let endDateer = new Date(day.range.after)
        if (startDateer > endDateer) {
          this.startDate = uni.$u.timeFormat(endDateer, 'yyyy-mm-dd')
          this.endDate = uni.$u.timeFormat(startDateer, 'yyyy-mm-dd')
        } else {
          this.startDate = day.range.before
          this.endDate = day.range.after
        }
        this.TimePeriod = this.startDate + ' 至 ' + this.endDate
      } else {
        this.startDate = day.fulldate
        this.endDate = day.fulldate
        this.TimePeriod = day.fulldate
      }
      this.selectedTime = NaN
      this.getBusinessReportsList()
    },
    /**
     * 日期弹窗关闭
     */
    calendarClose() {
      this.isShowAmount = true
    },
    /**
     * 获取组织渲染到组织筛选
     */
    async getOrgan() {
      var filterList = deepClone(this.menuDataList)
      // 组织类型列表
      var [errorOrgan, resultOrgan] = await this.$to(getUserOrgsList(2))
      // 赋值
      if (resultOrgan && !errorOrgan) {
        resultOrgan.unshift({ name: '全部组织', id: -1 })
        filterList[0].dataList = deepClone(resultOrgan)
        filterList[0].chooseItem = "全部组织"
      }
      this.menuDataList = deepClone(filterList)
    },

    /**
     * 获取营业报表数据
     * params (object) start_date: 开始时间,
        end_date: 结束日期,
        date_type: 时间类型,
        ord_id: 组织
     */
    getBusinessReportsList() {
      let params = {
        start_date: this.startDate,
        time_type: this.selectedTime,
        end_date: this.endDate,
        date_type: this.date_type,
        org_id: [this.orgIds]
      }
      if (this.orgIds === -1) {
        delete params.org_id
      }
      // console.log(params)
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      apiBusinessReportsList(params)
        .then(res => {
          if (res.code === 0) {
            this.$refs.segments.currentIndex = 0
            uni.hideLoading()
            this.mealStatistics = res.data
            this.initTurnoverTrendLine(res.data)
          } else {
            uni.$u.toast(res.msg)
            uni.hideLoading()
            this.setContentList(false)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
          console.log(err)
          this.setContentList(false)
        })
    },
    /**
     * 初始化数据
     * @param {res.data} data
     */
    initTurnoverTrendLine(data) {
      console.log("initTurnoverTrendLine", data);
      // 营业额的入参
      this.businessData = {
        turnover: numberToThousands(divide(data.turnover)),
        received: numberToThousands(divide(data.real_income)),
        compareTurnover: numberToThousands(divide(data.last_time_turnover)),
        compareReceived: numberToThousands(divide(data.last_time_real_income)),
        refundAmount: numberToThousands(divide(data.total_consume_refund)),
        purchasesNumber: numberToThousands(data.consume_order_count),
        consumersNumber: numberToThousands(data.consume_person_count),
        refundsNumber: numberToThousands(data.total_refund_count)
      }
      // 营收详情入参
      if (data.payway_consume_data) {
        this.notData = false
        data.payway_consume_data.forEach(item => {
          // console.log(item)
          item.turnover = numberToThousands(divide(item.turnover))
          item.real_income = numberToThousands(divide(item.real_income))
          item.payway_count = numberToThousands(item.payway_count)
        })
        this.revenueDetailsData = deepClone(data.payway_consume_data)
      } else if (!data.payway_consume_data) {
        this.notData = true
      }
      // 组织对比入参
      if (data.organization_consume_data) {
        let organization = data.organization_consume_data
        organization.sort((a, b) => {
          return b.total_payfee - a.total_payfee
        })
        organization.forEach(item => {
          item.percentage = item.total_payfee / this.businessData.turnover.replace(/,/g, '')
          item.total_payfee = numberToThousands(divide(item.total_payfee))
          this.organizationalComparisonData = deepClone(organization)
        })
        // 餐段统计入参
        // this.dataList.forEach(item => {
        //   item.value = this.mealStatistics.breakfast_statistic[item.key]
        // })
        // 这里要判断第一个餐段是啥
        if (Array.isArray(this.mealTypeInfoList) && this.mealTypeInfoList.length > 0) {
          var firstItemType = this.mealTypeInfoList[0].meal_type
          console.log("firstItem", firstItemType);
          // 更新数据
          this.updateMealSegments(firstItemType)
        } else {
          this.setContentList(false)
        }
        console.log("this.dataList", this.dataList, this.mealStatistics);
      }
    },
    /**
     * 获取餐段
     */
    getMealSegments(item) {
      console.log("getMealSegments", item, this.mealStatistics);
      this.updateMealSegments(item.meal_type)
    },
    /**
     * 更新餐段统计数据
     * @param {*} type
     */
    updateMealSegments(type) {
      var tagKey = type + "_statistic"
      if (Reflect.has(this.mealStatistics, tagKey)) {
      // 如果有这个类型就赋值
        this.dataList.map(item => {
          switch (item.key) {
            case "total_amount":
            case "real_income":
            case "refund_amount":
              item.value = '￥' + numberToThousands(divide(this.mealStatistics[tagKey][item.key]))
              break;
            case 'count':
              item.value = numberToThousands(this.mealStatistics[tagKey][item.key])
              break;
            default:
              break;
          }
          return item
        })
        // 更新数据
        if (this.$refs.segments && Reflect.has(this.$refs.segments, 'setContentList')) {
          this.$refs.segments.setContentList(this.dataList)
        }
      }
    },
    /**
     * 获取该用户餐段的配置数据
     * @param organId  组织ID
     */
    getMealType(organId) {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      apiBackgroundReportCenterDataReportGetMealType({ org_id: organId })
        .then(res => {
          uni.hideLoading()
          if (res.code === 0) {
            var data = res.data || {}
            if (Reflect.has(data, 'meal_type_info')) {
              var resultList = data.meal_type_info || []
              if (resultList && resultList.length > 0) {
                resultList.map(item => {
                  item.name = item.meal_type_alias
                  item.value = item.meal_type
                  return item
                })
              }
              this.mealTypeInfoList = deepClone(resultList)
              if (this.$refs.segments && Reflect.has(this.$refs.segments, 'setMenuList')) {
                this.$refs.segments.setMenuList(this.mealTypeInfoList)
              }
            }
          }
          this.getBusinessReportsList()
        })
        .catch(error => {
          uni.hideLoading()
          console.log('error', error)
          this.getBusinessReportsList()
        })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep.u-navbar .u-navbar--fixed{
  z-index: 99 !important;
}
.times {
  width: 750rpx;
  height: 182rpx;
  .times_henr {
    width: 490rpx;
    height: 52rpx;
    margin: 40rpx auto;
    display: flex;
    justify-content: space-around;
    .times-henrli {
      width: 110rpx;
      height: 52rpx;
      background-color: $background;
      border-radius: 8rpx;
      color: $color-text;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .times_time {
    width: 100%;
    height: 27rpx;
    line-height: 27rpx;
    font-size: 36rpx;
    color: $color-text;
    margin: auto;
    text-align: center;
  }
}
.details {
  width: 670rpx;
  // height: 728rpx;
  background-color: $background;
  border-radius: 12rpx;
  margin: 20rpx auto;
  overflow: hidden;
  .details_top {
    font-size: 30rpx;
    color: $color-text;
    margin: 30rpx;
  }
  .revenue_method {
    width: 610rpx;
    height: 61rpx;
    background-color: #eff1f6;
    border-radius: 8rpx 8rpx 0rpx 0rpx;
    margin: auto;
    font-size: 28rpx;
    color: $color-text;
    padding: 15rpx;
  }
  .amount_revenue {
    width: 610rpx;
    height: 131rpx;
    background-color: $background;
    border-radius: 0rpx 0rpx 8rpx 8rpx;
    border: solid 1rpx #eae9ed;
    margin: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20rpx;
    .amount_revenue_li {
      width: 200rpx;
      height: 130rpx;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      align-content: space-evenly;
      text:nth-child(1) {
        font-size: $font-size-xs;
        color: $color-text-a;
      }
      text:nth-child(2) {
        font-size: $font-size-md;
        color: $color-text;
      }
    }
  }
}
.turnover {
  width: 670rpx;
  height: 420rpx;
  margin: auto;
  display: flex;
  justify-content: space-between;
  border-radius: 20rpx;
  overflow: hidden;
  padding: 30rpx;
  position: relative;
  .turnover_img {
    width: 670rpx;
    height: 420rpx;
    border-radius: 20rpx;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    &image {
      width: 100%;
      height: 100%;
    }
  }
  .turnover_left,
  .turnover_right {
    position: relative;
    z-index: 98;
    width: 300rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-content: space-evenly;
    .li {
      width: 300rpx;
      margin-top: 10rpx;
      color: $color-text-b;
      font-size: 24rpx;
    }
    .li:nth-child(1) {
      text-align: center;
      display: flex;
    }
    .li:nth-child(2) {
      font-size: 56rpx;
    }
    .li:nth-child(3) {
      font-size: 20rpx;
      display: flex;
    }
    .empty {
      height: 30rpx;
    }
  }
}
.refund {
  width: 670rpx;
  min-height: 238rpx;
  background-color: $background;
  border-radius: 12rpx;
  overflow: hidden;
  margin: 20rpx 40rpx;
  .refund_top {
    margin: 30rpx;
    color: $color-text;
    font-size: $font-size-md;
  }
  .refund_data {
    min-height: 100rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .refund_data_left,
    .refund_data_right {
      width: 334rpx;
      min-height: 100rpx;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
      align-content: space-around;
      text:nth-child(1) {
        width: 334rpx;
        text-align: center;
        color: $color-text-a;
        font-size: $font-size-xs;
      }
      text:nth-child(2) {
        width: 334rpx;
        text-align: center;
        color: $color-text;
        font-size: 60rpx;
        word-break:break-all;
      }
    }
    .refund_data_conter {
      width: 1px;
      height: 100rpx;
      background-color: #eae9ed;
    }
  }
}
</style>
<style lang="scss">
// #ifdef MP-WEIXIN
 ::v-deep.u-navbar .u-navbar--fixed{
  z-index: 99 !important;
}
// #endif
</style>

<template>
  <view class="collectioncodedaily">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('business.Collection.Daily')"
      :autoBack="true"
      :leftIconColor="color.navigation"
      placeholder
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx',zIndex :'99' }"
    ></u-navbar>
    <!--#endif-->
    <!--筛选  -->
    <filterLayoutComponent
      :filterDataLayoutList="menuDataList"
      @handlerItemClick="handlerMenuItemClick"
    ></filterLayoutComponent>
    <!--头部日期  -->
    <view class="title_date_level_two">{{ dateTitle }}</view>
    <!--收款内容 -->
    <view class="collectioncodedaily_amount">
      <image
        class="collectioncodedaily_amount_img"
        :src="imgPath.IMG_INDEX_BG_ORANGE_SM"
        mode="scaleToFill"
      ></image>
      <text>{{ $t('business.collection.received') }}（{{ $t('RMB') }}）</text>
      <text>{{ totalPayFee }}</text>
    </view>
    <view class="collectioncodedaily_segment">
      <view class="collectioncodedaily_segment_statistics">
        {{ $t('business.Segment.statistics') }}
      </view>
      <view class="ul" v-if="!isShowEmptyView">
        <view class="li">
          <text>{{ $t('Meal') }}</text>
          <text>{{ $t('business.collection.received') }}</text>
        </view>
        <view class="li" v-for="item in mealSegmentdata" :key="item.key">
          <text>{{ item.name }}</text>
          <text>￥{{ item.value }}</text>
        </view>
      </view>
      <EmptyComponent :emptyContent="emptyContent" v-if="isShowEmptyView" :customEmptyStyle="customEmptyStyle"></EmptyComponent>
    </view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
import cache from '@/utils/cache'
// import common from '@/common/common'
import comDic from '@/common/comDic'
import filterLayoutComponent from '@/components/FilterLayoutComponent/FilterLayoutComponent.vue'
import { apiMerchantMobileReportFormsGetPaymentDailyReport } from '@/api/report'
import { getLastDayRange, deepClone, numberToThousands, divide } from '@/utils/util'
import { getUserOrgsList } from '@/utils/userUtil'

export default {
  computed: {
    ...mapGetters(['color'])
  },
  data() {
    return {
      imgPath: this.$imgPath,
      startDate: '', // 开始时间
      endDate: '', // 结束时间
      mealSegmentdata: comDic.COLLECTION_CODE_DATA_LIST, // 餐段数据
      selectedOrgan: '', // 所选组织
      menuDataList: [
        {
          title: '今天',
          chooseItem: '今天',
          dataList: comDic.DIC_COLLECTION_CODE_DATE_LIST
        },
        {
          title: '全部组织',
          chooseItem: '全部组织',
          dataList: []
        }
      ],
      dateTitle: '', // 二级时间标题显示
      parmasOrder: {}, // 餐段的参数
      pageNo: 1, // 页码
      pageSize: 99999, // 每页显示数据，后台说改成这个，这里不要明细，只要collect里面的总数
      totalPayFee: '', // 收款金额
      emptyContent: this.$t('tip.list.empty'),
      isShowEmptyView: false, // 是否显示空页面
      customEmptyStyle: { // 自定义空内容style
        marginTop: '50rpx'
      }
    }
  },
  components: {
    filterLayoutComponent
  },
  created() {
    this.initData()
  },
  mounted() {},
  methods: {
    /**
     * 初始化数据
     */
    async initData() {
      // 获取该账号的所选的组织
      var organId = cache.get(this.$common.KEY_USER_ORGAN)
      console.log('organId', organId)
      // 进入获取昨天的日期进行查询数据、
      var dateList = getLastDayRange(1, '{y}-{m}-{d}', true)
      console.log('dataList', dateList)
      this.startDate = dateList[0] || ''
      this.endDate = dateList[1] || ''
      if (this.startDate === this.endDate) {
        this.dateTitle = this.startDate
      } else {
        this.dateTitle = dateList.join(' 至 ')
      }
      // 获取数据
      this.getCollectCodeList()
      // 获取字典
      this.getDicList()
    },
    /**
     * 获取字典列表
     */
    async getDicList() {
      var filterList = deepClone(this.menuDataList)
      // 组织类型列表
      var [errorOrgan, resultOrgan] = await this.$to(getUserOrgsList(2))
      // 赋值
      if (resultOrgan && !errorOrgan) {
        resultOrgan.unshift({ name: '全部组织', value: '' })
        filterList[1].dataList = deepClone(resultOrgan)
      }
      this.menuDataList = deepClone(filterList)
    },
    /**
     * 筛选列表点击
     * @param {*} e
     */
    handlerMenuItemClick(itemData, index) {
      console.log('handlerMenuItemClick', itemData)
      this.menuDataList[index].chooseItem = itemData.name
      this.menuDataList[index].title = itemData.name
      switch (index) {
        case 0: // 时间点击
          var timeNum = itemData.number
          var dateList = getLastDayRange(timeNum, '{y}-{m}-{d}', true)
          this.startDate = dateList[0] || ''
          this.endDate = dateList[1] || ''
          if (this.startDate === this.endDate) {
            this.dateTitle = this.startDate
          } else {
            this.dateTitle = dateList.join(' 至 ')
          }
          break
        case 1: // 组织点击
          this.parmasOrder.org_id = [itemData.id]
          if (itemData.name === '全部组织') {
            delete this.parmasOrder.org_id
          }
          break
        default:
          break
      }
      this.pageNo = 1
      this.getCollectCodeList()
    },
    /**
     * 获取设备消费数据
     */
    async getCollectCodeList() {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      // this.parmasOrder.page = this.pageNo
      // this.parmasOrder.page_size = this.pageSize
      this.parmasOrder.start_date = this.startDate
      // this.parmasOrder.start_date = '2020-01-01'
      this.parmasOrder.end_date = this.endDate
      const [errorOrder, resOrder] = await this.$to(apiMerchantMobileReportFormsGetPaymentDailyReport(this.parmasOrder))
      uni.hideLoading()
      if (errorOrder) {
        uni.$u.toast(errorOrder.message)
        return
      }
      if (resOrder.code === 0) {
        var data = resOrder.data ? resOrder.data : {}
        var flag = false
        for (let i = 0; i < Object.values(data).length; i++) {
          if (Object.values(data)[i] !== null && parseInt(Object.values(data)[i]) > 0) {
            flag = true
            break
          }
        }
        if (!flag) {
          uni.$u.toast('当前没有餐段统计数据！')
        }
        this.isShowEmptyView = !flag
        // 统计概况
        this.setConsumeTotalList(data)
      } else {
        uni.$u.toast(resOrder.msg || '获取失败')
      }
    },
    /**
     * 设置统计概况列表
     */
    setConsumeTotalList(info) {
      this.totalPayFee = info.total_pay_fee ? numberToThousands(divide(info.total_pay_fee)) : '0'
      var newList = deepClone(this.mealSegmentdata)
      newList[0].value = info.breakfast_money ? numberToThousands(divide(info.breakfast_money)) : '0'
      newList[1].value = info.lunch_money ? numberToThousands(divide(info.lunch_money)) : '0'
      newList[2].value = info.afternoon_money ? numberToThousands(divide(info.afternoon_money)) : '0'
      newList[3].value = info.dinner_money ? numberToThousands(divide(info.dinner_money)) : '0'
      newList[4].value = info.supper_money ? numberToThousands(divide(info.supper_money)) : '0'
      newList[5].value = info.morning_money ? numberToThousands(divide(info.morning_money)) : '0'
      this.mealSegmentdata = deepClone(newList)
    }
  }
}
</script>

<style lang="scss" scoped>
.choose {
  width: 750rpx;
  height: 80rpx;
  background-color: $background;
  display: flex;
  justify-content: center;
  align-items: center;
  .choose-organization,
  .choose-time {
    width: 375rpx;
    height: 80rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    select {
      border: none;
    }
  }
}

.collectioncodedaily_segmenttop {
  margin: 40rpx auto;
  text-align: center;
  font-size: 36rpx;
  color: #1d1e20;
}
.collectioncodedaily_amount {
  // background-size: cover;
  width: 670rpx;
  height: 200rpx;
  margin: 20rpx auto;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: flex-start;
  overflow: hidden;
  padding-left: 30rpx;
  position: relative;
  text {
    position: relative;
    color: #ffffff;
    z-index: 1000;
  }
  text:nth-child(2) {
    font-size: 24rpx;
  }
  text:nth-child(3) {
    font-size: 68rpx;
  }
  .collectioncodedaily_amount_img {
    width: 670rpx;
    height: 200rpx;
    border-radius: 12rpx;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
  }
}
.collectioncodedaily_segment {
  width: 670rpx;
  height: 604rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 20rpx auto;
  overflow: hidden;
  .collectioncodedaily_segment_statistics {
    margin: 30rpx;
  }
  .ul {
    width: 610rpx;
    margin: auto;
    .li {
      width: 610rpx;
      height: 71rpx;
      display: flex;
      justify-content: space-around;
      align-items: center;
      text {
        width: 300rpx;
        word-break: break-all;
        text-align: center;
      }
    }

    .li:nth-child(2n) {
      background-color: #fff;
    }
    .li:nth-child(2n-1) {
      background-color: #f7f8fa;
    }
    .li:nth-child(1) {
      background-color: #eff1f6;
    }
  }
}
</style>

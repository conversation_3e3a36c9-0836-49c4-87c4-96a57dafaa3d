<template>
  <view class="turnover" :style="{ backgroundImage: 'url(' + img.background + ')' }">
    <image  class="turnover_img" :src="imgPath.IMG_INDEX_BG_ORANGE_BIG" mode="scaleToFill"></image>

    <view class="turnover_left">
      <view class="li">
        {{ $t('Turnover.day') }}（{{ $t('RMB') }}）
        <u-icon name="question-circle" color="#fff" size="24" @click="turnover = true"></u-icon>
        <u-modal
          :show="turnover"
          :content="turnovers.content"
          @confirm="turnover = false"
        ></u-modal>
      </view>
      <view class="li">34.376.00</view>
      <view class="li">
        {{ $t('Compared.yesterday') }} +2400
        <u-icon name="arrow-upward" color="#fff" size="24"></u-icon>
      </view>
      <view class="li">{{ $t('business.Refund.amount') }}（{{ $t('RMB') }}）-354.00</view>
      <view class="li">{{ $t('business.number.of.consumption') }} 4.288</view>
    </view>
    <view class="turnover_right">
      <view class="li">
        {{ $t('business.actual.amount.received') }}（{{ $t('RMB') }}）
        <u-icon name="question-circle" color="#fff" size="24" @click="received = true"></u-icon>
        <u-modal
          :show="received"
          :content="receiveds.content"
          @confirm="received = false"
        ></u-modal>
      </view>
      <view class="li">34.022.00</view>
      <view class="li">
        {{ $t('Compared.yesterday') }} +2400
        <u-icon name="arrow-upward" color="#fff" size="24"></u-icon>
      </view>
      <view class="li">{{ $t('business.how.many.consumed') }} 4.292</view>
      <!-- 你不加空白内容两边对不齐的，请注意-->
      <view class="li empty"></view>
    </view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  computed: {
    ...mapGetters(['img', 'color'])
  },
  data() {
    return {
      imgPath: this.$imgPath,
      turnover: false, // 营业额弹窗
      received: false, // 实收金额弹窗
      turnovers: {
        content: this.$t('business.Amount.paid')
      },
      receiveds: {
        content: this.$t('business.Turnover.refund')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.turnover {
  width: 670rpx;
  height: 420rpx;
  margin: auto;
  display: flex;
  justify-content: space-between;
  border-radius: 20rpx;
  overflow: hidden;
  padding: 30rpx;
  position: relative;
  .turnover_img{
    width: 670rpx;
    height: 420rpx;
    border-radius: 20rpx;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    &image{
      width: 100%;
      height: 100%;
    }

  }
  .turnover_left,
  .turnover_right {
    position: relative;
    z-index: 1000;
    width: 300rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-content: space-evenly;
    .li {
      width: 300rpx;
      margin-top: 10rpx;
      color: $color-text-b;
      font-size: 24rpx;
    }
    .li:nth-child(1) {
      text-align: center;
      display: flex;
    }
    .li:nth-child(2) {
      font-size: 56rpx;
    }
    .li:nth-child(3) {
      font-size: 20rpx;
      display: flex;
    }
    .empty{
      height: 30rpx;
    }
  }
}
</style>

<template>
  <view class="charts-box">
    <qiunDataCharts
      type="area"
      :opts="opts"
      :chart-data="chartData"
      :tooltipFormat="analyseData==='total_amount'?'chartstemp':'chartstemps'"
    />
  </view>
</template>

<script>
import qiunDataCharts from '@/pages_statistics/components/qiun-data-charts/components/qiun-data-charts/qiun-data-charts'
export default {
  props: {
    analyseData: {
      type: String
    },
    getData: {
      type: Array,
      default: () => {
        return [0, 0, 0, 0, 0, 0]
      }
    },
    queryDateList: {
      type: Array,
      default: () => {
        return ['早餐', '午餐', '下午茶', '晚餐', '宵夜', '凌晨餐']
      }
    }
  },
  components: {
    qiunDataCharts
  },
  data() {
    return {
      chartData: {},
      opts: {
        color: ['#fd953c'],
        legend: {
          show: false
        },
        dataLabel: false,
        padding: [30, 40, 10, 20],
        xAxis: {
          disableGrid: true,
          boundaryGap: 'justify',
          fontSize: 13,
          marginTop: 15,
          labelCount: 7
        },
        yAxis: {
          disabled: true,
          gridType: 'dash',
          axisline: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        extra: {
          area: {
            type: 'curve',
            opacity: 0.4,
            addLine: true,
            width: 2,
            gradient: true,
            activeType: 'hollow'
          }
        }
      }
    }
  },
  watch: {
    queryDateList: {
      handler(newlue) {
        this.chartData = JSON.parse(
          JSON.stringify({
            categories: newlue,
            series: [
              {
                name: '',
                data: this.getData
              }
            ]
          })
        )
      },
      deep: true
    },
    getData: {
      handler(newlue) {
        this.chartData = JSON.parse(
          JSON.stringify({
            categories: this.queryDateList,
            series: [
              {
                name: '',
                data: newlue
              }
            ]
          })
        )
      },
      deep: true
    }
  },
  mounted() {
    this.getServerData()
  },
  methods: {
    getServerData() {
      let res = {
        categories: [
          this.$t('Meal.Breakfast'),
          this.$t('Meal.lunch'),
          this.$t('Meal.tea'),
          this.$t('Meal.dinner'),
          this.$t('Meal.supper'),
          this.$t('Meal.early')
        ],
        series: [
          {
            name: '',
            data: [0, 0, 0, 0, 0, 0]
          }
        ]
      }
      this.chartData = JSON.parse(JSON.stringify(res))
    }
  }
}
</script>

<style lang="scss" scoped>
/* 请根据实际需求修改父元素尺寸，组件自动识别宽高 */
.charts-box {
  width: 100%;
  height: 100%;
}
</style>

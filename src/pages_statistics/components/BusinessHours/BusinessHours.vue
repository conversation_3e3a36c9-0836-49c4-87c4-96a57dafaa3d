<template>
  <view class="times">
    <view class="times_henr">
      <u-tabs
        :list="timelist"
        lineWidth="0"
        :activeStyle="activeStyle"
        :itemStyle="{ textAlign: 'center' }"
        :inactiveStyle="inactiveStyle"
        @click="switchTime"
      ></u-tabs>
    </view>
    <view>
      <u-calendar
        :show="customTime"
        mode="range"
        startText=""
        endText=""
        rowHeight="100"
        @closeOnClickOverlay="false"
        @confirm="confirmTime"
      ></u-calendar>
    </view>
    <view class="times_time">
      {{ TimePeriod }}
    </view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
import { getBeginToday, getBeginWeek, getBeginMonth } from '@/utils/time'
export default {
  computed: {
    ...mapGetters(['img', 'color'])
  },
  data() {
    return {
      customTime: false,
      TimePeriod: uni.$u.timeFormat(getBeginToday(), 'yyyy-mm-dd'),
      activeStyle: {
        color: '#fff',
        background: '#FD953C',
        width: ' 110rpx',
        height: '52rpx',
        textAlign: 'center',
        lineHeight: '52rpx',
        borderRadius: '8rpx'

      },
      inactiveStyle: {
        color: '#000',
        background: '#fff',
        width: ' 110rpx',
        height: '52rpx',
        textAlign: 'center',
        lineHeight: '52rpx',
        borderRadius: '8rpx'
      },
      timelist: [
        {
          name: this.$t('That.day'),
          type: 'day'
        },
        {
          name: this.$t('That.Week'),
          type: 'week'
        },
        {
          name: this.$t('That.Month'),
          type: 'month'
        },
        {
          name: this.$t('That.definition'),
          type: 'all'
        }
      ],
      dateType: 'day',
      startDate: uni.$u.timeFormat(new Date(), 'yyyy-mm-dd'),
      endDate: uni.$u.timeFormat(new Date(), 'yyyy-mm-dd'),
      sportRecordList: {}
    }
  },
  methods: {
    switchTime(item) {
      switch (item.type) {
        case 'day': // 当天
          this.TimePeriod = uni.$u.timeFormat(getBeginToday(), 'yyyy-mm-dd')
          this.sportRecordList.startDate = uni.$u.timeFormat(getBeginToday(), 'yyyy-mm-dd')
          this.sportRecordList.endDate = uni.$u.timeFormat(getBeginToday(), 'yyyy-mm-dd')
          this.sportRecordList.type = 'day'
          break
        case 'week': // 本周
          this.TimePeriod =
            uni.$u.timeFormat(getBeginWeek(), 'yyyy-mm-dd') +
            ' 至 ' +
            uni.$u.timeFormat(getBeginToday(), 'yyyy-mm-dd')
          this.sportRecordList.startDate = uni.$u.timeFormat(getBeginWeek(), 'yyyy-mm-dd')
          this.sportRecordList.endDate = uni.$u.timeFormat(getBeginToday(), 'yyyy-mm-dd')
          this.sportRecordList.type = 'week'
          break
        case 'month': // 本月
          this.TimePeriod =
            uni.$u.timeFormat(getBeginMonth(), 'yyyy-mm-dd') +
            ' 至 ' +
            uni.$u.timeFormat(getBeginToday(), 'yyyy-mm-dd')
          this.sportRecordList.startDate = uni.$u.timeFormat(getBeginMonth(), 'yyyy-mm-dd')
          this.sportRecordList.endDate = uni.$u.timeFormat(getBeginToday(), 'yyyy-mm-dd')
          this.sportRecordList.type = 'month'
          break
        case 'all': // 自定义
          this.customTime = true
          break
      }
      this.$emit('switchTime', this.sportRecordList)
    },
    confirmTime(period) {
      this.customTime = false
      console.log(period)
    }
  }
}
</script>

<style lang="scss" scoped>
.times {
  width: 750rpx;
  height: 182rpx;
  .times_henr {
    width: 490rpx;
    height: 52rpx;
    margin: 40rpx auto;
    display: flex;
    justify-content: space-around;
    .times-henrli {
      width: 110rpx;
      height: 52rpx;
      background-color: $background;
      border-radius: 8rpx;
      color: $color-text;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .times_time {
    width: 100%;
    height: 27rpx;
    line-height: 27rpx;
    font-size: 36rpx;
    color: $color-text;
    margin: auto;
    text-align: center;
  }
}
</style>

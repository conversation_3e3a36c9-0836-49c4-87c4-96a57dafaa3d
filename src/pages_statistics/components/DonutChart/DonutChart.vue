<template>
  <view class="charts-box">
    <qiunDataCharts type="ring" :opts="opts" :chartData="chartData" />
  </view>
</template>

<script>
import qiunDataCharts from '../../qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue'
export default {
  props: {
    getListData: {
      type: Array,
      default: () => {
        return []
      }
    }
    // queryDateList: {
    //   type: Array,
    //   default: () => {
    //     return ['早餐', '午餐', '下午茶', '晚餐', '宵夜', '凌晨餐']
    //   }
    // }
  },
  components: {
    qiunDataCharts
  },
  data() {
    return {
      chartData: {},
      // 您可以通过修改 config-ucharts.js 文件中下标为 ['ring'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。
      opts: {
        rotate: false,
        rotateLock: false,
        color: ['#fd953c', '#ffb626', '#fa6f71'],
        padding: [5, 5, 5, 5],
        dataLabel: true,
        enableScroll: false,
        legend: {
          show: false,
          position: 'right',
          lineHeight: 25
        },
        title: {
          name: 0,
          fontSize: 20,
          color: '#000'
        },
        subtitle: {
          name: '交易笔数',
          fontSize: 8,
          color: '#666'
        },
        extra: {
          ring: {
            ringWidth: 15,
            activeOpacity: 0.5,
            activeRadius: 5,
            customRadius: 60,
            offsetAngle: 0,
            labelWidth: 15,
            border: true,
            borderWidth: 3,
            borderColor: '#FFFFFF'
          }
        }
      }
    }
  },
  watch: {
    getListData: {
      handler(newlue) {
        console.log(newlue)
        this.chartData = JSON.parse(
          JSON.stringify({
            series: [
              {
                data: newlue
              }
            ]
          })
        )
      },
      deep: true
    }
  },

  mounted() {
    this.getServerData()
  },
  methods: {
    getServerData() {
      // 模拟从服务器获取数据时的延时
      setTimeout(() => {
        // 模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
        let res = {
          series: [
            {
              data: []
            }
          ]
        }
        this.chartData = JSON.parse(JSON.stringify(res))
      }, 500)
    }
  }
}
</script>

<style scoped>
/* 请根据实际需求修改父元素尺寸，组件自动识别宽高 */
.charts-box {
  width: 100%;
  height: 100%;
}
</style>

<template>
  <view :class="showtheswitch ? 'contrast' : 'contrastmsg'">
    <view class="contrast_top">{{ $t('business.Organizational') }}</view>
    <view :class="showtheswitch ? 'contrast_stall_wrap' : 'contrast_moremsg'">
      <view class="contrast_stall" v-for="item in organizationalComparisonData" :key="item.id">
        <text>{{ item.name }}</text>
        <view class="contrast_stall_line">
          <u-line-progress
            :showText="false"
            :percentage="item.percentage"
            height="15"
            :activeColor="color.themeColor"
          ></u-line-progress>
        </view>
        <text>￥{{ item.total_payfee }}</text>
      </view>
    </view>
    <view class="contrast_more" @click="clickShowcase" v-if="isShowMore">
      {{ showcase }}
      <u-icon :name="showtheswitch ? 'arrow-down' : 'arrow-up'" color="#8f9295" size="24"></u-icon>
    </view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  props: {
    organizationalComparisonData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  computed: {
    ...mapGetters(['img', 'color'])
  },
  watch: {
    organizationalComparisonData: {
      handler(newValue) {
        // console.log(newValue)
        this.isShowMore = newValue && newValue.length > 5
      },
      deep: true
    }
  },
  data() {
    return {
      showcase: this.$t('business.See.more'),
      showtheswitch: true,
      isShowMore: true // 是否显示更多
    }
  },
  mounted() {},
  methods: {
    /**
     * 点击展示更多
     */
    clickShowcase() {
      if (this.showtheswitch) {
        this.showtheswitch = false
        this.showcase = this.$t('business.Collapse')
      } else {
        this.showtheswitch = true
        this.showcase = this.$t('business.See.more')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.contrastmsg {
  width: 670rpx;
  // height: 500rpx;
  background-color: $background;
  border-radius: 12rpx;
  margin: 20rpx 40rpx;
  overflow: hidden;
  padding: 30rpx;
}
.contrast {
  width: 670rpx;
  background-color: $background;
  border-radius: 12rpx;
  margin: 20rpx 40rpx;
  overflow: hidden;
  padding: 30rpx;
}
.contrast_moremsg {
  height: 450rpx;
  overflow-y: scroll;
}
.contrast_stall_wrap {
  max-height: 360rpx;
  overflow: hidden;
}
.contrast_more {
  width: 221rpx;
  height: 23rpx;
  font-size: $font-size-xs;
  color: $color-text-a;
  margin: 30rpx auto;
  display: flex;
  justify-content: center;
  align-items: center;
}
.contrast_top {
  // margin: 30rpx;
  font-size: $font-size-md;
  color: $color-text;
}
.contrast_stall {
  width: 600rpx;
  height: 40rpx;
  display: flex;
  justify-content: space-between;
  align-content: center;
  margin-top: 35rpx;
  text:nth-child(1) {
    height: 36rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .contrast_stall_line {
    width: 400rpx;
    height: 25rpx;
    display: flex;
    align-items: center;
  }
}
</style>

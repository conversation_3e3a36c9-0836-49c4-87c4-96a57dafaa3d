<template>
  <view class="mealside">
    <view class="mealside_top">{{ title }}</view>
    <view class="mealside_name">
      <u-tabs
        :current="currentIndex"
        lineWidth="30rpx"
        :activeStyle="{ color: color.themeColor }"
        :lineColor="color.themeColor"
        :list="mealsegmentsList"
        @click="mealsegments"
        @change="tabChange"
      ></u-tabs>
      <view class="mealside_ul" v-if="!isShowEmptyView">
        <view class="mealside_li" v-for="(item, index) in contentList" :key="index">
          <text>{{ item.name }}</text>
          <text>{{ item.value }}</text>
        </view>
      </view>
      <!-- 空白页 -->
      <emptyComponent
        :emptyContent="emptyContent"
        v-if="isShowEmptyView"
        :customEmptyStyle="customEmptyStyle"
      ></emptyComponent>
    </view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
import { deepClone } from '../../../utils/util'
import emptyComponent from '@/components/EmptyComponent/EmptyComponent'
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    menuList: {
      // tabs头数据
      type: Array,
      default: () => {
        return []
      }
    },
    dataList: {
      // 页面数据
      type: Array,
      default: () => {
        return []
      }
    },
    current: {
      type: Number,
      default: 0
    }
  },
  computed: {
    ...mapGetters(['img', 'color'])
  },
  components: { emptyComponent },
  data() {
    return {
      mealsegmentsList: this.menuList, // 菜单列表
      currentIndex: this.current ? this.current : 0, // 当前选中哪个选项
      contentList: this.dataList, // 内容列表暂存
      isShowEmptyView: false, // 是否显示空白内容
      emptyContent: this.$t('tip.list.empty'), // 空值控件提示语
      customEmptyStyle: {
        // 空值控件自定义style
        marginTop: '40rpx'
      }
    }
  },
  created() {},
  methods: {
    mealsegments(item) {
      this.$emit('getSegments', item)
    },
    /**
     * 设置表头数据
     */
    setMenuList(menuList) {
      console.log('setMenuList', menuList)
      if (!menuList || menuList.length <= 0) {
        return
      }
      this.mealsegmentsList = deepClone(menuList)
    },
    /**
     * 设置内容展示
     * @param {*} contentList
     */
    setContentList(contentList) {
      this.isShowEmptyView = !contentList || contentList.length === 0
      if (!contentList || contentList.length <= 0) {
        return
      }
      this.contentList = deepClone(contentList)
      console.log('this.contentList', this.contentList)
      // 要求前端判断数据为空
      var flag = false
      for (let i = 0; i < contentList.length; i++) {
        if (contentList[i].value !== null && parseInt(contentList[i].value.replace(/￥/g, '')) > 0) {
          flag = true
          break
        }
      }
      if (!flag) {
        this.isShowEmptyView = true
      }
    },
    /**
     * 选择tab 变更
     * @param {*} index
     */
    tabChange(e) {
      this.currentIndex = e.index
      this.$emit('segmentTabChange', e)
    }
  }
}
</script>

<style lang="scss" scoped>
.mealside {
  width: 670rpx;
  height: 500rpx;
  background-color: $background;
  border-radius: 12rpx;
  margin: 20rpx auto;
  margin-bottom: 20rpx;
  overflow: hidden;

  .mealside_top {
    margin: 30rpx;
    color: $color-text;
    font-size: $font-size-md;
  }
  .mealside_name {
    width: 600rpx;
    margin: auto;
  }
  .mealside_ul {
    width: 600rpx;
    height: 300rpx;
    margin: auto;
    background: $background;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    align-items: center;
    align-content: space-around;
    .mealside_li {
      width: 290rpx;
      height: 116rpx;
      background-color: #eff1f6;
      border-radius: 8rpx;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-around;
      align-items: center;
      align-content: space-around;
      padding: 20rpx;
      text:nth-child(1) {
        width: 100%;
        color: #8f9295;
        font-size: 24rpx;
      }
      text:nth-child(2) {
        width: 100%;
        color: #1d1e20;
        font-size: 42rpx;
      }
    }
  }
}
</style>

<template>
  <view class="statistical-overview">
    <view class="statistical_overview_title">{{ $t('business.Statistical.overview') }}</view>
    <view class="statistical_overview_table">
      <view class="statistical_overview_table_td" v-for="(item,index) in consumedInfoList" :key="index">
        <text>{{ item.name  }}</text>
        <text>{{ item.number }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    consumedInfoList: {
      type: Array,
      default: () => {
        return []
      }
    }

  }
}
</script>

<style lang="scss" scoped>
.statistical-overview {
  width: 670rpx;
  height: 320rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: auto;
  overflow: hidden;
  .statistical_overview_title {
    width: 670rpx;
    color: #1d201e;
    font-size: 30rpx;
    padding: 30rpx;
  }
  .statistical_overview_table {
    width: 670rpx;
    margin: 0 30rpx;
    .statistical_overview_table_td {
      width: 300rpx;
      height: 100rpx;
      flex-direction: column;
      display: inline-flex;
      text:nth-child(1) {
        font-size: 24rpx;
        // width: 350rpx;
        color: #8f9295;
      }
      text:nth-child(2) {
        font-size: 48rpx;
        color: #0e2435;
      }
      // background: $but;
    }
  }
}
</style>

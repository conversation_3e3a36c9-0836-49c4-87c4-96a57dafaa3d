<template>
  <view>
    <view class="input_wrap">
      <view class="flex_row_center label_text">
        <input
          type="text"
          class="inputStyle"
          ref="groupInput"
          placeholder="请输入/选择"
          v-model="groupName"
          @click="popOrganization"
          focus
        />
        <view class="input_but" @click="siftDishes">搜索</view>
      </view>
      <view v-show="isShow" class="top_level">
        <view class="flex_row_center label_text">
          <scroll-view class="ly_tree_style" scroll-y="scroll">
            <ly-tree
              ref="tree"
              :tree-data="treeData"
              nodeKey="id"
              :props="props"
              :ready="ready"
              :checkOnClickNode="true"
              :expandOnClickNode="false"
              :show-checkbox="true"
              emptyText="暂无数据"
              :defaultExpandAll="true"
              @check="handleCheck"
            ></ly-tree>
          </scroll-view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import LyTree from '@/pages_statistics/components/ly-tree/ly-tree.vue'

import {
  apiBackgroundFoodFoodSortListPost,
  apiBackgroundFoodFoodCategoryListPost
} from '@/api/report.js'
import { deepClone } from '@/utils/util'
export default {
  components: {
    LyTree
  },
  props: {
    secondCategories: {
      type: Array,
      default: () => {
        return null
      }
    }
  },
  data() {
    return {
      ready: false, // 这里用于自主控制loading加载状态，避免异步正在加载数据的空档显示“暂无数据”
      treeData: [], // 树形数据
      selectData: [], // 所选数组
      groupName: '', // input value
      isShow: null,
      props() {
        return {
          label(data, node) {
            return node.data.name
          }
        }
      },
      groups: [],
      firstLevelList: [], // 一级
      secondLevelList: [] // 二级
    }
  },
  watch: {
    secondCategories: {
      handler(newVal, oldVal) {
        console.log(newVal, oldVal)
      },
      deep: true
    }
  },
  created() {},
  mounted() {
    this.getCategory()
  },
  methods: {
    // 请求数据
    getCategory(groupNameValue) {
      let params = {
        page: 1,
        page_size: 999999
      }
      // 一级分类
      const first = this.$to(apiBackgroundFoodFoodSortListPost(params))
      // 二级分类
      const second = this.$to(apiBackgroundFoodFoodCategoryListPost(params))
      Promise.all([first, second])
        .then(res => {
          //  console.log(res)
          res.forEach((result, i) => {
            //  console.log(result, i)
            if (result[1].code === 0) {
              switch (i) {
                case 0:
                  this.firstLevelList = result[1].data.results
                  break
                case 1:
                  this.secondLevelList = result[1].data.results
                  break
              }
            }
          })
          // let list = [].concat(this.firstLevelList, this.secondLevelList)
          // console.log(22, list)
          this.firstLevelList.forEach(item => {
            if (item.name.length > 8) {
              item.name = item.name.substring(0, 8) + '...'
            }
          })
          this.secondLevelList.forEach(item => {
            if (item.name.length > 8) {
              item.name = item.name.substring(0, 6) + '...'
            }
          })
          if (groupNameValue) {
            // console.log(this.firstLevelList, this.secondLevelList)
            this.firstLevelList.push.apply(this.firstLevelList, this.secondLevelList)
            // console.log(this.firstLevelList)
            this.treeData = []
            this.firstLevelList.forEach(item => {
              if (groupNameValue === item.name) {
                this.treeData.push(item)
              }
            })
            this.treeData.forEach(item => {
              for (let h = 0; h < this.secondCategories.length; h++) {
                if (item.id === this.secondCategories[h]) {
                  item.checked = true
                }
              }
            })
          } else {
            this.treeData = this.arrayToTree(this.firstLevelList, this.secondLevelList)
            this.ready = true
          }
        })
        .catch(error => {
          console.log(error)
        })
    },
    // 组合成树形结构
    arrayToTree(first, second) {
      first.forEach(item => {
        for (let h = 0; h < this.secondCategories.length; h++) {
          if (item.id === this.secondCategories[h]) {
            item.checked = true
          }
        }
      })
      second.forEach(item => {
        for (let h = 0; h < this.secondCategories.length; h++) {
          if (item.id === this.secondCategories[h]) {
            item.checked = true
          }
        }
      })
      let result = deepClone(first)
      second.forEach(v => {
        for (let index = 0; index < result.length; index++) {
          const item = result[index]
          if (v.sort === item.id) {
            item.isDisabled = false
            if (item.children) {
              item.children.push(v)
            } else {
              item.children = [v]
            }
          } else {
            if (!item.children) {
              item.children = []
              item.isDisabled = true
            }
          }
        }
      })
      return result
    },
    // 点击复选框
    handleCheck(obj) {
      this.selectData = obj.checkedKeys
      // console.log(this.selectData)
      this.$emit('groupParam', this.selectData)
    },
    // 点击input框
    popOrganization() {
      this.isShow = !this.isShow
      this.getCategory()
    },

    // 点击添加按钮
    siftDishes() {
      this.isShow = true
      this.getCategory(this.groupName)
    }
  }
}
</script>

<style lang="scss" scoped>
.input_wrap {
  width: 400rpx;
  height: 70rpx;
  margin: auto;
  display: flex;
  flex-direction: column;
  position: relative;
  .inputStyle {
    border: #bababa 0.5rpx solid;
    border-radius: 10rpx;
    width: 300rpx;
    height: 70rpx;
    line-height: 70rpx;
    box-sizing: border-box;
    font-size: 15rpx;
  }
  .input_but {
    width: 100rpx;
    height: 50rpx;
    background-color: $but;
    color: #f5f5f5;
    margin-left: 10rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .top_level {
    z-index: 100;
    width: 300rpx;
  }
  .flex_row_center {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
  .label_text {
    font-size: 28rpx;
    color: #555555;
  }
  .ly_tree_style {
    position: absolute;
    top: 70rpx;
    left: 0rpx;
    border: #f5f5f5 1rpx solid;
    width: 298rpx;
    height: 250rpx;
    overflow-y: scroll;
    background-color: #fff;
  }
}
</style>

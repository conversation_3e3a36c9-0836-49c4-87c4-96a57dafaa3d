<template>
  <view class="deviceconsumption">
    <mescroll-uni ref="mescrollRef"   :fixed="false" :safearea="true" :bottom="0" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="{ auto: false,use:false, isLock:true }" :up="{ auto: false }" >
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('business.device.consumption')"
      :autoBack="true"
      :leftIconColor="color.navigation"
      placeholder
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    ></u-navbar>
    <!--#endif-->
    <!--筛选  -->
    <filterLayoutComponent
      :filterDataLayoutList="filterDataList"
      @handlerItemClick="handlerDropDownItemClick"
    ></filterLayoutComponent>
    <!--头部日期  -->
    <view class="deviceconsumption_time">{{ dateTitle }}</view>
    <!-- 统计概括 -->
    <statisticaloverview :consumedInfoList="consumedTotalInfoList"></statisticaloverview>
    <!-- 设备统计 -->
    <view class="devicestatistics">

      <view class="devicestatistics_name">{{ $t('business.Device.statistics') }}</view>
      <view class="devicestatistics_top">
        <u-tabs
          :current="currentIndex"
          lineWidth="60rpx"
          lineHeight="5"
          :lineColor="color.themeColor"
          :activeStyle="{
            color: color.themeColor,
            fontWeight: 'bold'
          }"
          :inactiveStyle="{
            color: '#606266'
          }"
          itemStyle=" text-align: center; padding-left:15px; padding-right: 15px; height: 40px;"
          :list="constitute"
          @change="tabMenuChange"
        ></u-tabs>
      </view>
      <view v-if="!isShowEmptyView">
      <view class="ul">
        <view class="li">
          <text>{{ $t('business.equipment') }}</text>
          <text>{{ consumedTableTitle }}</text>
        </view>
        <view class="li" v-for="(item,index) in consumedDeviceTotalList" :key="index">
          <text>{{ item.device_name }}</text>
          <text><text v-if="currentIndex!==3">¥</text>{{item.sumTotal}}</text>
        </view>
      </view>
    </view>
     <!-- 空白页 -->
     <emptyComponent :emptyContent="emptyContent" v-if="isShowEmptyView" :customEmptyStyle="customEmptyStyle"></emptyComponent>
    </view>
  </mescroll-uni>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
import statisticaloverview from '../components/StatisticalOverview/StatisticalOverview'
import filterLayoutComponent from '@/components/FilterLayoutComponent/FilterLayoutComponent.vue'
import emptyComponent from '@/components/EmptyComponent/EmptyComponent'
import { apiBackgroundReportCenterDataReportDeviceConsumeList } from '@/api/report'
import { getLastDayRange, deepClone, numberToThousands, divide } from '@/utils/util'
import comDic from '../../common/comDic'
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import { apiBackgroundDeviceDeviceDeviceType } from '@/api/dic'
import { getUserOrgsList } from '@/utils/userUtil'

export default {
  components: {
    statisticaloverview,
    filterLayoutComponent,
    emptyComponent
  },
  mixins: [MescrollMixin],
  computed: {
    ...mapGetters(['img', 'color'])
  },
  data() {
    return {
      startDate: '',
      endDate: '',
      constitute: [
        {
          name: this.$t('business.Total.consumption')
        },
        {
          name: this.$t('business.Refund.amount')
        },
        {
          name: this.$t('business.actual.amount.received')
        },
        {
          name: this.$t('business.how.many.consumed')
        }
      ],
      filterDataList: [
        {
          title: '今天',
          chooseItem: '今天',
          dataList: comDic.DIC_CONSUMPTION_DATE_LIST
        },
        {
          title: '全部组织',
          chooseItem: '全部组织',
          dataList: []
        },
        {
          title: '全部类型',
          chooseItem: '全部类型',
          dataList: []
        }

      ],
      pageNo: 1, // 页码
      pageSize: 10, // 每页显示数据
      parmas: {}, // 入参
      consumedTotalInfoList: [], // 统计总数
      consumedDeviceTotalList: [], // 设备统计
      isShowEmptyView: false, // 是否显示空白内容
      emptyContent: this.$t('tip.list.empty'),
      currentIndex: 0, // 设备统计选中table项值
      customEmptyStyle: { // 自定义空内容style
        marginTop: '50rpx'
      },
      consumedTableTitle: this.$t('business.Total.consumption'),
      dateTitle: '', // 头部日期显示
      bottomListHeight: "70rpx"
    }
  },
  mounted() {},
  created() {
    // 初始化数据
    this.initData()
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      // 进入获取昨天的日期进行查询数据、
      var dateList = getLastDayRange(1, '{y}-{m}-{d}', true)
      console.log("dataList", dateList);
      this.startDate = dateList[0] || ''
      this.endDate = dateList[1] || ''
      if (this.startDate === this.endDate) {
        this.dateTitle = this.startDate
      } else {
        this.dateTitle = dateList.join(' 至 ')
      }
      this.getDeviceConsumeList()
      // 获取字典
      this.getDicList()
    },
    /**
     * 下拉刷新返回
     */
    downCallback(page) {
      console.log(" downCallback page", page);
      this.pageNo = 1
      this.parmas.page = this.pageNo
      this.getDeviceConsumeList()
    },
    /**
     * 上拉加载更多
     * @param {*} page
     */
    upCallback(page) {
      console.log(" upCallback page", page);
      this.pageNo++
      this.parmas.page = this.pageNo
      this.getDeviceConsumeList()
    },
    /**
     *头部筛选下拉选项点击
     * @param {*} itemData 点击项数据
     * @param index 点击的项目
     */
    handlerDropDownItemClick(itemData, index) {
      console.log("handlerDropDownItemClick", itemData);
      // 点击记录赋值
      this.filterDataList[index].chooseItem = itemData.name
      this.filterDataList[index].title = itemData.name
      switch (index) {
        case 0:// 时间点击
          var timeNum = itemData.number
          var dateList = getLastDayRange(timeNum, '{y}-{m}-{d}', true)
          this.startDate = dateList[0] || ''
          this.endDate = dateList[1] || ''
          if (this.startDate === this.endDate) {
            this.dateTitle = this.startDate
          } else {
            this.dateTitle = dateList.join(' 至 ')
          }
          break;
        case 1:// 组织点击
          this.parmas.org_ids = [itemData.id]
          if (itemData.name === '全部组织') {
            delete this.parmas.org_ids
          }
          break;
        case 2:// 类型点击
          this.parmas.device_type = itemData.key
          if (itemData.name === '全部类型') {
            delete this.parmas.device_type
          }
          break;
        default:
          break;
      }
      this.pageNo = 1
      this.getDeviceConsumeList()
    },

    /**
     * 获取设备消费数据
     */
    async getDeviceConsumeList() {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      this.parmas.page = this.pageNo
      this.parmas.page_size = this.pageSize
      this.parmas.start_date = this.startDate
      this.parmas.end_date = this.endDate
      const [error, res] = await this.$to(apiBackgroundReportCenterDataReportDeviceConsumeList(this.parmas))
      uni.hideLoading()
      if (error) {
        this.mescroll.endErr()
        uni.$u.toast(error.message)
        return
      }
      if (res.code === 0) {
        var data = res.data ? res.data : {}
        // 统计概况
        if (Reflect.has(data, 'collect')) {
          this.setConsumeTotalList(data.collect)
        }
        // 设备统计
        var resultList = Reflect.has(data, "result") ? data.result : []
        resultList = this.setDeviceListValueByTabs(resultList)
        var count = data.count ? data.count : 0

        this.setConsumeDeviceTotalList(resultList, count, res.msg)

        console.log("data", data);
      } else {
        uni.$u.toast(res.msg || '获取失败')
        this.mescroll.endErr()
      }
    },
    /**
     * 设置统计概况列表
     */
    setConsumeTotalList(infoList) {
      var totalList = comDic.DIC_CONSUMPTION_TOTAL_LIST
      if (infoList && Object.keys(infoList).length > 0) {
        totalList.map((item, index) => {
          if (Reflect.has(infoList, item.value)) {
            var sign = index !== 3 ? '¥' : ''
            var total = index !== 3 ? divide(infoList[item.value]) : infoList[item.value]
            item.number = sign + numberToThousands(total)
          }
          return item
        })
        console.log("totalList", totalList);
        this.consumedTotalInfoList = deepClone(totalList)
      }
    },
    /**
     * 设置设备统计列表
     * @param {*} deviceList
     */
    setConsumeDeviceTotalList(deviceList, count, message) {
      // 没有数据
      this.isShowEmptyView = this.pageNo === 1 && (!deviceList || deviceList.length === 0)
      if (this.pageNo === 1 && deviceList && deviceList.length > 0) {
        // 首次加载数据
        console.log("首次加载数据");
        this.consumedDeviceTotalList = deepClone(deviceList)
      } else if (this.pageNo !== 1 && deviceList && deviceList.length > 0) {
        // 加载更多数据
        console.log("加载更多数据");
        this.consumedDeviceTotalList = this.consumedDeviceTotalList.concat(deviceList)
      } else {
        // 其他情况
        console.log("其他情况");
        uni.hideLoading()
        uni.$u.toast(message === '成功' ? '暂无数据' : message)
      }
      this.mescroll.setPageNum(this.pageNo)
      this.mescroll.endBySize(this.pageSize, count)
      this.$set(this, 'bottomListHeight', ((this.consumedDeviceTotalList.length + 1) * 75) + "rpx")
    },
    /**
     * 根据tab选项设置显示的值
     */
    setDeviceListValueByTabs(resultList) {
      var newList = deepClone(resultList)
      newList.map(item => {
        switch (this.currentIndex) {
          case 0: // 消费总额
            item.sumTotal = numberToThousands(divide(item.consume ? item.consume.toString() : "0"))
            break;
          case 1:// 退款总额
            item.sumTotal = numberToThousands(divide(item.refund_fee ? item.refund_fee.toString() : "0"))
            break;
          case 2:// 实收总额
            item.sumTotal = numberToThousands(divide(item.real_fee ? item.real_fee.toString() : "0"))
            break;
          case 3:// 消费笔数
            item.sumTotal = numberToThousands(item.consume_count ? item.consume_count.toString() : "0")
            break;
          default:
            break;
        }
        return item
      })
      console.log("setDeviceListValueByTabs", newList);
      return newList
    },
    /**
     * 统计tab菜单切换监听
     * @param {*} e 索引值index
     */
    tabMenuChange(e) {
      console.log("tabMenuChange", e);
      this.currentIndex = e.index
      this.consumedTableTitle = this.constitute[e.index].name
      this.consumedDeviceTotalList = this.setDeviceListValueByTabs(this.consumedDeviceTotalList)
    },
    /**
     * 获取字典列表
     */
    async getDicList() {
      var filterList = deepClone(this.filterDataList)
      // 设备类型列表
      var [errorDevice, resultDevice] = await this.$to(apiBackgroundDeviceDeviceDeviceType())
      var resultDeviceData = Reflect.has(resultDevice, 'data') ? resultDevice.data : []

      // 组织类型列表
      var [errorOrgan, resultOrgan] = await this.$to(getUserOrgsList(2))

      // 赋值
      if (resultOrgan && !errorOrgan) {
        resultOrgan.unshift({ name: '全部组织', value: '' })
        filterList[1].dataList = deepClone(resultOrgan)
      }
      if (resultDeviceData && !errorDevice) {
        resultDeviceData.unshift({ name: '全部类型', value: '' })
        filterList[2].dataList = resultDeviceData
      }

      this.filterDataList = deepClone(filterList)
    }
  }
}
</script>

<style lang="scss" scoped>
.deviceconsumption{
  height: 100vh;
}
.deviceconsumption_time{
  font-size: 36rpx;
  color:#1d1e20;
  margin: 30rpx auto;
  text-align:center;
}
.devicestatistics {
  width: 670rpx;
  min-height: 535rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 20rpx auto;
  overflow: hidden;
  .devicestatistics_name {
    margin: 30rpx 0 0 30rpx;
    font-size: 30rpx;
    color: #1d201e;
  }

  .ul {
    width: 610rpx;
    margin: 30rpx auto;
    .li {
      width: 610rpx;
      height: 71rpx;
      display: flex;
      justify-content: space-around;
      align-items: center;
      text {
        width: 300rpx;
        word-break: break-word;
        text-align: center;
      }
    }

    .li:nth-child(2n) {
      background-color: #fff;
    }
    .li:nth-child(2n-1) {
      background-color: #f7f8fa;
    }
    .li:nth-child(1) {
      background-color: #eff1f6;
    }
  }
}
</style>

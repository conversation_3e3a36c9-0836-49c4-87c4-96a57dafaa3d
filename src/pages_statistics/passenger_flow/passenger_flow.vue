<template>
  <view class="passengerflow">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('passenger.flow')"
      safeAreaInsetTop
      :autoBack="true"
      :leftIconColor="color.navigation"
      placeholder
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' ,zIndex :'99'}"
    ></u-navbar>
    <!--#endif-->
    <!--筛选  -->
    <filterLayoutComponent :filterDataLayoutList="drowDownDatalist"></filterLayoutComponent>
    <!-- 当天，本周，本月 -->
    <businessHours @switchTime="switchTime"></businessHours>
    <view class="customer_data">
      <image
        class="customer_data_img"
        :src="imgpath.IMG_INDEX_BG_ORANGE_SM"
        mode="scaleToFill"
      ></image>
      <view class="customer_data_text">
        <text>{{ $t('passenger.day') }}</text>
        <text>3,234,050</text>
        <text>{{ $t('Compared.yesterday') }} +2000</text>
      </view>
    </view>
    <view class="chart">
      <view class="chart_title">
        {{ $t('passenger.flow') }}
      </view>
      <view class="chart_content">
        <areachart></areachart>
      </view>
    </view>
    <view class="meal_segments">
      <text>{{ $t('passenger.analysis') }}</text>
      <view class="segments">
        <view class="segments_text">
          <view>{{ $t('Meal.Breakfast') }}</view>
          <view>3205人</view>
        </view>
        <u-line-progress
          :percentage="85"
          height="8"
          :showText="false"
          activeColor="#0cdfd1"
        ></u-line-progress>
      </view>
      <view class="segments">
        <view class="segments_text">
          <view>{{ $t('Meal.lunch') }}</view>
          <view>3205人</view>
        </view>
        <u-line-progress
          :percentage="80"
          height="8"
          :showText="false"
          activeColor="#fd953c"
        ></u-line-progress>
      </view>
      <view class="segments">
        <view class="segments_text">
          <view>{{ $t('Meal.tea') }}</view>
          <view>3205人</view>
        </view>
        <u-line-progress
          :percentage="40"
          height="8"
          :showText="false"
          activeColor="#ffb626"
        ></u-line-progress>
      </view>
      <view class="segments">
        <view class="segments_text">
          <view>{{ $t('Meal.dinner') }}</view>
          <view>3205人</view>
        </view>
        <u-line-progress
          :percentage="90"
          height="8"
          :showText="false"
          activeColor="#fd859d"
        ></u-line-progress>
      </view>
      <view class="segments">
        <view class="segments_text">
          <view>{{ $t('Meal.supper') }}</view>
          <view>3205人</view>
        </view>
        <u-line-progress
          :percentage="30"
          height="8"
          :showText="false"
          activeColor="#c076f9"
        ></u-line-progress>
      </view>
      <view class="segments">
        <view class="segments_text">
          <view>{{ $t('Meal.early') }}</view>
          <view>3205人</view>
        </view>
        <u-line-progress
          :percentage="83"
          height="8"
          :showText="false"
          activeColor="#4ac3f6"
        ></u-line-progress>
      </view>
    </view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
import areachart from '@/pages_statistics/components/AreaChart/AreaChart.vue'
import businessHours from '@/pages_statistics/components/BusinessHours/BusinessHours.vue'
import filterLayoutComponent from '@/components/FilterLayoutComponent/FilterLayoutComponent.vue'

export default {
  computed: {
    ...mapGetters(['img', 'color'])
  },
  components: {
    businessHours,
    areachart,
    filterLayoutComponent
  },
  data() {
    return {
      imgpath: this.$imgPath,
      drowDownDatalist: [
        {
          title: '全部组织',
          chooseItem: '第一项',
          dataList: [
            {
              name: '第一项'
            },
            {
              name: '第二项'
            },
            {
              name: '第三项'
            }
          ]
        }
      ]
    }
  },
  methods: {
    // 切换时间段
    switchTime(item) {
      setTimeout(() => {
        console.log(item)
      }, 100)
    }
  }
}
</script>

<style lang="scss" scoped>
.downlist {
  width: 750rpx;
  height: 80rpx;
  background-color: $background;
  display: flex;
  justify-content: baseline;
  align-items: center;

  .downlist_organization,
  .downlist-time {
    width: 375rpx;
    height: 80rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    select {
      border: none;
    }
  }
}
.customer_data {
  width: 670rpx;
  height: 214rpx;
  border-radius: 12rpx;
  margin: auto;
  position: relative;
  .customer_data_img {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
  }
  image {
    width: 100%;
    height: 100%;
  }
  .customer_data_text {
    width: 100%;
    height: 100%;
    line-height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    margin-left: 30rpx;
    z-index: 1000;
    text:nth-child(1) {
      font-size: 24rpx;
      color: #ffffff;
    }
    text:nth-child(2) {
      font-size: 68rpx;
      color: #ffffff;
    }
    text:nth-child(3) {
      font-size: 20rpx;
      color: #ffffff;
    }
  }
}
.chart {
  width: 670rpx;
  height: 370rpx;
  background-color: $color-white;
  border-radius: 12rpx;
  margin: auto;
  margin-top: 30rpx;
  overflow: hidden;

  .chart_title {
    width: 100%;
    height: 30rpx;
    font-size: 30rpx;
    color: $color-text;
    margin-top: 29rpx;
    margin-left: 31rpx;
  }

  .chart_content {
    width: 670rpx;
    height: 300rpx;
  }
}
.meal_segments {
  width: 670rpx;
  height: 554rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 20rpx auto;
  padding: 30rpx;
  text {
    font-size: 30rpx;
    color: #1d201e;
  }
  .segments {
    width: 610rpx;
    height: 43rpx;
    .segments_text {
      display: flex;
      justify-content: space-between;
      font-size: 24rpx;
      color: #1d1e20;
      margin: 30rpx 0 12rpx 0;
    }
  }
}
</style>

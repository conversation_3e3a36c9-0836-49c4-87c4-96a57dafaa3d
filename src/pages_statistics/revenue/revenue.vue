<template>
  <view class="revenue">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      safeAreaInsetTop
      :title="$t('business.Situation')"
      :autoBack="true"
      :leftIconColor="color.navigation"
      placeholder
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' ,zIndex :'99'}"
    ></u-navbar>
    <!--#endif-->
    <!--筛选  -->
    <filterLayoutComponent
      :filterDataLayoutList="menuDataList"
      @handlerItemClick="handlerMenuItemClick"
      :filterCustomStyle="filterCustomStyle"
      @showDropDownStatusChange="showDropDownStatusChange"
    ></filterLayoutComponent>

    <!--当天，本周，当月  -->
    <view class="times">
      <view class="times_henr">
        <u-tabs
          :list="timelistTop"
          lineWidth="0"
          :activeStyle="activeStyleTop"
          :itemStyle="{ textAlign: 'center' }"
          :inactiveStyle="inactiveStyleTop"
          @click="switchTime"
        ></u-tabs>
      </view>
      <view>
        <uni-calendar
          ref="calendar"
          range
          :insert="false"
          @confirm="formatter"
          @close="calendarClose"
        />
      </view>
      <view class="times_time">
        {{ TimePeriod }}
      </view>
    </view>
    <!-- 营业金额 实收金额 -->
    <view class="revenue_turnover">
      <image
        class="revenue_turnover_img"
        :src="imgPath.IMG_INDEX_BG_ORANGE_SM"
        mode="scaleToFill"
      ></image>
      <view class="revenue_turnover_text">
        <view class="li">{{ thenDay }}{{ $t('business.turnover') }}({{ $t('RMB') }})</view>
        <view class="li">{{ businessData.turnover }}</view>
        <view class="li" v-if="isShowCompareYes != 'all'">
          {{ yesterDay }} {{ businessData.compareTurnover }}
          <image  v-if="businessData.compareTurnover!=0" class ='arrow_style' :src="businessData.compareTurnover > 0 ? imgPath.IMG_ARROW_TOP_WHITE:imgPath.IMG_ARROW_BOTTOM_WHITE" mode="scaleToFill"></image>
        </view>
      </view>
    </view>
    <!-- 营业额分析 -->
    <view class="revenue_contenter">
      <view class="revenue_contenter_tabs">
        <view class="revenue_trend">{{ $t('business.Turnover.analysis') }}</view>
        <view class="revenue_trends">
          <!-- <u-tabs
            :list="trendone"
            lineWidth="0"
            :activeStyle="activeStyle"
            itemStyle="padding-left: 0rpx; padding-right: 0rpx;"
            :inactiveStyle="inactiveStyle"
            @click="trendoneer"
            :current="trendoneCurrent"
          ></u-tabs> -->
          <view class="revenue_tabs flex">
            <view :class="['tabs_item',trendoneCurrent==0?'tabs_active':'tabs_no_active','border-lt-lb-6']" @click="trendoneer(trendone[0],0)">{{trendone[0].name}}</view>
            <view :class="['tabs_item',trendoneCurrent==1?'tabs_active':'tabs_no_active','border-rt-rb-6']" @click="trendoneer(trendone[1],1)">{{trendone[1].name}}</view>
          </view>
        </view>
      </view>
      <view class="revenue_chart">
        <!-- <areachart
          :analyseData="analyseData"
          :queryDateList="queryDateList"
          :getData="getData"
        ></areachart> -->
        <view class="charts-box" v-if="isShowChart">
          <!-- <qiunDataCharts
            type="area"
            ontouch
            :opts="lineOpts"
            :chart-data="chartData"
            :tooltipFormat="analyseData === 'total_amount' ? 'chartstemp' : 'chartstemps'"
          /> -->
          <qiunDataCharts
            type="area"
            :opts="lineOpts"
            :chart-data="chartData"
            :tooltipFormat="analyseData === 'total_amount' ? 'chartstemp' : 'chartstemps'"
          />

        </view>
      </view>
    </view>
    <view class="revenue_consume">
      <view class="revenue_consume_left">
        <view class="text_purchases">{{ $t('business.how.many.consumed') }}</view>
        <view class="text_number">{{ businessData.purchasesNumber }}</view>
        <view class="text_of" v-if="isShowCompareYes != 'all'">
          {{ yesterDay }}
          <view :class="businessData.comparePurchasesNumber >= 0 ? 'uptrend' : 'downtrend'">
            {{ businessData.comparePurchasesNumber }}
            <image v-if="businessData.comparePurchasesNumber!=0" class ='arrow_style' :src="businessData.comparePurchasesNumber > 0 ? imgPath.IMG_ARROW_TOP_GREEN:imgPath.IMG_ARROW_BOTTOM_RED" mode="scaleToFill"></image>
          </view>
        </view>
      </view>
      <view class="revenue_consume_conter"></view>
      <view class="revenue_consume_figth">
        <view class="text_purchases">{{ $t('business.number.of.consumption') }}</view>
        <view class="text_number">{{ businessData.consumersNumber }}</view>
        <view class="text_of" v-if="isShowCompareYes != 'all'">
          {{ yesterDay }}
          <view :class="businessData.compareConsumersNumber >= 0 ? 'uptrend' : 'downtrend'">
            {{ businessData.compareConsumersNumber }}
            <image v-if="businessData.compareConsumersNumber!=0" class ='arrow_style' :src="businessData.compareConsumersNumber > 0 ? imgPath.IMG_ARROW_TOP_GREEN:imgPath.IMG_ARROW_BOTTOM_RED" mode="scaleToFill"></image>
          </view>
        </view>
      </view>
    </view>
    <!-- 组织对比 -->
    <organizationalComparison v-if="isShowOrganizationView"
      :organizationalComparisonData="organizationalComparisonData"
    ></organizationalComparison>
    <!-- 营收构成 -->
    <view class="revenue_constitute">
      <view class="revenue_constitute_figth">
        <view class="revenue_trend">{{ $t('business.Revenue.composition') }}</view>
        <view class="revenue_trends">
          <!-- <u-tabs
            :list="trendtwo"
            lineWidth="0"
            :activeStyle="activeStyle"
            itemStyle="padding-left: 0rpx; padding-right: 0rpx;"
            :inactiveStyle="inactiveStyle"
            @click="trendtwoer"
          ></u-tabs> -->
          <view class="revenue_tabs flex">
            <view :class="['tabs_item',trendtwoCurrent==0?'tabs_active':'tabs_no_active','border-lt-lb-6']" @click="trendtwoer(trendone[0],0)">{{trendtwo[0].name}}</view>
            <view :class="['tabs_item',trendtwoCurrent==1?'tabs_active':'tabs_no_active','border-rt-rb-6']" @click="trendtwoer(trendone[1],1)">{{trendtwo[1].name}}</view>
          </view>
        </view>
      </view>
      <view class="revenue_constitute_top">
        <u-tabs
          lineWidth="60rpx"
          lineHeight="5"
          :lineColor="color.themeColor"
          :current="revenueActive"
          :activeStyle="{
            color: color.themeColor,
            fontWeight: 'bold'
          }"
          :inactiveStyle="{
            color: '#606266'
          }"
          itemStyle="padding-left:15px;margin-top: 20rpx; padding-right: 15px; height: 34px;"
          :list="constitute"
          @click="constitutes"
        ></u-tabs>
      </view>
      <view class="revenue_constitute_sublevel">
        <view v-if="filterDox">
          <filterBox @groupParam="changeCategory" :secondCategories="secondCategories"></filterBox>
        </view>
        <view v-if="!notData">
          <view v-if="revenueData === 'total_amount'">
            <view
              class="revenue_contrast_stall"
              v-for="(item, index) in paymentType"
              :key="item.name"
            >
              <view class="text">{{ item.name }}</view>
              <view class="revenue_contrast_stall_line">
                <u-line-progress
                  :showText="false"
                  :percentage="item.proportion"
                  height="15"
                  :activeColor="payColor(index)"
                ></u-line-progress>
              </view>
              <view class="text">{{ item.totalamount }}</view>
            </view>
          </view>
          <view v-if="revenueData === 'total_count'">
            <view
              class="revenue_contrast_stall"
              v-for="(item, index) in paymentType"
              :key="item.name"
            >
              <view class="text">{{ item.name }}</view>
              <view class="revenue_contrast_stall_line">
                <u-line-progress
                  :showText="false"
                  :percentage="item.proportion"
                  height="15"
                  :activeColor="payColor(index)"
                ></u-line-progress>
              </view>
              <view class="text">{{ item.totalcount }}</view>
            </view>
          </view>
          <!--查看全部-->
          <view class="contrast_more" @click="clickShowAll" v-if="isShowAllView">
            {{ tipShowAll }}
            <u-icon :name="isShowTipAll ? 'arrow-down' : 'arrow-up'" color="#8f9295" size="24"></u-icon>
          </view>
        </view>
        <emptyComponent
          :customEmptyStyle="customEmptyStyle"
          :emptyContent="emptyContent"
          v-if="notData"
        ></emptyComponent>
      </view>
    </view>
    <!-- 交易笔数分析 -->
    <view class="transactions">
      <view class="transactions_top">{{ $t('business.Analysis.transactions') }}</view>
      <view class="transactions_button">
        <view class="transactions_left">
          <view class="charts-box" v-if="isShowChart">
            <qiunDataCharts
              type="ring"
              :opts="tradeOpts"
              :chartData="tradeData"
              tooltipFormat="tradeOptstemp"
            />
          </view>
        </view>
        <view class="transactions_right">
          <view v-for="(item, index) in transactions" :key="index">
            <u-badge :isDot="true" :bgColor="item.color"></u-badge>
            <view class="text">{{ item.name }}</view>
          </view>
        </view>
        <view class="transactions_right">
          <view class="text" v-for="(item, index) in transactions" :key="index">
            {{ item.label }}笔
          </view>
        </view>
      </view>
    </view>
    <!-- 钱包类型使用情况 -->
    <view class="walletusage">
      <view class="walletusage_top">{{ $t('business.Analysis.Usage') }}</view>
      <view class="walletusage_button">
        <view class="walletusage_left">
          <view class="charts-box" v-if="isShowChart">
            <qiunDataCharts
              type="ring"
              :opts="purseOpts"
              :chartData="purseDataType"
              tooltipFormat="purseOptstemp"
            />
          </view>
        </view>
        <view class="walletusage_right">
          <view v-for="(item, index) in walletusage" :key="index">
            <u-badge :isDot="true" :bgColor="item.color"></u-badge>
            <view class="text">{{ item.name }}</view>
          </view>
        </view>
        <view class="walletusage_right">
          <view class="text" v-for="(item, index) in walletusage" :key="index">
            ￥{{ item.label }}
          </view>
        </view>
      </view>
    </view>
    <!-- 钱包分析 -->
    <view class="walletanalyse">
      <view class="walletanalyse_type">
        <view class="walletanalyse_type_name">{{ $t('business.Analysis.Wallet') }}</view>
        <view class="walletanalyse_type_value">
          <u-tabs
            :list="timelist"
            lineWidth="0"
            :current="currentIndex"
            :activeStyle="activeStyle"
            itemStyle=" padding-left: 10rpx; padding-right: 0rpx;"
            :inactiveStyle="inactiveStyle"
            @click="timelistli"
            ref="segments"
          ></u-tabs>
        </view>
      </view>
      <view class="walletanalyse_man">
        <view class="walletanalyse_man_t">
          {{ TimePeriod }}
        </view>
        <view class="walletanalyse_man_ul">
          <view class="walletanalyse_man_li" v-for="(item, index) in purseData" :key="index">
            <view class="walletanalyse_man_tex_top">{{ item.name }}</view>
            <view class="walletanalyse_man_text_buttom">{{ item.value }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
import * as dayjs from 'dayjs'
import organizationalComparison from '@/pages_statistics/components/OrganizationalComparison/OrganizationalComparison.vue'
import filterBox from '@/pages_statistics/components/filterBox/filterBox.vue'
import comDic from '@/common/comDic'
import { numberToThousands, divide, deepClone } from '@/utils/util'
import { getBeginToday, getBeginWeek, getBeginMonth } from '@/utils/time'
import { apiBusinessSituationList } from '@/api/report.js'
import filterLayoutComponent from '@/components/FilterLayoutComponent/FilterLayoutComponent.vue'
import emptyComponent from '@/components/EmptyComponent/EmptyComponent'
import qiunDataCharts from '@/pages_statistics/components/qiun-data-charts/components/qiun-data-charts/qiun-data-charts'
import { getUserOrgsList } from '@/utils/userUtil'
export default {
  components: {
    organizationalComparison,
    filterBox,
    emptyComponent,
    filterLayoutComponent,
    qiunDataCharts
  },
  computed: {
    ...mapGetters(['img', 'color']),
    diffDay() {
      return this.getDiffDay(this.change_time[1], this.change_time[0]) + 1
    }
  },
  data() {
    return {
      chartData: {
        categories: [],
        series: [
          {
            name: '',
            data: []
          }
        ]
      }, // 折线图数据
      filterDox: false,
      notData: false, // 有无数据
      emptyContent: this.$t('tip.list.empty'),
      customEmptyStyle: {
        // 自定义空内容style
        marginTop: '50rpx',
        marginBottom: '50rpx'
      },
      lineOpts: comDic.LINE_OPTS,
      tradeOpts: comDic.TRADE_OPTS,
      purseOpts: comDic.PURES_OPTS,
      organizationalComparisonData: [], // 组织对比数据
      filterCustomStyle: { justifyContent: 'space-between' },
      queryDateList: deepClone(comDic.QUERY_DATA_LIST), // 时间列表
      selectTrend: 'payinfo_consume_data',
      getData: [], // 数据列表
      change_time: [], // 开始和结束时间数组
      thenDay: this.$t('That.day'),
      yesterDay: this.$t('Compared.yesterday'),
      dateType: 'day',
      radiolist: [], // 各项组织
      staging: [], // 暂存数据
      org_ids: -1, // 所选组织
      imgPath: this.$imgPath,
      selectedTime: 'day', // 所选时间段
      startDate: NaN, // 开始时间
      endDate: NaN, // 结束时间
      categories: NaN, // 菜品选择项
      revenueData: 'total_amount', // 营收构成的类型total_amount：营业额 total_count：消费笔数
      analyseData: 'total_amount', // 营业额分析的类型total_amount：营业额 total_count：消费笔数
      purseData: [], // 钱包分析数据
      purseDataType: {}, // 钱包类型图表数据
      tradeData: {}, // 交易笔数图表数据
      secondCategories: [], // 二级分类id
      TimePeriod: uni.$u.timeFormat(getBeginToday(), 'yyyy-mm-dd'),
      businessData: {
        // 营业的数据
        turnover: '', // 营业额
        compareTurnover: '', // 较上次营业额
        purchasesNumber: '', // 消费笔数
        comparePurchasesNumber: '', // 较上次消费笔数
        consumersNumber: '', // 消费人数
        compareConsumersNumber: '' // 较上次消费人数
      },
      menuDataList: [
        {
          title: '全部组织',
          // chooseItem: "第一项",
          dataList: []
        }
      ],
      activeStyleTop: {
        color: '#fff',
        background: '#FD953C',
        width: ' 110rpx',
        height: '52rpx',
        textAlign: 'center',
        lineHeight: '52rpx',
        borderRadius: '8rpx'
      },
      inactiveStyleTop: {
        color: '#000',
        background: '#fff',
        width: ' 110rpx',
        height: '52rpx',
        textAlign: 'center',
        lineHeight: '52rpx',
        borderRadius: '8rpx'
      },
      activeStyle: {
        color: '#fff',
        background: '#FD953C',
        width: '110rpx',
        height: '36rpx',
        fontSize: '18rpx',
        textAlign: 'center',
        lineHeight: '36rpx',
        border: '1rpx solid #fd953c',
        borderRadius: '6rpx'
      },
      inactiveStyle: {
        color: '#000',
        background: '#fff',
        width: ' 110rpx',
        height: '36rpx',
        fontSize: '18rpx',
        textAlign: 'center',
        lineHeight: '36rpx',
        border: '1rpx solid #e0e0e0',
        borderRadius: '6rpx'
      },
      transactions: comDic.CONSUME_COUNT_DATA, // 交易笔数分析数据
      timelistTop: comDic.TIME_SWITCHING,
      walletusage: comDic.WALLETUSAGE_DATA, // 钱包类型使用情况
      walleTstatistic: comDic.WALLE_STATISTI, // 储值钱包
      subsidyStatistic: comDic.SUBSIDY_STATISTIC, // 补贴钱包类型
      complimentaryStatistic: comDic.COMPLIMENTARY_STATISTIC, // 赠送钱包类型
      currentIndex: 0, // 钱包分析tabs的索引值
      revenueActive: 0, // 营收构成tabs的索引值
      trendone: comDic.TREND_ONE_DATA,
      trendtwo: comDic.TREND_TWO_DATA,
      timelist: comDic.STATISTIC_TABS,
      constitute: comDic.CONSTITUTE_DATA,
      paymentType: [],
      isShowChart: true, // 是否显示图表
      trendoneCurrent: 0, // 营业额分析tab点击,
      trendtwoCurrent: 0, // 营收构成tab点击,
      tabItemCommonStyle: {// tabs通用CSS

      },
      isShowOrganizationView: true, // 是否显示组织对比视图，只有在全部组织的时候才会显示
      tipShowAll: this.$t('tip.view.all'), // 提示查看全部
      isShowTipAll: true, // 是否查看更多与收起的提示
      isShowAllView: false, // 是否显示更多这个图层，需要数量大于5的时候才显示
      isShowCompareYes: true// 是否显示比较昨日数据，如果是自定义时间则不显示

    }
  },
  created() {
    this.getapiBusinessSituationList()
  },
  mounted() {
    this.getOrgan()
  },
  methods: {
    /**
     * 获取经营情况数据
     */
    getapiBusinessSituationList() {
      this.currentIndex = 0
      this.filterDox = false
      let params = {
        start_date: this.startDate,
        time_type: this.selectedTime,
        end_date: this.endDate,
        org_id: [this.org_ids],
        second_categories: this.categories
      }
      // console.log(params);
      if (this.org_ids === -1) {
        delete params.org_id
      }
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      apiBusinessSituationList(params)
        .then(res => {
          if (res.code === 0) {
            uni.hideLoading()
            console.log(res)
            this.staging = []
            this.staging = res.data
            this.initTurnoverTrendLine(res.data)
          } else {
            uni.$u.toast(res.msg)
            uni.hideLoading()
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
          console.log(err)
        })
    },
    // 支付类型进度条颜色
    payColor(id) {
      if (id === 0) {
        return '#03CDFE'
      } else if (id === 1) {
        return '#7E86FF'
      } else if (id === 2) {
        return '#05DACB'
      } else if (id === 3) {
        return '#49D96C'
      } else if (id === 4) {
        return '#569BFE'
      } else if (id === 5) {
        return '#FFB72F'
      }
    },
    /**
     * 初始化数据
     */
    initTurnoverTrendLine(data) {
      // 营业数据入参
      this.businessData = {
        turnover: numberToThousands(divide(data.turnover)),
        compareTurnover: numberToThousands(divide(data.last_time_turnover)),
        purchasesNumber: numberToThousands(data.consume_order_count),
        comparePurchasesNumber: numberToThousands(data.last_time_consume_order_count),
        consumersNumber: numberToThousands(data.consume_person_count),
        compareConsumersNumber: numberToThousands(data.last_time_consume_person_count)
      }
      // 组织对比入参
      if (data.organization_consume_data) {
        let organization = data.organization_consume_data
        organization.sort((a, b) => {
          return b.total_payfee - a.total_payfee
        })
        organization.forEach(item => {
          item.percentage = item.total_payfee / this.businessData.turnover.replace(/,/g, '')
          item.total_payfee = numberToThousands(divide(item.total_payfee))
          this.organizationalComparisonData = deepClone(organization)
        })
      }
      // 营业额分析数据
      if (data.turnover_statistic.length > 0) {
        var newList = []
        this.getData = []
        this.queryDateList.map(item => {
          let value = 0
          data.turnover_statistic.forEach(v => {
            if (item === v.x_axis) {
              newList.push(item)
              if (this.analyseData === 'total_amount') {
                value = divide(v.total_amount)
              } else if (this.analyseData === 'total_count') {
                value = v.total_count
              }
              this.getData.push(Number(value))
            }
          })
          return { value, item }
        })
        this.chartData = JSON.parse(
          JSON.stringify({
            categories: newList,
            series: [
              {
                name: '',
                data: this.getData
              }
            ]
          })
        )
        console.log("this.queryDateList", this.queryDateList, this.getData);
      } else {
        if (this.selectedTime === 'day') {
          this.getData = [0, 0, 0, 0, 0, 0]
        } else if (this.selectedTime === 'weeky') {
          this.getData = [0, 0, 0, 0, 0, 0, 0]
        } else if (this.selectedTime === "month") {
          this.getData = [0, 0, 0, 0]
        } else {
          this.getData = []
        }

        this.chartData = JSON.parse(
          JSON.stringify({
            categories: this.queryDateList,
            series: [
              {
                name: '',
                data: this.getData
              }
            ]
          })
        )
      }

      // 营收构成数据
      if (this.selectTrend === 'payinfo_consume_data') {
        if (data.revenue_composition.payinfo_consume_data) {
          this.processingType(data.revenue_composition.payinfo_consume_data)
        } else {
          this.notData = true
        }
      } else if (this.selectTrend === 'meal_type_consume_data') {
        if (data.revenue_composition.meal_type_consume_data) {
          this.processingType(data.revenue_composition.meal_type_consume_data)
        } else {
          this.notData = true
        }
      } else if (this.selectTrend === 'order_type_consume_data') {
        if (data.revenue_composition.order_type_consume_data) {
          this.processingType(data.revenue_composition.order_type_consume_data)
        } else {
          this.notData = true
        }
      } else if (this.selectTrend === 'food_sales_consume_data') {
        this.filterDox = true
        if (data.revenue_composition.food_sales_consume_data) {
          this.processingType(data.revenue_composition.food_sales_consume_data)
        } else {
          this.notData = true
        }
      }
      if (data.revenue_composition.second_categories) {
        this.secondCategories = data.revenue_composition.second_categories
      } else {
        this.secondCategories = []
      }

      // 交易笔数分析
      if (data.wallet_data.walle_tstatistic) {
        let sum = 0
        this.tradeData = {}
        this.transactions.forEach(item => {
          item.label = numberToThousands(data.consume_count_data[item.key])
          item.value = Number(data.consume_count_data[item.key])
          sum += Number(item.value)
        })
        this.tradeData = JSON.parse(
          JSON.stringify({
            series: [
              {
                data: this.transactions
              }
            ]
          })
        )
        this.tradeOpts.subtitle.name = '交易笔数'
        this.tradeOpts.title.name = sum.toString()
      }
      // 钱包类型使用情况数据
      if (data.wallet_data.walle_tstatistic) {
        let sum = 0
        this.purseDataType = {}
        if (data.wallet_data.wallet_type_statistic) {
          this.walletusage.forEach(item => {
            item.label = numberToThousands(divide(data.wallet_data.wallet_type_statistic[item.type]))
            item.value = Number(divide(data.wallet_data.wallet_type_statistic[item.type]))
            sum += Number(item.value)
          })
          console.log("this.walletusage", this.walletusage);
          // this.purseDataType = deepClone(this.walletusage)
          this.purseDataType = JSON.parse(
            JSON.stringify({
              series: [
                {
                  data: this.walletusage
                }
              ]
            })
          )
          this.purseOpts.subtitle.name = '钱包使用金额'
          this.purseOpts.title.name = isNaN(sum) ? 0 : sum.toFixed(2).toString()
          console.log("sum", sum, isNaN(sum));
        }
      }
      // 钱包分析数据
      if (data.wallet_data.walle_tstatistic) {
        this.processWalletTypes(data.wallet_data.walle_tstatistic, this.walleTstatistic)
      } else if (data.wallet_data.subsidy_statistic) {
        this.processWalletTypes(data.wallet_data.subsidy_statistic, this.subsidyStatistic)
      } else if (data.complimentary_statistic) {
        this.processWalletTypes(
          data.wallet_data.complimentary_statistic,
          this.complimentaryStatistic
        )
      }
    },
    /**
     *
     *计算两个时间相差的天数， value1要大于value2
     */

    getDiffDay(value1, value2) {
      return dayjs(value1).diff(value2, 'day')
    },
    /**
     * 获取组织渲染到组织筛选
     */
    async getOrgan() {
      var filterList = deepClone(this.menuDataList)
      // 组织类型列表
      var [errorOrgan, resultOrgan] = await this.$to(getUserOrgsList(2))
      // 赋值
      if (resultOrgan && !errorOrgan) {
        resultOrgan.unshift({ name: '全部组织', id: -1 })
        filterList[0].dataList = deepClone(resultOrgan)
        filterList[0].chooseItem = '全部组织'
      }
      this.menuDataList = deepClone(filterList)
    },
    /**
     * 获取时间数组
     */
    setDateList() {
      let dataList = []
      let minDate = this.change_time[0]
      if (this.diffDay > 0) {
        for (let index = 0; index < this.diffDay; index++) {
          let date = dayjs(minDate).add(index, 'day').format('MM.DD')
          dataList.push(date)
        }
      } else {
        dataList = [minDate]
      }
      return dataList
    },
    /**
     * 营收构成里的切换tabs
     * @param {*} item
     */
    constitutes(item) {
      //  console.log(item)
      this.selectTrend = item.type
      this.revenueActive = item.index
      switch (item.type) {
        case 'payinfo_consume_data': // 支付类型
          this.filterDox = false
          this.processingType(this.staging.revenue_composition.payinfo_consume_data)
          break
        case 'meal_type_consume_data': // 餐段
          this.filterDox = false
          this.processingType(this.staging.revenue_composition.meal_type_consume_data)
          break
        case 'order_type_consume_data': // 就餐方式
          this.filterDox = false
          this.processingType(this.staging.revenue_composition.order_type_consume_data)
          break
        case 'food_sales_consume_data': // 菜品
          this.filterDox = true
          if (this.staging.revenue_composition.food_sales_consume_data) {
            this.processingType(this.staging.revenue_composition.food_sales_consume_data)
          } else {
            this.notData = true
          }

          break
      }
    },
    /**
     * 处理营收构成的四种类型
     */
    processingType(type) {
      if (type) {
        this.notData = false
        let organization = type
        // console.log(type)
        if (this.trendtwoCurrent === 0) {
          organization.sort((a, b) => {
            return b.total_amount - a.total_amount
          })
        } else {
          organization.sort((a, b) => {
            return b.total_count - a.total_count
          })
        }
        organization.forEach(item => {
          item.totalamount = '￥' + numberToThousands(divide(item.total_amount))
          item.totalcount = numberToThousands(item.total_count) + '笔'
          item.proportion = this.trendtwoCurrent === 0 ? item.total_amount / this.businessData.turnover.replace(/,/g, '') : (item.total_count / this.businessData.purchasesNumber.replace(/,/g, '')) * 100
        })
        // 只展示前50条
        organization = organization.length > 50 ? organization.slice(0, 50) : organization
        // 判断查看全部有没有
        this.paymentType = this.isShowTipAll && organization.length > 5 ? deepClone(organization.slice(0, 5)) : deepClone(organization)
        this.isShowAllView = organization.length > 5 && this.selectTrend === "food_sales_consume_data"
        console.log("this.paymentType", this.paymentType);
      } else {
        this.notData = true
      }
    },
    /**
     * 切换钱包类型
     * @param {*} item
     */
    timelistli(item) {
      this.currentIndex = item.index
      switch (item.key) {
        case 'walle_tstatistic': // 储值钱包类型
          this.processWalletTypes(this.staging.wallet_data.walle_tstatistic, this.walleTstatistic)
          break
        case 'subsidy_statistic': // 补贴钱包类型
          this.processWalletTypes(this.staging.wallet_data.subsidy_statistic, this.subsidyStatistic)
          break
        case 'complimentary_statistic': // 赠送钱包类型
          this.processWalletTypes(
            this.staging.wallet_data.complimentary_statistic,
            this.complimentaryStatistic
          )
          break
      }
    },
    /**
     * 处理钱包分析的三种类型
     */
    processWalletTypes(type, data) {
      if (type) {
        data.forEach(item => {
          item.value = '￥' + numberToThousands(divide(type[item.type]))
        })
        this.purseData = deepClone(data)
      }
    },
    /**
     * 营收构成切换营业额和消费笔数
     * @param {*} item
     */
    trendtwoer(item, index) {
      // console.log('营收构成', item.key      total_amount      total_count  )
      this.revenueData = item.key
      this.trendtwoCurrent = index
      var currentItem = {
        type: this.selectTrend,
        index: this.revenueActive
      }
      this.constitutes(currentItem)
    },
    /**
     * 营业额分析里切换营业额和消费笔数chartData
     * @param {*} item
     */
    trendoneer(item, index) {
      this.analyseData = item.key
      this.trendoneCurrent = index
      this.getapiBusinessSituationList()
    },
    /**
     * 筛选列表点击
     * @param {*} e
     */
    handlerMenuItemClick(timeName, index) {
      console.log('timename', timeName)
      this.menuDataList[index].title = timeName.name
      this.org_ids = timeName.id
      this.isShowOrganizationView = timeName.name === '全部组织'
      this.getapiBusinessSituationList()
    },
    // 切换时间段
    switchTime(item) {
      console.log("item", item);
      switch (item.type) {
        case 'day': // 当天

          this.TimePeriod = uni.$u.timeFormat(getBeginToday(), 'yyyy-mm-dd')
          this.selectedTime = 'day'
          this.startDate = NaN // 开始时间
          this.endDate = NaN // 结束时间
          this.thenDay = this.$t('That.day')
          this.yesterDay = this.$t('Compared.yesterday')
          this.change_time = [
            uni.$u.timeFormat(getBeginToday(), 'yyyy-mm-dd'),
            uni.$u.timeFormat(getBeginToday(), 'yyyy-mm-dd')
          ]
          this.queryDateList = [
            this.$t('Meal.Breakfast'),
            this.$t('Meal.lunch'),
            this.$t('Meal.tea'),
            this.$t('Meal.dinner'),
            this.$t('Meal.supper'),
            this.$t('Meal.early')
          ]
          this.$set(this.lineOpts.xAxis, 'labelCount', this.queryDateList.length + 1)
          this.chartData = JSON.parse(
            JSON.stringify({
              categories: this.queryDateList,
              series: [
                {
                  name: '',
                  data: this.getData
                }
              ]
            })
          )
          this.getapiBusinessSituationList()
          break
        case 'week': // 本周

          this.TimePeriod =
            uni.$u.timeFormat(getBeginWeek(), 'yyyy-mm-dd') +
            ' 至 ' +
            uni.$u.timeFormat(getBeginToday(), 'yyyy-mm-dd')
          this.selectedTime = 'weeky'
          this.startDate = NaN // 开始时间
          this.endDate = NaN // 结束时间
          this.thenDay = this.$t('That.Week')
          this.yesterDay = this.$t('Compared.yesterweek')
          this.change_time = [
            uni.$u.timeFormat(getBeginWeek(), 'yyyy-mm-dd'),
            uni.$u.timeFormat(getBeginToday(), 'yyyy-mm-dd')
          ]
          this.queryDateList = [
            this.$t('statistics.revenue.monday'),
            this.$t('statistics.revenue.tuesday'),
            this.$t('statistics.revenue.wednesday'),
            this.$t('statistics.revenue.thursday'),
            this.$t('statistics.revenue.friday'),
            this.$t('statistics.revenue.saturday'),
            this.$t('statistics.revenue.sunday')
          ]
          this.$set(this.lineOpts.xAxis, 'labelCount', 8)

          this.chartData = JSON.parse(
            JSON.stringify({
              categories: this.queryDateList,
              series: [
                {
                  name: '',
                  data: this.getData
                }
              ]
            })
          )
          this.getapiBusinessSituationList()
          break
        case 'month': // 本月
          this.TimePeriod =
            uni.$u.timeFormat(getBeginMonth(), 'yyyy-mm-dd') +
            ' 至 ' +
            uni.$u.timeFormat(getBeginToday(), 'yyyy-mm-dd')
          this.selectedTime = 'month'
          this.startDate = NaN // 开始时间
          this.endDate = NaN // 结束时间
          this.thenDay = this.$t('That.Month')
          this.yesterDay = this.$t('Compared.yestermonth')
          this.change_time = [
            uni.$u.timeFormat(getBeginMonth(), 'yyyy-mm-dd'),
            uni.$u.timeFormat(getBeginToday(), 'yyyy-mm-dd')
          ]
          this.queryDateList = this.setDateList()
          this.$set(this.lineOpts.xAxis, 'labelCount', 4)
          this.chartData = JSON.parse(
            JSON.stringify({
              categories: this.queryDateList,
              series: [
                {
                  name: '',
                  data: this.getData
                }
              ]
            })
          )
          this.getapiBusinessSituationList()
          break
        case 'all': // 自定义
          this.selectedTime = 'all'
          this.$refs.calendar.open()
          this.setHideChartsView(false)

          break
      }
      this.isShowCompareYes = item.type
      console.log("this.isShowCompareYes", this.isShowCompareYes)
    },
    /**
     * 自定义选项确认
     * @param {*} day
     */
    formatter(day) {
      console.log(day)
      this.$set(this.lineOpts.xAxis, 'labelCount', 4)
      this.setHideChartsView(true)
      if (day.range.before !== '' && day.range.after !== '') {
        let startDateer = new Date(day.range.before)
        let endDateer = new Date(day.range.after)
        if (startDateer > endDateer) {
          this.startDate = uni.$u.timeFormat(endDateer, 'yyyy-mm-dd')
          this.endDate = uni.$u.timeFormat(startDateer, 'yyyy-mm-dd')
        } else {
          this.startDate = day.range.before
          this.endDate = day.range.after
        }
        this.TimePeriod = this.startDate + ' 至 ' + this.endDate
      } else {
        this.startDate = day.fulldate
        this.endDate = day.fulldate
        this.TimePeriod = day.fulldate
      }
      this.selectedTime = NaN
      this.change_time = [this.startDate, this.endDate]
      this.queryDateList = this.setDateList()
      this.chartData = JSON.parse(
        JSON.stringify({
          categories: this.queryDateList,
          series: [
            {
              name: '',
              data: this.getData
            }
          ]
        })
      )
      this.getapiBusinessSituationList()
    },
    /**
     * 日历关闭
     */
    calendarClose() {
      this.setHideChartsView(true)
    },
    /**
     * 点击菜品筛选
     * @param {*} e
     */
    changeCategory(e) {
      this.categories = e
      setTimeout(() => {
        this.getapiBusinessSituationList()
      }, 1000)
    },
    /**
     * 设置图表页面显示隐藏
     * 注意：小程序canvas是最高层级，弹窗时图表会浮在上面，所以要及时隐藏图表，弹窗结束就显示图表
     * @param {*} isShowChart 是否显示chart
     */
    setHideChartsView(isShowChart) {
      // #ifdef MP-WEIXIN || MP-ALIPAY
      this.isShowChart = isShowChart
      // #endif
    },
    /**
     * 监听下拉弹窗框是否显示
     * @param {*} e
     */
    showDropDownStatusChange(e) {
      console.log('showDropDownStatusChange', e)
      this.setHideChartsView(!e)
    },
    /**
     * 点击查看全部
     */
    clickShowAll() {
      this.isShowTipAll = !this.isShowTipAll
      this.tipShowAll = !this.isShowTipAll ? this.$t('business.Collapse') : this.$t('tip.view.all')
      this.processingType(this.staging.revenue_composition.food_sales_consume_data)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep.u-navbar .u-navbar--fixed{
  z-index: 99 !important;
}
.revenue {
  .revenue_data {
    width: 200rpx;
    display: flex;
    color: #666;
    font-size: 40rpx;
    justify-content: center;
    align-items: center;
    margin: 80rpx auto;
  }
  .times {
    width: 750rpx;
    height: 182rpx;
    .times_henr {
      width: 490rpx;
      height: 52rpx;
      margin: 40rpx auto;
      display: flex;
      justify-content: space-around;
    }
    .times_time {
      width: 100%;
      height: 27rpx;
      line-height: 27rpx;
      font-size: 36rpx;
      color: $color-text;
      margin: auto;
      text-align: center;
    }
  }
  .downlist {
    width: 750rpx;
    height: 80rpx;
    background-color: $background;
    display: flex;
    justify-content: baseline;
    align-items: center;

    .downlist_organization,
    .downlist-time {
      width: 375rpx;
      height: 80rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      select {
        border: none;
      }
    }
  }
  .revenue_contenter {
    width: 670rpx;
    // height: 370rpx;
    background-color: $color-white;
    border-radius: 12rpx;
    margin: auto;
    margin-top: 30rpx;
    overflow: hidden;
    min-height: 410rpx;

    .revenue_contenter_tabs {
      width: 600rpx;
      height: 30rpx;
      font-size: 30rpx;
      color: $color-text;
      margin-top: 29rpx;
      margin-left: 31rpx;
      display: flex;
      justify-content: space-between;
      .revenue_trend {
        width: 300rpx;
      }
      .revenue_trends {
        display: flex;
        .trend-l {
          width: 120rpx;
          height: 40rpx;
          background-color: #fd953c;
          border: 1rpx solid #e0e0e0;
          font-size: 18rpx;
          display: flex;
          justify-content: center;
          line-height: 40rpx;
          border-radius: 6rpx 0rpx 0rpx 6rpx;
        }
        .trend-r {
          width: 120rpx;
          height: 40rpx;
          background-color: #fff;
          border-top: 1rpx solid #e0e0e0;
          border-bottom: 1rpx solid #e0e0e0;
          border-right: 1rpx solid #e0e0e0;
          font-size: 18rpx;
          display: flex;
          justify-content: center;
          line-height: 40rpx;
          border-radius: 0rpx 6rpx 6rpx 0rpx;
        }
      }
    }

  }
  .revenue_consume {
    width: 670rpx;
    height: 220rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    margin: 20rpx auto;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    .revenue_consume_left,
    .revenue_consume_figth {
      width: 300rpx;
      height: 200rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      align-items: center;
      .text_purchases {
        width: 300rpx;
        height: 23rpx;
        text-align: center;
        font-size: 24rpx;
        color: #8f9295;
      }
      .text_number {
        width: 200rpx;
        height: 60rpx;
        font-size: 60rpx;
        text-align: center;
        color: #0e2435;
      }
      .text_of {
        width: 300rpx;
        height: 25rpx;
        font-size: 20rpx;
        // text-align: center;
        color: #8f9295;
        display: flex;
        justify-content: center;
        align-items: center;
        .uptrend {
          line-height: 25rpx;
          color: #0fc27e;
          margin-left: 10rpx;
        }
        .downtrend {
          line-height: 25rpx;
          color: #ff5f5f;
          margin-left: 10rpx;
        }
      }
    }
    .revenue_consume_conter {
      width: 2rpx;
      height: 121rpx;
      background-color: #eae9ed;
    }
  }
  .revenue_constitute {
    width: 670rpx;
    min-height: 100rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    margin: 20rpx auto;
    overflow: hidden;
    .revenue_constitute_figth {
      width: 600rpx;
      height: 30rpx;
      font-size: 30rpx;
      color: $color-text;
      margin-top: 29rpx;
      margin-left: 31rpx;
      display: flex;
      justify-content: space-between;
      .revenue_trend {
        width: 300rpx;
      }
      .revenue_trends {
        display: flex;
        .trend-l {
          width: 120rpx;
          height: 40rpx;
          background-color: #fd953c;
          border: 1rpx solid #e0e0e0;
          font-size: 18rpx;
          display: flex;
          justify-content: center;
          line-height: 40rpx;
          border-radius: 6rpx 0rpx 0rpx 6rpx;
        }
        .trend-r {
          width: 120rpx;
          height: 40rpx;
          background-color: #fff;
          border-top: 1rpx solid #e0e0e0;
          border-bottom: 1rpx solid #e0e0e0;
          border-right: 1rpx solid #e0e0e0;
          font-size: 18rpx;
          display: flex;
          justify-content: center;
          line-height: 40rpx;
          border-radius: 0rpx 6rpx 6rpx 0rpx;
        }
      }
    }
    .revenue_constitute_sublevel {
      // overflow-y: scroll;
      // padding: 20rpx;
      .revenue_contrast_stall {
        width: 600rpx;
        height: 25rpx;
        display: flex;
        justify-content: space-between;
        align-content: center;
        margin: 35rpx auto;
        .text {
          width: 200rpx;
          height: 36rpx;
          display: flex;
          justify-content: flex-start;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .revenue_contrast_stall_line {
          width: 290rpx;
          height: 25rpx;
          margin: 0 30rpx 0 30rpx;
          display: flex;
          align-items: center;
        }
      }
    }

  }
  .transactions {
    width: 670rpx;
    height: 374rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    margin: 20rpx auto;
    overflow: hidden;
    // display: flex;
    .transactions_top {
      width: 670rpx;
      height: 50rpx;
      color: #1d201e;
      font-size: 30rpx;
      margin-left: 30rpx;
      margin-top: 30rpx;
    }
    .transactions_button {
      display: flex;
      .transactions_left {
        width: 320rpx;
        height: 320rpx;
        .charts-box {
          width: 100%;
          height: 100%;
        }
      }
      .transactions_right {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: center;
        width: 170rpx;
        margin-top: 35rpx;
        height: 260rpx;
        color: #666;
        view {
          width: 150rpx;
          font-size: 24rpx;
          color: #1d1e20;
          display: flex;
          justify-content: space-around;
          align-items: center;
        }
      }
    }
  }
  .walletusage {
    width: 670rpx;
    height: 374rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    margin: 20rpx auto;
    overflow: hidden;
    // display: flex;
    .walletusage_top {
      width: 670rpx;
      height: 50rpx;
      color: #1d201e;
      font-size: 30rpx;
      margin-left: 30rpx;
      margin-top: 30rpx;
    }
    .walletusage_button {
      display: flex;
      .walletusage_left {
        width: 320rpx;
        height: 320rpx;
        .charts-box {
          width: 100%;
          height: 100%;
        }
      }
      .walletusage_right {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: center;
        width: 170rpx;
        margin-top: 35rpx;
        height: 260rpx;
        color: #666;
        view {
          width: 150rpx;
          display: flex;
          font-size: 24rpx;
          color: #1d1e20;
          justify-content: space-around;
          align-items: center;
        }
      }
    }
  }
  .walletanalyse {
    width: 670rpx;
    height: 427rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    margin: 20rpx auto;
    overflow: hidden;
    .walletanalyse_type {
      width: 600rpx;
      height: 80rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .walletanalyse_type_name {
        width: 260rpx;
        height: 80rpx;
        font-size: 30rpx;
        color: #1d201e;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .walletanalyse_type_value {
        width: 310rpx;
        height: 30rpx;
        display: flex;
        justify-content: space-evenly;
      }
    }
    .walletanalyse_man {
      width: 610rpx;
      height: 311rpx;
      background-color: #ffffff;
      border-radius: 0rpx 0rpx 8rpx 8rpx;
      border: solid 1rpx #eae9ed;
      margin: auto;
      .walletanalyse_man_t {
        width: 610rpx;
        height: 61rpx;
        background-color: #eff1f6;
        border-radius: 8rpx 8rpx 0rpx 0rpx;
        font-size: 30rpx;
        color: #1d1e20;
        padding: 10rpx;
      }
      .walletanalyse_man_ul {
        width: 610rpx;
        height: 251rpx;
        border-radius: 0rpx 0rpx 8rpx 8rpx;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;
        .walletanalyse_man_li {
          width: 200rpx;
          height: 125rpx;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          align-items: center;
          .walletanalyse_man_tex_top {
            font-size: 24rpx;
            color: #8f9295;
          }
          .walletanalyse_man_text_buttom {
            font-size: 30rpx;
            color: #1d1e20;
          }
        }
        .walletanalyse_man_li:nth-child(2) {
          .walletanalyse_man_text_buttom {
            font-size: 30rpx;
            color: #fa6f71;
          }
        }
        .walletanalyse_man_li:nth-child(5) {
          .walletanalyse_man_text_buttom {
            font-size: 30rpx;
            color: #fa6f71;
          }
        }
      }
    }
  }
  .revenue_turnover {
    width: 670rpx;
    height: 214rpx;
    margin: auto;
    display: flex;
    justify-content: space-between;
    border-radius: 20rpx;
    overflow: hidden;
    position: relative;
    .revenue_turnover_img {
      width: 670rpx;
      height: 214rpx;
      border-radius: 12rpx;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 10;
      &image {
        width: 100%;
        height: 100%;
      }
    }

    padding: 30rpx;
    .revenue_turnover_text,
    .revenue_turnover-r {
      position: relative;
      width: 300rpx;
      display: flex;
      flex-direction: column;
      align-content: space-evenly;
      z-index: 98;
      .li {
        width: 500rpx;
        margin-top: 10rpx;
        color: $color-text-b;
      }
      .li:nth-child(1) {
        font-size: 24rpx;
        display: flex;
      }
      .li:nth-child(2) {
        font-size: 56rpx;
      }
      .li:nth-child(3) {
        font-size: 20rpx;
      }
    }
  }
  .revenue_tabs{
    .tabs_item{
      width: 120rpx;
      height: 36rpx;
      font-size: 18rpx;
      text-align: center;
      line-height: 36rpx;
    }
    .tabs_active{
        background-color: $color-primary;
        color: $color-white ;
      }
      .tabs_no_active{
        background-color: $color-white;
        color: $color-black;
        border: solid 1px #e0e0e0
      }
  }
  .contrast_more {
    width: 221rpx;
    height: 23rpx;
    font-size: $font-size-xs;
    color: $color-text-a;
    margin: 30rpx auto;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
<style lang="scss">
// #ifdef MP-WEIXIN
 ::v-deep.u-navbar .u-navbar--fixed{
  z-index: 99 !important;
}
// #endif
</style>

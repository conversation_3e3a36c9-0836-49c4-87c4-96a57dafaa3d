<template>
  <view class="sectorconsumption">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('business.Sector.consumption')"
      :autoBack="true"
      :leftIconColor="color.navigation"
      placeholder
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    ></u-navbar>
    <!--#endif-->
    <!--筛选  -->
    <filterLayoutComponent
      :filterDataLayoutList="filterDataList"
      @handlerItemClick="handlerDropDownItemClick"
    ></filterLayoutComponent>
    <!--头部日期  -->
    <view class="sectorconsumption_time">{{ dateTitle }}</view>
    <!-- 统计概况 -->
    <statisticaloverview :consumedInfoList="consumedTotalInfoList"></statisticaloverview>
    <!-- 餐段统计 -->
    <segments
      ref="segments"
      :current="currentIndex"
      :title="segmentsTitle"
      :menuList="mealTypeInfoList"
      @segmentTabChange="segmentTabChange"
    ></segments>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
import segments from '@/pages_statistics/components/Segments/Segments.vue'
import statisticaloverview from '../components/StatisticalOverview/StatisticalOverview'
import filterLayoutComponent from '@/components/FilterLayoutComponent/FilterLayoutComponent.vue'
import comDic from '../../common/comDic'
import {
  apiBackgroundReportCenterDataReportDepartmentPaymentCollectList,
  apiBackgroundReportCenterDataReportGetMealType
} from '@/api/report'
import { getLastDayRange, deepClone, numberToThousands, divide } from '@/utils/util'
import { getUserOrgsList, getUserDepartmentList } from '@/utils/userUtil'
import cache from '../../utils/cache'
export default {
  data() {
    return {
      startDate: '', // 开始时间
      endDate: '', // 结束时间
      filterDataList: [
        // 头部筛选列表
        {
          title: '昨天',
          chooseItem: '昨天',
          dataList: comDic.DIC_DEPARTMENT_DATE_LIST
        },
        {
          title: '全部部门',
          chooseItem: '全部部门',
          dataList: []
        },
        {
          title: '全部组织',
          chooseItem: '全部组织',
          dataList: []
        }
      ],
      consumedTotalInfoList: [], // 统计总数
      pageNo: 1, // 页码
      pageSize: 99999, // 每页显示数据，后台说改成这个，这里不要明细，只要collect里面的总数
      parmas: {}, // 入参
      isShowEmptyView: false, // 是否显示空白内容
      emptyContent: this.$t('tip.list.empty'),
      currentIndex: 0, // 餐段统计选中table项值
      consumedDepartmentTotalList: [], // 餐段统计列表
      mealTypeInfoList: [], // 餐段表头列表
      dateTitle: '', // 时间段展示
      segmentsTitle: this.$t('business.Segment.statistics')
    }
  },
  components: {
    segments,
    statisticaloverview,
    filterLayoutComponent
  },
  computed: {
    ...mapGetters(['color'])
  },
  created() {
    // 初始化数据
    this.initData()
  },
  methods: {
    /**
     * 初始化数据
     */
    async initData() {
      // 获取该账号的所选的组织
      var organId = cache.get(this.$common.KEY_USER_ORGAN)
      console.log('organId', organId)
      await this.getMealType(organId)
      // 进入获取昨天的日期进行查询数据、
      var dateList = getLastDayRange(1, '{y}-{m}-{d}', false)
      console.log('dataList', dateList)
      this.startDate = dateList[0] || ''
      this.endDate = dateList[1] || ''
      if (this.startDate === this.endDate) {
        this.dateTitle = this.startDate
      } else {
        this.dateTitle = dateList.join(' 至 ')
      }
      this.getDepartmentConsumeList()
      // 获取字典
      this.getDicList()
    },
    /**
     * 下拉刷新返回
     */
    downCallback(page) {
      console.log(' downCallback page', page)
      this.pageNo = 1
      this.parmas.page = this.pageNo
      this.getDepartmentConsumeList()
    },
    /**
     *
     * @param {*} date 切换下拉列表时间
     */
    handlerItemClick(timename) {
      // console.log(timename)
      if (timename.type === 'day') {
        this.dateType = 'day'
      } else if (timename.type === 'week') {
        this.dateType = 'week'
      } else if (timename.type === 'month') {
        this.dateType = 'month'
      }
    },
    /**
     * 获取该用户餐段的配置数据
     * @param organId  组织ID
     */
    getMealType(organId) {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      apiBackgroundReportCenterDataReportGetMealType({ org_id: organId })
        .then(res => {
          uni.hideLoading()
          if (res.code === 0) {
            var data = res.data || {}
            if (Reflect.has(data, 'meal_type_info')) {
              this.mealTypeInfoList = deepClone(data.meal_type_info)
              this.mealTypeInfoList.map(item => {
                item.name = item.meal_type_alias
                item.value = item.meal_type
                return item
              })
              if (this.$refs.segments && Reflect.has(this.$refs.segments, 'setMenuList')) {
                this.$refs.segments.setMenuList(this.mealTypeInfoList)
              }
            }
          }
        })
        .catch(error => {
          uni.hideLoading()
          console.log('error', error)
        })
    },
    /**
     * 获取字典列表
     */
    async getDicList() {
      var filterList = deepClone(this.filterDataList)
      // 全部部门列表
      var [errorDepartment, resultDepartment] = await this.$to(getUserDepartmentList())

      // 组织类型列表
      var [errorOrgan, resultOrgan] = await this.$to(getUserOrgsList(2))

      // 赋值
      if (resultDepartment && !errorDepartment) {
        resultDepartment.unshift({ name: '全部部门', value: '' })
        filterList[1].dataList = resultDepartment
      }
      if (resultOrgan && !errorOrgan) {
        resultOrgan.unshift({ name: '全部组织', value: '' })
        filterList[2].dataList = deepClone(resultOrgan)
      }

      this.filterDataList = deepClone(filterList)
    },
    /**
     * 获取设备消费数据
     */
    async getDepartmentConsumeList() {
      this.$showLoading({
        title: this.$t('tip.loading'),
        mask: true
      })
      this.parmas.page = this.pageNo
      this.parmas.page_size = this.pageSize
      this.parmas.start_date = this.startDate
      this.parmas.end_date = this.endDate
      const [error, res] = await this.$to(
        apiBackgroundReportCenterDataReportDepartmentPaymentCollectList(this.parmas)
      )
      uni.hideLoading()
      if (error) {
        uni.$u.toast(error.message)
        return
      }
      if (res.code === 0) {
        var data = res.data ? res.data : {}
        // 统计概况
        if (Reflect.has(data, 'collect')) {
          console.log(data)
          this.setConsumeTotalList(data.collect)
          this.setContentList(data.collect)
          this.consumedDepartmentTotalList = deepClone(data.collect)
        }
      } else {
        uni.$u.toast(res.msg || '获取失败')
        this.mescroll.endErr()
      }
    },
    /**
     * 设置餐段统计内容数据，需要根据tab的值进行处理
     * @param {*} contentList
     */
    setContentList(contentList) {
      console.log("this.menuList", this.mealTypeInfoList, this.currentIndex, contentList);
      if (!this.mealTypeInfoList || this.mealTypeInfoList.length === 0) {
        this.updateViewSegmentList([])
        return
      }
      var key = this.mealTypeInfoList[this.currentIndex].value
      var newList = comDic.DIC_DEPARTMENT_CONSUMPTION_MEAL_LIST
      if (contentList && Object.keys(contentList).length > 0) {
        this.contentListClone = deepClone(contentList)
        for (let i = 0; i < newList.length; i++) {
          switch (i) {
            case 0:
              newList[0].value = Reflect.has(contentList, key + '_pay_fee')
                ? numberToThousands(divide(contentList[key + '_pay_fee']))
                : '0'
              break
            case 1:
              newList[1].value = Reflect.has(contentList, key + '_discount_fee')
                ? numberToThousands(divide(contentList[key + '_discount_fee']))
                : '0'
              break
            case 2:
              newList[2].value = Reflect.has(contentList, key + '_jc_count')
                ? numberToThousands(contentList[key + '_jc_count'])
                : '0'
              break
            case 3:
              newList[3].value = Reflect.has(contentList, key + '_consume_count')
                ? numberToThousands(contentList[key + '_consume_count'])
                : '0'
              break
            default:
              break
          }
        }
        this.updateViewSegmentList(newList)
        console.log("resultData", newList);
      }
    },
    /**
     * 更新餐段统计列表视图
     */
    updateViewSegmentList(newList) {
      if (this.$refs.segments && Reflect.has(this.$refs.segments, 'setContentList')) {
        this.$refs.segments.setContentList(newList)
      }
    },
    /**
     * 设置统计概况列表
     */
    setConsumeTotalList(infoList) {
      var totalList = comDic.DIC_DEPARTMENT_CONSUMPTION_TOTAL_LIST
      if (infoList && Object.keys(infoList).length > 0) {
        totalList.map((item, index) => {
          if (Reflect.has(infoList, item.value)) {
            var sign = index === 0 | index === 1 ? '¥' : ''
            var total = index === 0 | index === 1 ? divide(infoList[item.value]) : infoList[item.value]
            item.number = sign + numberToThousands(total)
          }
          return item
        })
        console.log('totalList', totalList)
        this.consumedTotalInfoList = deepClone(totalList)
      }
    },

    /**
     *头部筛选下拉选项点击
     * @param {*} itemData 点击项数据
     * @param index 点击的项目
     */
    handlerDropDownItemClick(itemData, index) {
      console.log('handlerDropDownItemClick', itemData)
      // 点击记录赋值
      this.filterDataList[index].chooseItem = itemData.name
      this.filterDataList[index].title = itemData.name
      switch (index) {
        case 0: // 时间点击
          var timeNum = itemData.number
          var dateList = getLastDayRange(timeNum, '{y}-{m}-{d}', false)
          this.startDate = dateList[0] || ''
          this.endDate = dateList[1] || ''
          if (this.startDate === this.endDate) {
            this.dateTitle = this.startDate
          } else {
            this.dateTitle = dateList.join(' 至 ')
          }
          break
        case 1: // 部门点击
          this.parmas.payer_department_group_ids = [itemData.id]
          if (itemData.name === '全部部门') {
            delete this.parmas.payer_department_group_ids
          }
          break
        case 2: // 类型点击
          this.parmas.org_ids = [itemData.id]
          if (itemData.name === '全部组织') {
            delete this.parmas.org_ids
          }
          break
        default:
          break
      }
      this.pageNo = 1
      this.getDepartmentConsumeList()
    },
    /**
     * 餐段统计tab切换监听
     */
    segmentTabChange(e) {
      console.log('segmentTabChange', e)
      this.currentIndex = e.index
      this.setContentList(this.consumedDepartmentTotalList)
    }
  }
}
</script>

<style lang="scss" scoped>
.sectorconsumption_time {
  font-size: 36rpx;
  color: #1d1e20;
  margin: 30rpx auto;
  text-align: center;
}
.choose {
  width: 750rpx;
  height: 80rpx;
  background-color: $background;
  display: flex;
  justify-content: center;
  align-items: center;
  .choose-organization,
  .choose-timesitem,
  .choose-groupitem {
    width: 375rpx;
    height: 80rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    select {
      border: none;
    }
  }
}
.time_each_segment {
  margin: 40rpx auto;
  text-align: center;
  font-size: 36rpx;
  color: #1d1e20;
}
</style>

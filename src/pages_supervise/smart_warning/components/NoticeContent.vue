<template>
  <view class="notice-content">
    <view class="notice-content-title">
      <view class="notice-content-title-left">
        {{ computedTitle }}
      </view>
      <view class="notice-content-title-right">
        {{ showMessage.create_time }}
      </view>
    </view>
    <view class="notice-content-content">
      <view v-if="currentTab === 0">
        <view class="notice-content-content-item">
          <text>内容：</text>
          <text>{{ showMessage.warn_detail }}：</text>
          <text style="color: #FF445B;">{{ showMessage.warn_val }}%</text>
        </view>
      </view>
      <view v-else-if="currentTab === 1">
        <view class="notice-content-content-item">
          <text>内容：</text>
          <text>{{ showMessage.warn_text }}</text>
        </view>
        <view class="notice-content-content-item">
          <text>预警物资：</text>
          <text style="color: #FF445B;">{{ showMessage.materials_name }}</text>
        </view>
        <view class="notice-content-content-item">
          <text>供应商：</text>
          <text>{{ showMessage.supplier_manage_name }}</text>
        </view>
        <view class="notice-content-content-item">
          <text>关联单据：</text>
          <text>{{ showMessage.trade_no }}</text>
        </view>
      </view>
      <view v-else>
        <view class="notice-content-content-item">
          <text>内容：</text>
          <text>{{ showMessage.warn_text }}</text>
        </view>
        <view class="notice-content-content-item">
          <text>有效期：</text>
          <text v-if="showMessage.file_type === 'healthy_license'">{{ showMessage.end_time }}</text>
          <text v-else>{{ showMessage.start_time }} ~ {{ showMessage.end_time }}</text>
        </view>
        <view class="notice-content-content-item" style="display: flex;">
          <text>附件：</text>
          <view style="color: #02A7F0;" @click="showImage(showMessage.image_json[0].img, 0)">查看</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    showMessage: {
      type: Object,
      default: () => {
        return {}
      }
    },
    currentTab: {
      type: Number,
      default: 0
    }
  },
  computed: {
    computedTitle() {
      let str = ''
      switch (this.currentTab) {
        case 0:
          str = this.showMessage.warn_type_alias || ''
          break
        case 1:
          str = this.showMessage.drp_type_alias || ''
          break
        case 2:
          str = this.showMessage.file_type_alias || ''
          break
      }
      return str
    }
  },
  data() {
    return {
      title: ''
    }
  },
  methods: {
    showImage(imgList, index) {
      let obj = {
        imgList: [imgList],
        index
      }
      this.$emit('showImage', obj)
    }
  }
}
</script>

<style lang="scss" scoped>
.notice-content {
  padding: 20rpx;
  &-title {
    padding: 10rpx 0;
    border-bottom: 1rpx solid #F2F2F2;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &-left {
      font-weight: 700;
    }&-right {
      color: #666666;
    }
  }
  &-content {
    padding: 20rpx 0;
    &-item {
      padding: 10rpx 0;
    }
  }
}
</style>

<template>
  <view class="approval_container">
    <!--#ifdef MP-WEIXIN || H5 -->
    <u-navbar
      :title="$t('title.smart.warning')"
      placeholder
      :autoBack="true"
      :leftIconColor="color.navigation"
      leftIconSize="37rpx"
      :titleStyle="{ color: color.navigation, fontSize: '37rpx' }"
    ></u-navbar>
    <!--#endif-->
    <!--头部tab-->
    <view class="approval_tabs">
      <u-tabs lineHeight="5" :current="currentIndex" :list="tabList" @click="tabClick" lineWidth="60rpx" lineColor="#fd953c" :activeStyle="activeStyle" :inactiveStyle="inactiveStyle" :itemStyle="itemStyle"></u-tabs>
    </view>
    <!--中间筛选-->
    <view class="approval_middle_choose">
      <FilterLayoutComponent id="filterLayout" ref="filterLayout" :topHeight="topHeight" :filterDataLayoutList="filterDataList"  @handlerItemClick="handlerDropDownItemClick" :key="refresh"></FilterLayoutComponent>
    </view>

    <!--底部列表-->
    <view class="approval_middle_content" v-if="!isShowEmptyView">
      <mescroll-uni
        ref="mescrollRef"
        :fixed="false"
        :safearea="false"
        :bottom="50"
        @init="mescrollInit"
        @down="downCallback"
        @up="upCallback"
        :down="{ auto: false }"
        :up="{ auto: false }" >
        <view class="approval_middle_content_item"  v-for="(item,index) in orderList" :key="index">
          <NoticeContent :showMessage="item" :currentTab="currentIndex" @showImage="handlerShowImg"></NoticeContent>
        </view>
      </mescroll-uni>
    </view>
    <!-- 空白页 -->
    <EmptyComponent :emptyContent="emptyContent" v-if="isShowEmptyView"></EmptyComponent>
    <!--#ifdef MP-WEIXIN || MP-ALIPAY -->
    <CustomDialogComponent ref="customDialog"></CustomDialogComponent>
    <!--#endif-->
    <!--图片预览-->
    <image-preview ref="imgPreview"></image-preview>
  </view>
</template>

<script>
import * as dayjs from 'dayjs'
import comDic from '../../common/comDic'
import { mapGetters } from 'vuex'
import { deepClone } from '../../utils/util'
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import NoticeContent from './components/NoticeContent.vue'
import {
  apiMerchantMobileWarnDispositionBusinessWarnList,
  apiMerchantMobileWarnDispositionContractWarnList,
  apiMerchantMobileWarnDispositionMaterialWarnList
} from '@/api/supervise'
import ImagePreview from '@/components/ImagePreview/ImagePreview.vue';

export default {
  components: { NoticeContent, ImagePreview },
  data() {
    return {
      topHeight: 0,
      currentIndex: 0,
      tabList: [
        {
          name: "经营预警"
        },
        {
          name: "物资风险"
        },
        // {
        //   name: "采购质量"
        // },
        {
          name: "证件/合同"
        }
      ],
      itemStyle: { "font-size": "30rpx", margin: "26rpx 0" },
      activeStyle: { color: "#fd953c", fontWeight: "bold", transform: "scale(1.05)" },
      inactiveStyle: { color: "#8f9295" },
      filterDataList: [
        {
          title: this.$t("dic.date.select.time"),
          chooseItem: this.$t("dic.date.all.time"),
          dataList: comDic.DIC_SMART_WARNING_TIME_LIST
        },
        {
          title: this.$t("dic.date.warning.type"),
          chooseItem: this.$t("dic.date.all.type"),
          dataList: []
        }
      ],
      params: {
        start_date: "",
        end_date: "",
        file_type: "",
        warn_type: "",
        drp_type: ""
      },
      orderList: [],
      pageNo: 1,
      pageSize: 10,
      isShowEmptyView: false, // 是否显示空白内容
      emptyContent: this.$t('tip.list.empty'),
      refresh: 0,
      mescroll: ''
    }
  },
  mixins: [MescrollMixin],
  computed: {
    ...mapGetters(["color"])
  },
  created() {
    this.initData()
  },
  /**
     * 页面加载
     */
  onload(e) {
    console.log("申请审批", e);
    // #ifdef MP-WEIXIN || MP-ALIPAY
    this.msgListener();
    // #endif
  },
  onUnload() {
    // #ifdef MP-WEIXIN || MP-ALIPAY
    uni.$off(this.$common.MSG_APPROVAL_SUCCESS);
    // #endif
  },
  mounted() {
    console.log("mounted");
    // #ifdef H5
    this.msgListener();
    // #endif
  },
  destroyed() {
    // #ifdef H5
    // 销毁通知首页刷新
    uni.$emit(this.$common.MSG_UPDATE_UNREAD_BACK, "申请返回")
    uni.$off(this.$common.MSG_APPROVAL_SUCCESS);
    // #endif
  },
  methods: {
    mescrollInit(mescroll) {
      this.mescroll = mescroll; // 将实例绑定到组件作用域
    },
    /**
         * 信息监听
         */
    msgListener() {
      var that = this;
      uni.$on(this.$common.MSG_APPROVAL_SUCCESS, data => {
        console.log("data", data);
        that.getOrderList(that.currentIndex);
      });
    },
    initData() {
      // 设置距离顶部高度
      this.topHeight = uni.upx2px(90)
      this.filterDataList[1].dataList = deepClone(comDic.DIC_SMART_WARNING_TYPE_LIST0)
      this.getOrderList(0)
    },
    tabClick(data) {
      if (this.$refs.filterLayout) {
        this.$refs.filterLayout.$refs.drowDown.showDropDown(false)
      }
      switch (data.index) {
        case 0:
          this.filterDataList[1].dataList = deepClone(comDic.DIC_SMART_WARNING_TYPE_LIST0)
          break
        case 1:
          this.filterDataList[1].dataList = deepClone(comDic.DIC_SMART_WARNING_TYPE_LIST1)
          break
        case 2:
          this.filterDataList[1].dataList = deepClone(comDic.DIC_SMART_WARNING_TYPE_LIST3)
          break
      }
      this.currentIndex = data.index
      this.pageNo = 0
      this.orderList = []
      this.pageNo = 1
      this.getOrderList(this.currentIndex)
      this.refresh++
    },
    handlerDropDownItemClick(itemData, index) {
      if (index === 0 && itemData.number) {
        this.params.end_date = dayjs().format("YYYY-MM-DD")
        this.params.start_date = dayjs().subtract(itemData.number, "month").format("YYYY-MM-DD")
      }
      if (index === 1) {
        switch (this.currentIndex) {
          case 0: {
            this.params.warn_type = itemData.key || ''
            this.params.file_type = ''
            this.params.drp_type = ''
            break
          }
          case 1: {
            this.params.warn_type = ''
            this.params.file_type = ''
            this.params.drp_type = itemData.key || ''
            break
          }
          case 2: {
            this.params.warn_type = ''
            this.params.file_type = itemData.key || ''
            this.params.drp_type = ''
            break
          }
        }
      }
      this.orderList = []
      this.pageNo = 1
      this.getOrderList(this.currentIndex)
    },
    /**
         * 下拉刷新返回
         */
    downCallback(page) {
      console.log(" downCallback page", page);
      this.pageNo = 1;
      this.getOrderList(this.currentIndex);
    },
    /**
         * 上拉加载更多
         * @param {*} page
         */
    upCallback(page) {
      console.log(" upCallback page", page);
      this.pageNo++;
      this.getOrderList(this.currentIndex);
    },
    getOrderList(index) {
      let obj = deepClone(this.params)
      for (let key in obj) {
        if (obj[key] === "") delete obj[key];
      }
      let params = {
        page: this.pageNo,
        page_size: this.pageSize,
        ...obj
      }
      console.log('params', params)
      switch (index) {
        case 0: {
          this.getBusinessWarnList(params)
          break
        }
        case 1: {
          this.getMaterialWarnList(params)
          break
        }
        case 2: {
          this.getContractWarnList(params)
          break
        }
      }
    },
    async getBusinessWarnList(params) {
      const [error, res] = await this.$to(apiMerchantMobileWarnDispositionBusinessWarnList(params))
      console.log('获取成功', error, res)
      if (error) {
        this.mescroll.endErr();
        return uni.$u.toast(error.msg)
      }
      if (res && res.code === 0) {
        var count = res.data.count ? res.data.count : 0;
        var resultList = deepClone(res.data.results || [])
        if (this.pageNo === 1 && resultList && resultList.length > 0) {
          // 首次加载数据
          console.log("首次加载数据");
          this.orderList = deepClone(resultList)
        } else if (this.pageNo !== 1 && resultList && resultList.length > 0) {
          // 加载更多数据
          console.log("加载更多数据");
          this.orderList = this.orderList.concat(resultList)
        }
        // 没有数据
        this.isShowEmptyView = this.pageNo === 1 && (!this.orderList || this.orderList.length === 0)
        console.log('setPageNum', params.page)
        this.mescroll.setPageNum(params.page)
        this.mescroll.endBySize(this.pageSize, count);
      } else {
        this.mescroll.endErr();
        return uni.$u.toast(res.msg)
      }
    },
    async getMaterialWarnList(params) {
      const [error, res] = await this.$to(apiMerchantMobileWarnDispositionMaterialWarnList(params))
      if (error) {
        this.mescroll.endErr();
        return uni.$u.toast(error.msg)
      }
      if (res && res.code === 0) {
        var count = res.data.count ? res.data.count : 0;
        var resultList = deepClone(res.data.results || [])
        if (this.pageNo === 1 && resultList && resultList.length > 0) {
          // 首次加载数据
          console.log("首次加载数据");
          this.orderList = deepClone(resultList)
        } else if (this.pageNo !== 1 && resultList && resultList.length > 0) {
          // 加载更多数据
          console.log("加载更多数据");
          this.orderList = this.orderList.concat(resultList)
        }
        // 没有数据
        this.isShowEmptyView = this.pageNo === 1 && (!this.orderList || this.orderList.length === 0)
        console.log('setPageNum', params.page)
        this.mescroll.setPageNum(params.page)
        this.mescroll.endBySize(this.pageSize, count)
      } else {
        this.mescroll.endErr();
        return uni.$u.toast(res.msg)
      }
    },
    async getContractWarnList(params) {
      const [error, res] = await this.$to(apiMerchantMobileWarnDispositionContractWarnList(params))
      if (error) {
        this.mescroll.endErr();
        return uni.$u.toast(error.msg)
      }
      if (res && res.code === 0) {
        var count = res.data.count ? res.data.count : 0;
        var resultList = deepClone(res.data.results || [])
        if (this.pageNo === 1 && resultList && resultList.length > 0) {
          // 首次加载数据
          console.log("首次加载数据");
          this.orderList = deepClone(resultList)
        } else if (this.pageNo !== 1 && resultList && resultList.length > 0) {
          // 加载更多数据
          console.log("加载更多数据");
          this.orderList = this.orderList.concat(resultList)
        }
        // 没有数据
        this.isShowEmptyView = this.pageNo === 1 && (!this.orderList || this.orderList.length === 0)
        console.log('setPageNum', params.page)
        this.mescroll.setPageNum(params.page)
        this.mescroll.endBySize(this.pageSize, count)
      } else {
        this.mescroll.endErr();
        return uni.$u.toast(res.msg)
      }
    },

    // 展示图片
    handlerShowImg(obj) {
      let { imgList, index } = obj
      if (!imgList || imgList.length === 0) {
        return this.$u.toast('暂无预览图片')
      }
      if (this.$refs.imgPreview) {
        this.$refs.imgPreview.setImgList(imgList, index)
        this.$refs.imgPreview.showDialog()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.approval_container{
  font-size: 28rpx;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  .approval_tabs{
    height: 90rpx;
    padding: 0 40rpx;
    background-color: #ffffff;
  }
  // #ifdef MP-WEIXIN
  .approval_middle_content{
    padding-top:20rpx;
    height: calc(100% - 350rpx);
    padding-bottom: 50rpx;
  }
  // #endif
   // #ifdef H5
   .approval_middle_content{
    padding-top:20rpx;
    height: calc(100% - 258rpx);
  }
  // #endif
  .approval_middle_content_item{
    display: inline-block;
    width: 670rpx;
    min-height: 100rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    margin :20rpx 40rpx;
  }
}
</style>

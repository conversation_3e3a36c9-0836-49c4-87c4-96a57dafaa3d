// router.js
import { RouterMount, createRouter } from 'uni-simple-router';
// import Vue from 'vue'

const router = createRouter({
  platform: process.env.VUE_APP_PLATFORM,
  // eslint-disable-next-line no-undef
  routes: [...ROUTES]
});
// 全局路由前置守卫
router.beforeEach((to, from, next) => {
  // console.log(to(),from());
  // 当路由切换页面的时候，遍历全局数组，将上一个页面的所有请求cancel掉
  // if (Vue.prototype.cancelAxios) {
  //   Vue.prototype.cancelAxios.forEach((cancel) => {
  //     cancel()
  //   })
  //   Vue.prototype.cancelAxios = []
  // }
  next();
});
// 全局路由后置守卫
router.afterEach((to, from) => {
  // console.log('跳转结束')
})

export {
  router,
  RouterMount
}

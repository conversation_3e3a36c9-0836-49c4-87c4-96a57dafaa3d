
const state = {

  color: {
    themeColor: '#FD953C', // 主题色
    twoColor: "#ffc23f",
    color1: '#fff', // 文字白色
    color3: '#333', // 文字深灰色
    color9: '#999', // 文字浅灰色
    navigation: '#282b27', // 导航栏字体颜色
    colorBtnYellow: "#fdb73c", // 按钮黄色
    colorBtnRedWarning: "#ff6349", // 按钮警告红色
    colorBtnRed: "#fd7c3c", // 按钮红色
    colorBtnOrange: "#fd953c", // 按钮橙色
    colorBtnBlack: "#3a3531", // 按钮黑色
    colorBtnGray: '#999999' // 按钮灰色
  }

}

const mutations = {

  SET_COLOR: (state, colorValue) => {
    state.color = colorValue
  }
}

const actions = {

  setColor({ commit }, color) {
    commit('SET_COLOR', color)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

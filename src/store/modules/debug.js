const state = {
  env: process.env.EVN_CONFIG,
  showVconsole: false // 是否打开vconsole
}

const mutations = {
  UPDATE_SHOWVCONSOLE: (state, data) => {
    state.showVconsole = data
  }
}

const actions = {
  setVconsoleOpts({ commit, dispatch }, data) {
    return new Promise(resolve => {
      commit('UPDATE_SHOWVCONSOLE', data)
      // #ifdef H5
      let show = data ? 1 : 0 // 用1/0存吧，不要用true/false会变字符串
      sessionStorage.setItem('showVconsole', show)
      // #endif
      resolve()
    })
  }
}

export default {
  state,
  mutations,
  actions
}

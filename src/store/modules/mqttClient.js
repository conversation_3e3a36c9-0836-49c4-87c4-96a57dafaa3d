const Paho = require('@/utils/pahoMqtt.js')
const state = {
  // 连接服务器的url
  url: '',
  // 客户端实例
  client: null,
  // 订阅主题的集合，key为topic, value为接收到该topic时需要执行的回调
  subscribeMembers: {},
  // 是否显示消息
  showMessage: false
}
var defaultClientOptions = {
  qos: 2 // 默认订阅的主题的qos等级
}
const mutations = {
  SET_SERVER_URL(state, url) {
    state.url = url
  },
  SET_CLIENT(state, client) {
    state.client = client
  },
  SET_MESSAGE(state, msg) {
    state.showMessage = msg
  },
  DESTROY(state) {
    state.client = undefined
    state.subscribeMembers = {}
  },
  SUBSCRIBE(state, { topic, callback }) {
    state.subscribeMembers[topic] = callback
  },
  UNSUBSCRIBE(state, topic) {
    state.subscribeMembers[topic] = undefined
  }
}

const actions = {
  // 创建mqtt连接
  connect({ commit }, { url, options, isShowMsg }) {
    debugger
    return new Promise(resolve => {
      console.log('connect', url, options, isShowMsg)
      // 如果有配置就参数合并吧
      if (options && typeof options === 'object') {
        defaultClientOptions.clientId = options.clientId
      }
      const client = new Paho.Client(
        url,
        options.clientId // 客户端 ClientId
      )
      console.log(client, '连不连')
      var connectOptions = {
        timeout: 10,
        cleanSession: false, // 实现QoS>0必须设置为false，代表不清除，是持久会话
        keepAliveInterval: 5,
        reconnect: false, // 意外断开之后会重连，第一次1秒后重连，往后每次翻倍直到2分钟，后续保持2分钟
        mqttVersion: 4,
        userName: options.username,
        password: options.password,
        onSuccess: function () {
          client.onMessageArrived = onMessage
          commit('SET_CLIENT', client)
          commit('SET_SERVER_URL', url)
          console.log('连接成功')
          resolve('success');
        },
        onFailure: function (option) {
          console.log('fail', option)
        }
      }
      client.connect(connectOptions)
    })
  },

  disconnect({ commit }) {
    if (!state.client) {
      return
    }
    state.client.disconnect()
    commit('DESTROY')
    console.log(`服务器已断开连接！`)
  },

  /**
   * 订阅
   * @param commit
   * @param dispatch
   * @param topic 消息主题
   * @param subscribeOption 订阅设置 提前做了 单纯获取消息
   * @param callback 接收消息的回调
   */
  subscribe({ commit, dispatch }, { topic, callback, subscribeOptions }) {
    if (!state.client) {
      dispatch('connect')
    }
    state.client.subscribe(topic, {
      qos: defaultClientOptions.qos,
      timeout: 5,
      onSuccess: function () {
        console.log(`订阅主题: ${topic}成功`)
      },
      onFailure: function () {
        console.log(`订阅主题: ${topic}失败`)
      }
    })
    commit('SUBSCRIBE', { topic, callback })
  },

  /**
   * 取消订阅
   * @param commit
   * @param topic 消息主题
   */
  unSubscribe({ commit }, topic) {
    if (!state.client) {
      return
    }
    state.client.unsubscribe(topic, {}, (error, granted) => {
      if (error) {
        console.log(
          `客户端: ${defaultClientOptions.clientId}, 取消订阅主题: ${topic}
  失败: `,
          error
        )
      } else {
        console.log(`客户端: ${defaultClientOptions.clientId}, 取消订阅主题: ${topic}
  成功`)
      }
    })
    commit('UNSUBSCRIBE', topic)
  },
  /**
   * 发布消息
   * @param commit
   * @param dispatch
   * @param topic 消息主题
   * @param message 消息内容
   */
  publish({ commit, dispatch }, { topic, message }) {
    if (!state.client || !state.client.connected) {
      dispatch('connect')
    }
    state.client.publish(topic, message, defaultClientOptions.qos, e => {
      if (e) {
        console.log(
          `客户端: ${defaultClientOptions.clientId}, 发送主题为: ${topic} 的消
  息, 发送失败: `,
          e
        )
      }
    })
  }
}

// 接收消息
const onMessage = message => {
  console.log(
    `客户端: ${defaultClientOptions.clientId}, 接收到来自主题: ${message.topic} 的消
  息: `,
    message.payloadString
  )
  let callback = state.subscribeMembers[message.topic]
  callback?.(message.topic, message.payloadString)
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

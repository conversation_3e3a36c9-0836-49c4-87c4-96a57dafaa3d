import { login, logout, getInfo } from '@/api/user'
import cache from '@/utils/cache'
import common from '@/common/common'
import { $t } from '@/utils/i18n'

const state = {
  token: '',
  userName: '',
  userInfo: '',
  orgs: '',
  roles: [],
  cookie: cache.get(common.KEY_COOKIES) || '',
  mqttInfo: cache.get('mqttInfo') || {} // mqtt配置
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_USERNAME: (state, name) => {
    state.userName = name
  },
  SET_USERINFO: (state, avatar) => {
    state.userInfo = avatar
  },
  SET_ORGS: (state, orgs) => {
    state.orgs = orgs
  },
  // 设置cookie
  SET_COOKIE(state, data) {
    state.cookie = data
    console.log("SET_COOKIE", data);
    cache.set(common.KEY_COOKIES, data)
  },
  SET_MQTT_INFO(state, data) {
    state.mqttInfo = data
    cache.set('mqttInfo', data, 86400)
  }
}

const actions = {
  /**
   * 登录
   * @param {*} param0
   * @param {*} userInfo 账号密码作为参数
   * @returns
   */
  login({ commit }, userInfo) {
    removeCache()
    const { username, password, verifyCode, mode } = userInfo
    return new Promise((resolve, reject) => {
      login({ username: username.trim(), password: password, is_merchant_mobile: true, verify_code: verifyCode, mode: mode }).then(response => {
        console.log("data login111111", response);
        var data = response.data || {}
        if (data) {
          // 保存秘钥
          var token = data.token || ""
          commit('SET_TOKEN', token)
          cache.set(common.API_TOKEN, token)
          // 保存个人信息
          var userName = data.username || ''
          commit('SET_USERNAME', userName)
          commit('SET_USERINFO', data)
          cache.set(common.KEY_USER_INFO, data)
        }
        console.log("data login", response);
        resolve(response)
      }).catch(error => {
        console.log("error login", error);
        reject(error)
      })
    })
  },
  /**
   * 登录验证码
   * @param {*} param0
   * @param {*} userInfo 账号密码作为参数
   * @returns
   */
  Verification ({ commit }, userInfo) {
    // eslint-disable-next-line camelcase
    removeCache()
    const { phone, smsCode, mode, companyId, code } = userInfo
    return new Promise((resolve, reject) => {
      const params = { phone: phone.trim(), sms_code: smsCode, mode: mode, is_merchant_mobile: true, code: code }
      if (companyId) {
        params.company_id = companyId
      }
      login(params).then(response => {
        var data = response.data || {}
        console.log('登陆后data', data);
        if (data) {
          // 保存秘钥
          var token = data.token || ""
          commit('SET_TOKEN', token)
          cache.set(common.API_TOKEN, token)
          // 保存个人信息
          var userName = data.username || ''
          commit('SET_USERNAME', userName)
          commit('SET_USERINFO', data)
          cache.set(common.KEY_USER_INFO, data)
        }
        console.log("data login", response);
        resolve(response)
      }).catch(error => {
        console.log("error login", error);
        if (error.message && error.message === "参数格式错误") {
          error.message = $t('page.login.ver.code.error')
        }
        reject(error)
      })
    })
  },
  /**
   * 获取用户信息
   * @param {*} param0
   * @returns
   */
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo(state.token).then(response => {
        const { data } = response

        if (!data) {
          reject(new Error('Verification failed, please Login again.'))
        }

        const { roles, name } = data

        // roles must be a non-empty array
        if (!roles || roles.length <= 0) {
          reject(new Error('getInfo: roles must be a non-null array!'))
        }

        commit('SET_NAME', name)
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },
  /**
   * 设置mqtt配置
   * @param {*} commit
   * @param {*} dispatch
   * @param {*} data
   * @returns
   */
  setMqttInfo({ commit, dispatch }, data) {
    return new Promise(resolve => {
      commit('SET_MQTT_INFO', data)
      resolve()
    })
  },

  /**
   * 登出
   * @param {*} param0
   * @returns
   */
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      logout(state.token).then(() => {
        commit('SET_TOKEN', '')
        // commit('SET_ROLES', [])
        commit('SET_ORGS', '')
        commit('SET_COOKIE', '')
        commit('SET_MQTT_INFO', {})
        cache.remove('api_token')
        cache.remove('key_user_info')
        cache.remove('user_organ')
        cache.remove(common.KEY_COOKIES)
        cache.remove('mqttInfo')
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  /**
   * 重置token
   * @param {*} param0
   * @returns
   */
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      // commit('SET_ROLES', [])
      resolve()
    })
  },
  // 设置cookie
  setCookies({ commit }, data) {
    commit('SET_COOKIE', data)
  }

}
/**
 * 清除缓存
 */
function removeCache() {
  try {
    cache.remove(common.KEY_USER_INFO)
    cache.remove(common.KEY_USER_ORGAN)
    cache.remove(common.API_TOKEN)
  } catch (error) {
    console.log(error);
  }
}
export default {
  namespaced: true,
  state,
  mutations,
  actions
}

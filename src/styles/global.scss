/**
公共样式
 */

 /** 公共颜色 */

$success:#11E69E;
$warn:#FFB142;
$cancel:#9F9F9F;
$error:#FF5757;

/** S 背景颜色 **/

.bg-primary {
    background-color: $color-primary;
}

.bg-success {
    background-color: $color-success;
}

.bg-warning {
    background-color: $color-warning;
}


.bg-white {
    background-color: $color-white;
}

.bg-info {
    background-color: $color-info;
}


/** E 背景颜色 **/


/** E 字体颜色 **/


.white {
    color: $color-white;
}

.black {
    color: $color-text-black;
}

.normal {
    color: $color-text-primary;
}

.lighter {
    color: $color-text-regular;
}

.muted {
    color: $color-text-secondary;
}

.primary {
    color: $color-primary;
}

.success {
    color: $color-success;
}

.warning {
	color: $color-warning;
}

.yellow {
	color: $color-yellow;
}
.red {
	color: $color-red;
}
.gray-light {
	color: $color-light;
}
/** E 字体颜色 **/


/** S Font **/

.xxl {
	font-size: $font-size-xxl;
}

// XL
.xl {
    font-size: $font-size-xl;
}

// LG
.lg {
    font-size: $font-size-lg;
}

// MD
.md {
    font-size: $font-size-md;
}

// NR
.nr {
    font-size: $font-size-nr;
}

// SM
.sm {
    font-size: $font-size-sm;
}

// XS
.xs {
    font-size: $font-size-xs;
}

// XXS
.xxs {
    font-size: $font-size-xxs;
}

// mini
.mini {
    font-size: $font-size-mini;
}




// 定义字体大小，Example: f-s-[38-60]
@for $i from 38 through 60 {
	@if $i % 2 == 0 {
		.font-size-#{$i} {
			font-size: $i + rpx;
		}
	}
}

// 字体字重 Example: f-w-[100-900]
@for $i from 100 through 900 {
    @if $i % 100==0 {
        .f-w-#{$i} {
            font-weight: $i;
        }
    }
}


/** S Font **/

// 定义内外边距，历遍2-60
@for $i from 2 through 60 {
	// 只要双数和能被5除尽的数
	@if $i % 2 == 0 or $i % 5 == 0 {
		// 如：m-30
		.m-#{$i} {
			margin: $i + rpx;
		}
		
		// 如：p-30
		.p-#{$i} {
			padding: $i + rpx;
		}
		
		@each $short, $long in l left, t top, r right, b bottom {
			//结果如： m-l-30
			// 定义外边距
			.m-#{$short}-#{$i} {
				margin-#{$long}: $i + rpx;
			}
			
			// 定义内边距
			//结果如： p-l-30
			.p-#{$short}-#{$i} {
				padding-#{$long}: $i + rpx;
			}
		}
	}
}


/** S 文本行数限制 **/

.line-1 {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.line-2 {
    -webkit-line-clamp: 2;
}

.line-3 {
    -webkit-line-clamp: 3;
}

.line-4 {
    -webkit-line-clamp: 4;
}

.line-2,
.line-3,
.line-4 {
    overflow: hidden;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
}
.line-break {
  word-break: break-all;
}


/** E 文本行数限制 **/


/** S 内容排序方式 **/
.text-left { text-align: left; }

.text-center { text-align: center; }

.text-right { text-align: right; }
/** E 内容排序方式 **/


/** S Flex-弹性布局 **/

.flex {
    display: flex;
    flex-direction: row;
}

.flex-inline {
    display: inline-flex;
}


.flex-col {
    flex-direction: column;
}

.flex-center {
    align-items: center;
    justify-content: center;
}
.flex-none { flex: none; }

.flex-wrap { flex-wrap: wrap; }

.flex-nowrap { flex-wrap: nowrap; }

.col-baseline {	align-items: baseline; }

.col-center { align-items: center; }

.col-top { align-items: flex-start; }

.col-bottom { align-items: flex-end; }

.col-stretch { align-items:stretch; }

.row-center { justify-content: center; }

.row-left { justify-content: flex-start; }

.row-right { justify-content: flex-end; }

.row-between { justify-content: space-between; }

.row-around { justify-content: space-around; }


// Example: flex-[0-24]
@for $i from 0 through 24 {
    .flex-#{$i} {
        flex: $i;
    }
}


/** E Flex-弹性布局 **/
.ps-inline {
    display: inline;
}
// 行内块元素
.inline {
    display: inline-block;
}

// 块元素
.block {
    display: block;
}


.clearfix:after{
	content: ""; 
	display: block; 
	clear: both; 
	visibility: hidden;  
}  

/* 中划线 */
.line-through {
    text-decoration: line-through;
}


//******图标******/
@mixin icon-image($size) {
    min-height: $size;
    min-width: $size;
    height: $size;
    width: $size;
    vertical-align: middle;
}


.icon-xs {
    @include icon-image(20rpx);
}

.icon-sm {
    @include icon-image(28rpx);
}

.icon {
	@include icon-image(32rpx);
}
.icon-40 {
    @include icon-image(40rpx);
}
.icon-md {
	@include icon-image(44rpx);
}
.icon-lg {
    @include icon-image(54rpx);
}

.icon-xl {
    @include icon-image(58rpx);
}

.icon-xxl {
    @include icon-image(64rpx);
}

.mg-center{
    margin: 0 auto;
}

// 单行超出隐藏
.ellipsis {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  //******二级日期标题样式******/
 .title_date_level_two{
    font-size: 36rpx;
    color:#1d1e20;
    margin: 30rpx auto;
    text-align:center;
  }

.border-lt-lb-6{
    border-radius: 6rpx 0 0 6rpx;
}

.border-rt-rb-6{
    border-radius: 0 6rpx 6rpx 0;
}
.arrow_style{
    width: 12rpx;
    height: 16rpx;
    margin:0 10rpx;
}

.position-fixed{
    position: fixed;
}

.m-b-30 {
    margin-bottom: 30rpx;
}
@for $i from 0 through 750 {
    .w-#{$i} {
      width: $i + rpx;
    }
  }
  
  @for $i from 0 through 1624 {
    .h-#{$i} {
      height: $i + rpx;
    }
  }

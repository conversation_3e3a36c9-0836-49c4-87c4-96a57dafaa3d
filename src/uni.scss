
/* 颜色变量 */

// 系统原有的
$color-primary: #FD953C;
$color-assistant: #60fcf2;
$img-filter: initial;
//主题色
$but:#fd953c;
$butwo:#ffc23f;
$buttrer:#FEBF8A;

// 中行
// $color-primary: #EE2319;
// $color-assistant: #ff5d35;
// $img-filter: hue-rotate(205deg) saturate(500%);

$color-success: #67c23a;
$color-warning: #F1C669;
$color-red: #FE5858;
$color-yellow: #F8A73C;
$color-error: #FF5757;
$color-info: #999999;
$color-white: #ffffff;
$color-light:  #8f9295;
$color-disabled: #c8c9cc;
$color-black:#1d201e;

$color-primary-light-1: mix($color-white, $color-primary, 10%);
$color-primary-light-2: mix($color-white, $color-primary, 20%);
$color-primary-light-3: mix($color-white, $color-primary, 30%);
$color-primary-light-4: mix($color-white, $color-primary, 40%);
$color-primary-light-5: mix($color-white, $color-primary, 50%);
$color-primary-light-6: mix($color-white, $color-primary, 60%);
$color-primary-light-7: mix($color-white, $color-primary, 70%);
$color-primary-light-8: mix($color-white, $color-primary, 80%);
$color-primary-light-84: mix($color-white, $color-primary, 84%);
$color-primary-light-9: mix($color-white, $color-primary, 90%);
$color-primary-light-91: mix($color-white, $color-primary, 91%);

$color-primary-assistant-6: mix($color-assistant, $color-primary, 60%);

// 渐变
// linear-gradient(90deg, #11E69E, #11E6C5);
$primary-btn: linear-gradient(90deg, $color-primary, $color-primary-assistant-6);
// #e6fff3 linear-gradient(90deg, #a9fed5 0%, #ebfef5 0%, #ebfef5 0%, #d3fcf5 100%, #d3fcf5 100%);
$index-menu: linear-gradient(90deg, $color-primary-light-91 0%, $color-primary-light-91 0%, $color-primary-light-91 0%, $color-primary-light-8 100%, $color-primary-light-84 100%);
// linear-gradient(180deg, rgba(240,243,245,.0) 85%, rgba(240,243,245,1) 100%), linear-gradient(90deg, #a9fed5 0%, #c6f8f9 100%);
$user-bg: linear-gradient(180deg, rgba(240,243,245,.0) 85%, rgba(240,243,245,1) 100%),linear-gradient(90deg, $color-primary-light-7 0%, $color-primary-light-6 100%);

// 字体相关变量
$color-text: #1d201e;
$color-text-a:#8f9295;
$color-text-b:#fff;
$color-text-f:#2e2d2d;
$color-text-black: #101010;
$color-text-primary: #333333;
$color-text-regular: #666666;
$color-text-secondary: #999999;
$font-size-xxxl:48rpx;
$font-size-xxl: 36rpx;
$font-size-xl: 34rpx;
$font-size-lg: 32rpx;
$font-size-md: 30rpx;
$font-size-nr: 28rpx;
$font-size-sm: 26rpx;
$font-size-xs: 24rpx;
$font-size-xxs: 22rpx;
$font-size-mini: 20rpx;
$font-size-base: $font-size-nr;

// 背景
$background-color: #f0f3f5;
$background:#fff;

// 边框相关
$border-color-base: #EFEFEF;
$border-color-light: #f2f2f2;
$border-width-base: 1px;
$border-style-base: solid;
$border-base: $border-width-base $border-style-base $border-color-base;

// uview的主题色配置
$u-main-color: $color-text-primary;
$u-content-color: $color-text-regular;
$u-tips-color:$color-text-secondary;
$u-light-color: $color-light;
$u-border-color: #e5e5e5;
$u-bg-color: $background-color;
$u-disabled-color: $color-disabled;

$u-primary: $color-primary;
$u-primary-dark: mix($color-white, $color-primary, 20%);
$u-primary-disabled: mix($color-white, $color-primary, 50%);
$u-primary-light: mix($color-white, $color-primary, 90%);

$u-warning: $color-warning;
$u-warning-dark: mix($color-white, $color-warning, 20%);
$u-warning-disabled: mix($color-white, $color-warning, 50%);
$u-warning-light:mix($color-white, $color-warning, 90%);

$u-success: $color-success;
$u-success-dark: mix($color-white, $color-success, 20%);
$u-success-disabled: mix($color-white, $color-success, 50%);
$u-success-light: mix($color-white, $color-success, 90%);

$u-error: $color-error;
$u-error-dark: mix($color-white, $color-error, 20%);
$u-error-disabled: mix($color-white, $color-error, 50%);
$u-error-light: mix($color-white, $color-error, 90%);

$u-info: $color-info;
$u-info-dark: mix($color-white, $color-info, 20%);
$u-info-disabled: mix($color-white, $color-info, 50%);
$u-info-light: mix($color-white, $color-info, 90%);
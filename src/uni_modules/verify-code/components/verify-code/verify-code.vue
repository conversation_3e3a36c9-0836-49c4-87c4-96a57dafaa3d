<template>
	<view>
		<uni-popup ref="alertPopup" :is-mask-click="false">
			<view class="verifyCode">
				<view class="verifyCode-title">
					<text class="verifyCode-title-text">请完成安全验证</text>
					<uni-icons type="closeempty" color="#9CA4AC" size="18" @click="close"></uni-icons>
				</view>
				<view class="verifyCode-view" v-if="!loading">
					<view class="verifyCode-view-panel" @click="move" :style="{ backgroundImage: randomBackGround }">
						<view class="selected" v-for="(item, index) in mosPosArray" :key="index"
							@click.stop="mosPosTap(item, index + 1)" :style="{ left: `${item.x}px`, top: `${item.y}px` }">
							<text class="selected-text">{{ index + 1 }}</text>
							<image class="selected-image" :src="imgPath + '/<EMAIL>'" mode="aspectFit"></image>
						</view>
						<text class="originalText" v-for="(item, posIndex) in posArray" :key="posIndex" @click="selectedText(item)"
							:style="{ transform: `rotate(${item.rotate}deg)`, left: `${item.text_x}px`, top: `${item.text_y}px`, fontSize: `${item.fontSize}rpx`, fontWeight: `${item.fontWeight}`, color: `${item.color}` }">{{
								item.word }}</text>
						<view v-if="!pass" class="refresh" @click.stop="refresh">
							<uni-icons type="refreshempty" color="#FFFFFF" size="44rpx"></uni-icons>
						</view>
					</view>
					<!-- 初始化提示 -->
					<view v-if="!verifying" class="verifyCode-view-tips">
						<text class="verifyCode-view-tips-text">
							请依次点击
							<text class="text-weight" v-for="(item, presetIndex) in presetsTextArray" :key="presetIndex"> "{{ item }}"
							</text>
						</text>
					</view>
					<!-- 初始化提示 -->
					<!-- 验证中 -->
					<view v-else class="verifyCode-view-tips"
						:style="{ backgroundColor: pass ? '#d2f4ef' : '#fce1e1', borderColor: pass ? '#52ccba' : '#f57a7a' }">
						<uni-icons :type="pass ? 'checkmarkempty' : 'closeempty'" size="34rpx"
							:color="pass ? '#20C8AE' : '#F06767'"></uni-icons>
						<text class="verifyCode-view-tips-text" :style="{ color: pass ? '#52ccba' : '#f57a7a', paddingLeft: '6rpx' }">
							{{ pass ? '验证成功' : '验证失败，请重试' }}
						</text>
					</view>
					<!-- 验证中 -->
				</view>
				<view class="loading" v-else>
					<view class="loading-panel">
						<image class="loading-panel-image" :src="imgPath + '/loading.gif'" mode="aspectFit"></image>
					</view>
					<view class="loading-tips">
						<text>加载中...</text>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>
<script>
import { deepClone } from '@/utils/util.js'
import basePath from '../../../../config/index'

export default {
	props: {
		isNumber: {
			type: Boolean,
			default: false
		},
		chooseNum: {
			type: Number,
			default: 2
		}
	},
	data() {
		return {
			codeCharacter: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 'A', 'B', 'C', 'D', 'E', 'F'], //16进制数
			posArray: [],			//随机文字集合
			mosPosArray: [],		//点击的坐标集合
			randomBackGround: 0,	//随机图片背景
			startLeft: 0,			//可点击的范围距离左侧边界的距离
			startTop: 0,			//可点击的范围距离上侧边界的距离
			clickInt: 0,			//点击次数
			selectedArray: [],		//用户选中文字
			presetsTextArray: [],   //预设文字集合
			loading: false,			//是否正在加载中
			verifying: false,		//是否在验证中
			pass: false,			//验证是否通过
			resultTxtList: [], // 结果集，可以提前从后台获取结果集
			resultTxtCloneList: [], // 保存原始结果集
			codeList: [],
			imgPath: basePath.imgUrl
		}
	},
	methods: {
		//关闭验证码
		close() {
			this.$refs.alertPopup.close()
		},
		//打开验证码
		open() {
			this.$refs.alertPopup.open();
			this.$nextTick(() => {
				setTimeout(() => {
					this.init();
				}, 300)
			})
		},
		//刷新验证码
		refresh() {
			// this.resultTxtList = deepClone(this.resultTxtCloneList)
			// this.init();
			this.$emit("refresh");
		},
		//取消选中位置
		mosPosTap(item, index) {
			if (index == this.clickInt && this.clickInt < this.chooseNum) {
				this.mosPosArray = index == 1 ? [] : this.mosPosArray.splice(item, 1);
				this.clickInt = index == 1 ? 0 : this.clickInt - 1;
			}
		},
		//选中位置
		move(e) {
			if (this.clickInt < this.chooseNum) {
				this.mosPosArray.push({
					x: e.detail.x - this.startLeft,
					y: e.detail.y - this.startTop
				})
				this.clickInt++;
			}
			this.compareText();
		},
		//比对用户选择的文字
		compareText() {
			if (this.clickInt === this.chooseNum) {
				//验证中
				this.verifying = true;
				if (this.presetsTextArray.toString() === this.selectedArray.toString()) {
					//验证通过
					this.pass = true;
					this.$emit("success", this.selectedArray);
					setTimeout(() => {
						this.close();
					}, 600)
				} else {
					//验证失败
					this.pass = false;
					setTimeout(() => {
						this.refresh();
					}, 600)
				}
			}
		},
		//选中文字，存储文字
		selectedText(text) {
			if (this.clickInt < this.chooseNum) {
				this.selectedArray.push(text.word);
			}
		},
		//初始化验证码
		init() {
			//开启加载中
			this.loading = true;
			//重置验证状态
			this.verifying = false;
			this.pass = false;
			//重置点击的坐标
			this.mosPosArray = [];
			//重置选中文字
			this.selectedArray = [];
			//重置点击次数
			this.clickInt = 0;
			//生成随机背景图片
			this.getRandomBackGround();
			//生成四个随机文字， 字体颜色，字体大小，字体粗细，字体坐标等
			this.getPosition().then(res => {
				this.startLeft = res.left;
				this.startTop = res.top;
				this.posArray = []
				this.codeList = []
				// 获取随机结果集
				for (let i = 0; i < 4; i++) {
					this.codeList.push(this.getRandomText())
				}
				// 打乱一下
				this.codeList = this.shuffleArray(this.codeList)
				// 生成随机坐标跟颜色角度
				for (let i = 0; i < 4; i++) {
					this.posArray.push({
						index: i,
						word: this.codeList[i],
						color: this.getRandomColor(),
						text_x: this.getRandomPosition(i, res.width, res.height, "x"),
						text_y: this.getRandomPosition(i, res.width, res.height, "y"),
						rotate: this.getRandomRotate(),
						fontSize: this.getRandomSize(),
						fontWeight: this.getRandomWeight(),
					})
				}
			});

			//生成预设验证码
			setTimeout(() => {
				this.getRandomPresetsCode();
				//关闭加载中
				this.loading = false;
			}, 600)
		},
		// 打乱数据
		shuffleArray(array) {
			for (let i = array.length - 1; i > 0; i--) {
				// 生成一个从0到i的随机索引
				const j = Math.floor(Math.random() * (i + 1));
				// 交换当前元素与随机索引处的元素
				[array[i], array[j]] = [array[j], array[i]];
			}
			return array;
		},
		//获取容器的布局信息
		async getPosition() {
			return new Promise((resolve, reject) => {
				const query = uni.createSelectorQuery().in(this);
				query.select('.verifyCode-view-panel').boundingClientRect(data => {
					if (data) {
						resolve({ left: data.left, top: data.top, width: data.width, height: data.height });
					}
				}).exec();
			});
		},
		//生成随机背景图片
		getRandomBackGround() {
			this.randomBackGround = 'url('+ this.imgPath + '/' + (Math.floor(Math.random() * (9 - 1)) + 1) + '.png)'
		},
		//在随机的文字中挑选X个作为预设验证码
		getRandomPresetsCode() {
			this.presetsTextArray = [];
			let newPosArray = []
			if (this.resultTxtCloneList && this.resultTxtCloneList.length > 0) {
				this.resultTxtCloneList.forEach(item => {
					let findItem = this.posArray.find(subItem => {
						return subItem.word === item
					})
					if (findItem) {
						newPosArray.push(findItem)
					}
				})
			} else {
				newPosArray = [...this.posArray];
			}
			console.log("newPosArray 1111", newPosArray);
			for (let i = 0; i < this.chooseNum; i++) {
				this.presetsTextArray[i] = newPosArray[i].word;
			}
			console.log("newPosArray", newPosArray, this.presetsTextArray);
		},
		//生成随机文字
		getRandomText() {
			let resultTxt = ''
			if (this.resultTxtList && this.resultTxtList.length > 0) {
				let index = 0
				resultTxt = this.resultTxtList[index]
				this.resultTxtList.splice(index, 1)
			} else
				if (this.isNumber) {
					resultTxt = Math.round(Math.random() * 10)
					const isSome = this.codeList.some(item => {
						return item  === resultTxt;
					})
					if (isSome) {
						return this.getRandomText()
					}
				} else {
					let initStr = '0x';
					let tempStr3 = 4 + Math.round(Math.random() * 5);
					initStr += this.codeCharacter[tempStr3];
					let tempStr4 = tempStr3 === 4 ? 14 + Math.round(Math.random()) : Math.round(Math.random() * 15);
					initStr += this.codeCharacter[tempStr4];
					let tempStr5 = tempStr4 === 15 && tempStr3 === 9 ? Math.round(Math.random() * 10) : Math.round(Math.random() * 15);
					initStr += this.codeCharacter[tempStr5];
					let tempStr6 = tempStr3 === 9 && tempStr4 === 15 && tempStr5 === 10 ? Math.round(Math.random() * 5) : Math.round(Math.random() * 15);
					initStr += this.codeCharacter[tempStr6];
					console.log("getRandomText", String.fromCharCode(initStr));
					resultTxt = String.fromCharCode(initStr)
				}
			console.log("getRandomText", this.isNumber, resultTxt);
			return resultTxt
		},
		//生成随机坐标
		getRandomPosition(int, width, height, position) {
			let maxTop = (int < 2 ? height / 2 : height) - 30 > 0 ? (int < 2 ? height / 2 : height) - 30 : (int < 2 ? height / 2 : height);
			let minTop = int < 2 ? 0 : height / 2
			let maxLeft = (int % 2 === 0 ? width / 2 : width) - 30 > 0 ? (int % 2 === 0 ? width / 2 : width) - 30 : (int % 2 === 0 ? width / 2 : width);
			let minLeft = int % 2 === 0 ? 0 : width / 2
			return position == "x" ? Math.floor(Math.random() * (maxLeft - minLeft)) + minLeft : Math.floor(Math.random() * (maxTop - minTop)) + minTop;
		},
		//生成随机颜色
		getRandomColor() {
			var letters = "0123456789ABCDEF";
			var color = "#";
			for (var i = 0; i < 6; i++) {
				color += letters[Math.floor(Math.random() * 16)];
			}
			return color;
		},
		//设置文字随机字体大小 在74和60之间，单位px
		getRandomSize() {
			return Math.floor(Math.random() * (74 - 60)) + 60;
		},
		//设置文字随机粗体 在500和1000之间
		getRandomWeight() {
			return Math.floor(Math.random() * (1000 - 500)) + 500;
		},
		//生成文字随机旋转角度
		getRandomRotate() {
			return Math.round(Math.random()) == 1 ? Math.floor(Math.random() * 45) : -Math.floor(Math.random() * 45);
		},
		// 设置结果集
		setResultTxt(list) {
			if (list && Array.isArray(list)) {
				this.resultTxtList = deepClone(list)
				this.resultTxtCloneList = deepClone(list)
			}
			console.log("setResultTxt", this.resultTxtList);
		}
	}
}
</script>
<style lang="scss">
.verifyCode {
	background-color: #FFFFFF;
	border-radius: 2px;
	width: 640rpx;

	&-title {
		padding: 30rpx;
		border-bottom: 1px solid #e4e7eb;
		display: flex;
		justify-content: space-between;

		&-text {
			font-size: 32rpx;
			color: #45494c;
		}
	}

	&-view {
		padding: 30rpx;

		&-panel {
			width: 100%;
			height: 320rpx;
			margin-bottom: 30rpx;
			position: relative;
			background-size: cover;
			background-repeat: no-repeat;

			.selected {
				width: 70rpx;
				height: 70rpx;
				transform: translate(-50%, -50%);
				position: absolute;
				z-index: 98;
				opacity: 0.8;

				&-text {
					font-size: 32rpx;
					color: #FFFFFF;
					position: absolute;
					left: 50%;
					top: 44%;
					transform: translate(-50%, -50%);
					z-index: 9;
				}

				&-image {
					width: 70rpx;
					height: 70rpx;
					position: absolute;
				}
			}

			.originalText {
				position: absolute;
			}

			.refresh {
				padding: 12rpx;
				display: inline-block;
				background-color: rgba(0, 0, 0, .12);
				position: absolute;
				right: 0;
				top: 0;
				z-index: 99;
			}
		}

		&-tips {
			width: 100%;
			height: 76rpx;
			background-color: #F7F9FA;
			border: 1px solid #e4e7eb;
			border-radius: 2px;
			display: flex;
			align-items: center;
			justify-content: center;


			&-text {
				font-size: 28rpx;
			}

			.text-weight {
				font-weight: 700;
				margin-left: 8rpx;
			}
		}
	}

	.loading {
		padding: 30rpx;

		&-panel {
			width: 100%;
			height: 320rpx;
			margin-bottom: 30rpx;
			position: relative;
			background-color: #FBF7F3;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;

			&-image {
				width: 300rpx;
				height: 300rpx;
			}
		}

		&-tips {
			width: 100%;
			height: 76rpx;
			background-color: #F7F9FA;
			border: 1px solid #e4e7eb;
			border-radius: 2px;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
		}
	}
}
</style>
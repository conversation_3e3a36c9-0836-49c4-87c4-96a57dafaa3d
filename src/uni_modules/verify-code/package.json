{"id": "verify-code", "displayName": "仿【网易易盾】文字点选验证码，按顺序点击文字的验证码，本地验证码", "version": "1.0.1", "description": "仿照【网易易盾】的文字点选验证码，文字、文字渲染坐标、文字颜色、文字大小、文字粗细，包括点选区域的背景图等等，都是随机生成的。", "keywords": ["网易易盾", "文字点选", "按顺序点击文字", "验证码", "本地验证码"], "repository": "", "engines": {"HBuilderX": "^3.1.0"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "u"}, "App": {"app-vue": "y", "app-nvue": "u", "app-uvue": "u"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "u", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y", "钉钉": "y", "快手": "y", "飞书": "y", "京东": "y"}, "快应用": {"华为": "u", "联盟": "u"}}}}}
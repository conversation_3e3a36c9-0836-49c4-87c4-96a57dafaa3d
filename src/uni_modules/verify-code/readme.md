# verify-code
## 按顺序点击文字的验证码

> 仿照【网易易盾】的文字点选验证码，文字、文字渲染坐标、文字颜色、文字大小、文字粗细，包括点选区域的背景图等等，都是随机的，如果单位要求安全性更高，可以把【随机文字集合】和【预设文字集合】挪到后端生成，利用接口方式返回到前端即可。
> 背景图和点选图标，以及加载gif图是本地的，如果业务是微信小程序，可以挪到后端。

### 使用说明：

```vue
<verify-code ref="verifyCode" @success="verifyCodeSuccess"></verify-code>
```

### 文档：

|属性名		|必填	|默认	|说明					|
|:-:|:-:|:-:|:-:|
|codeCharacter	|是		|	|汉字的16进制数			|
|posArray	|是		|[]	|随机文字集合	|
|mosPosArray	|是		|[]	|点击的坐标集合	|
|randomBackGround	|是		|0	|随机图片背景	|
|startLeft	|是		|0	|可点击的范围距离左侧边界的距离	|
|startTop	|是		|0	|可点击的范围距离上侧边界的距离	|
|clickInt	|是		|0	|点击次数	|
|selectedArray	|是		|[]	|用户选中文字	|
|presetsTextArray	|是		|[]	|预设文字集合	|
|loading	|是		|false	|是否正在加载中	|
|verifying	|是		|false	|是否在验证中	|
|pass	|是		|false	|验证是否通过	|

### Events：

|事件称名	|说明	|返回值	|
|:-:|:-:|:-:|:-:|
|close	|关闭验证码|-	|
|open	|打开验证码|-	|
|refresh|刷新验证码|-	|
|mosPosTap	|取消选中位置|-	|
|move	|选中位置|-	|
|close	|关闭验证码|-	|

### Api：

|事件称名	|说明	|返回值	|
|:-:|:-:|:-:|:-:|
|compareText	|比对用户选择的文字|-	|
|selectedText	|选中文字，存储文字|-	|
|init	|初始化验证码|-	|
|getPosition	|获取容器的布局信息|返回 NodesRef 对应的 SelectorQuery	|
|getRandomBackGround	|生成随机背景图片|-	|
|getRandomPresetsCode	|在随机的文字中挑选3个作为预设验证码|-	|
|getRandomText	|生成随机文字|-	|
|getRandomPosition	|生成随机坐标|-	|
|getRandomColor	|生成随机颜色|-	|
|getRandomSize	|设置文字随机字体大小 在40和52之间，单位px|-	|
|getRandomWeight	|设置文字随机粗体 在500和1000之间|-	|
|getRandomRotate	|生成文字随机旋转角度|-	|

### 调用父组件：

```javascript
this.$emit("success", true);
```

如果对你有帮助，请留下珍贵的评价吧~~~
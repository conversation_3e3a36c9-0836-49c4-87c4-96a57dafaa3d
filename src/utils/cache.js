const Cache = {
  // 缓存前缀
  keyPrev: 'store_',
  // 设置缓存（expire为时间戳，单位为s）
  set(key, value, expire) {
    let data = {
      expire: expire ? this.time() + expire : '',
      value
    }

    if (typeof data === 'object') data = JSON.stringify(data)
    try {
      uni.setStorageSync(this.getKey(key), data)
    } catch (e) {
      return false
    }
  },
  // 获取缓存
  get(key) {
    try {
      let data = uni.getStorageSync(this.getKey(key))
      const { value, expire } = JSON.parse(data)
      if (expire && expire < this.time()) {
        uni.removeStorageSync(this.getKey(key))
        return false
      } else {
        return value
      }
    } catch (e) {
      return false
    }
  },
  // 获取当前时间
  time() {
    return Math.round(new Date() / 1000)
  },
  // 移除缓存
  remove(key) {
    if (key) uni.removeStorageSync(this.getKey(key))
  },
  get<PERSON><PERSON>(key) {
    return this.keyPrev + key
  }
}

export default Cache

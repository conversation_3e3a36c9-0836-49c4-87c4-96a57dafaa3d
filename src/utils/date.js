import { replaceDate } from './util'
const weekArr = ['日', '一', '二', '三', '四', '五', '六']

/**
 * @description 时间格式化
 * @param dateTime { number } 时间错
 * @param fmt { string } 时间格式
 * @return { string }
 */
// yyyy:mm:dd|yyyy:mm|yyyy年mm月dd日|yyyy年mm月dd日 hh时MM分等,可自定义组合
export const timeFormat = (dateTime, fmt = 'yyyy-mm-dd') => {
  // 如果为null,则格式化当前时间
  if (!dateTime) dateTime = Number(new Date())
  // 如果dateTime长度为10或者13，则为秒和毫秒的时间戳，如果超过13位，则为其他的时间格式
  if (dateTime.toString().length === 10) dateTime *= 1000
  let date = new Date(replaceDate(dateTime))
  let ret
  let opt = {
    'y+': date.getFullYear().toString(), // 年
    'm+': (date.getMonth() + 1).toString(), // 月
    'd+': date.getDate().toString(), // 日
    'h+': date.getHours().toString(), // 时
    'M+': date.getMinutes().toString(), // 分
    's+': date.getSeconds().toString(), // 秒
    'w+': weekArr[date.getDay()] // 周几
  }
  for (let k in opt) {
    ret = new RegExp('(' + k + ')').exec(fmt)
    if (ret) {
      fmt = fmt.replace(ret[1], ret[1].length === 1 ? opt[k] : opt[k].padStart(ret[1].length, '0'))
    }
  }
  return fmt
}

/**
 * @description 获取指定长度的日期
 * @param begin { number } 开始时间 时间戳
 * @param length { int } 日期长度
 * @return { Array }
 */
export const dateLengthInfo = (begin, length) => {
  let dateArr = []
  let dateB = begin - 24 * 60 * 60 * 1000
  let dateE = begin + 24 * 60 * 60 * 1000 * (length - 1)
  for (let j = dateB; j < dateE;) {
    j = j + 24 * 60 * 60 * 1000
    dateArr.push(timeFormat(j, 'yyyy-mm-dd'))
  }
  return dateArr
}

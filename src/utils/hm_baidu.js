// 百度统计
// #ifdef H5
/* eslint-disable */
var _hmt = _hmt || [];
// 判断域名使用
if (location.host.indexOf('sh-v4') > -1) {
  if (location.host.indexOf('debug') > -1) {
    // 测试环境
    createBaiduHm('https://hm.baidu.com/hm.js?d2703d2c179fbbea410ac4d7ca28e7f0')
  } else {
    // 开发环境
    createBaiduHm('https://hm.baidu.com/hm.js?9646f38c3accfe5fee1c23df0fb97142')
  }
}

function createBaiduHm(src) {
  var hm = document.createElement('script')
  hm.src = src
  var s = document.getElementsByTagName('script')[0]
  s.parentNode.insertBefore(hm, s)
}
// #endif

/*
 * @Author: jiuylq
 * @Date: 2022-12-07 10:22:20
 * @LastEditors: jiuylq
 * @LastEditTime: 2022-12-12 14:28:14
 * @Description: 请填写简介
 */
import Cache from '@/utils/cache'
import store from '@/store'
// 引入i18n
import Vue from 'vue'
import VueI18n from 'vue-i18n'
import messages from '@/locale/index.js'
Vue.use(VueI18n);

// 创建i18n对象
export const i18n = new VueI18n({
  locale: uni.getLocale('en'), // 获取设置的语言
  // locale: 'en',
  messages
})
// 重新封装方法
export function $t(args) {
  // eslint-disable-next-line no-useless-call
  return i18n.tc.call(i18n, args);
}

export function getLocalLang() {
  let lang = Cache.get('language')
  if (!lang) {
    lang = uni.getLocale() || 'zh-Hans'
  }
  // 监听下
  onchangeLocaleLang()
  return lang
}

/**
 * @description 设置语言
 * @param {*} lang
 */
export function setLocaleLang(lang) {
  uni.setLocale(lang);
  // 注意this指向
  this.$i18n.locale = lang;
  Cache.set('language', lang)
  store.dispatch('setLanguage', lang)
}

/**
 * @description 监听语言的变化
 *
 */
export function onchangeLocaleLang() {
  return new Promise((resolve, reject) => {
    uni.onLocaleChange((e) => {
      console.log('changeLang', e)
      resolve(e.locale)
    })
  })
}

export function generateTitle(title) {
  const listJson = messages[store.getters.language]
  let text = ''
  // vue中method才有this，其它情况为undefined
  // 兼容下
  if (this) {
    console.log(33, this.$t(title))
    console.log(44, this.$i18n.locale)
    text = this.$t(title)
  } else {
    // json文件有2中key的定义方式需要兼容下
    text = listJson[title]
    if (!text) {
      title.split('.').forEach(item => {
        if (!text) {
          text = listJson[item]
        } else {
          text = text[item]
        }
      });
    }
  }
  if (text) {
    return text
  }
  return title
}

export function setNavBarTitle(title) {
  uni.setNavigationBarTitle({
    title: this.$t(title)
  })
}


/**
  * 判断是否为空对象
  * @param {*} object 源对象
  */
export const isEmptyObject = (object) => {
  return Object.keys(object).length === 0
}

/**
  * 判断是否为数组
  *@param {*} array
  */
export const isArray = (array) => {
  return Object.prototype.toString.call(array) === '[Object Array]'
}

/**
  * 判断是否为对象
  *@param {*} Obejct
  */
export const isObject = (object) => {
  return Object.prototype.toString.call(object) === '[Object Obejct]'
}

/**
  * 判断是否为空
  * @param {*} object 源对象
  */
export const isEmpty = (value) => {
  if (isArray(value)) {
    return value.length === 0
  }
  if (isObject(value)) {
    return isEmptyObject(value)
  }
  return !value
}

/**
  * 对象转URL参数格式
  * {id:111,name:'xxx'} 转为 ?id=111&name=xxx
  * @param {object} obj
  */
export const urlEncode = (obj = {}) => {
  const result = []
  for (var key in obj) {
    let item = obj[key]
    if (!item) {
      continue
    }
    if (isArray(item)) {
      item.forEach(i => {
        result.push(`${key}=${i}`)
      })
    } else {
      result.push(`${key}=${item}`)
    }
  }
  return result.join('&')
}
/**
  * 是否在数组内
  */
export const inArray = (search, array) => {
  for (var i in array) {
    if (array[i] === search) return true
  }
  return false
}

import axios from '@/uni_modules/uniapp-axios/uni-axios'
import basePath from '@/config/index'
import cache from '@/utils/cache'
import common from '@/common/common'
import { toast, mergeCookie, getHeaderCookies } from '@/utils/util'
import { miRouter } from '@/utils/router_utils'
import store from '@/store'

// import Vue from 'vue'
// #ifdef H5
var pendingRequest = new Map();
// #endif
let url = ''
// #ifdef H5
url = process.env.NODE_ENV !== 'development' ? basePath.baseApi : basePath.baseUrl + basePath.baseApi
// #endif
// #ifdef MP-WEIXIN || MP-ALIPAY
url = basePath.baseUrl + basePath.baseApi
// #endif
// 创建一个axios实例
console.log("url", url);

const service = axios.create({
  baseURL: url, // url = base url + request api
  withCredentials: true, // send cookies when cross-domain requests
  timeout: 5000, // request timeout
  headers: {
    'content-type': 'application/json'
  }
})

// 请求拦截
service.interceptors.request.use(
  config => {
    // 在请求拦截器中为每一个请求添加cancelToken，并将cancel方法存入全局数组中保存
    // #ifdef H5
    // 检查是否存在重复请求，若存在则取消已发的请求
    removePendingRequest(config);
    // 把当前请求信息添加到pendingRequest对象中
    addPendingRequest(config);
    // #endif
    // 让每个请求都设置一次token
    var token = cache.get(common.API_TOKEN)
    if (token) {
      config.headers.TOKEN = token
    }
    // 统一走CONTRACT
    if (store.getters.cookies) {
      // 不要在https中设置这些请求头，浏览器会报错的
      // #ifdef MP-WEIXIN || MP-ALIPAY
      // config.headers.Cookie = ''
      // #endif
      config.headers.CONTRACT = store.getters.cookies
    }
    return config
  },
  error => {
    // 请求错误处理
    console.log(error)
    return Promise.reject(error)
  }
)

// 返回数据拦截
service.interceptors.response.use(

  response => {
    let cookie = getHeaderCookies(response.header)
    if (cookie) {
      store.dispatch('user/setCookies', mergeCookie(store.getters.cookies, cookie))
    }
    // #ifdef H5
    removePendingRequest(response.config);
    // #endif
    const res = response.data || {}
    // console.log("data", res);
    if (Reflect.has(res, "code") && res.code !== 0) {
      // cancelLink()
      // 账号在其他地方登陆
      if (res.code === -3) {
        // to re-login
        miRouter.replaceAll({ path: '/pages/login/login' })
      }
      // 登录要另外处理
      if (response.config.url.indexOf('/api/background/login') > -1) {
        return res
      }
      return Promise.reject(new Error(res.msg || 'Error'))
    } else {
      return res
    }
  },
  error => {
    console.log('err' + error)
    // #ifdef H5
    removePendingRequest(error.config || {});
    if (axios.isCancel(error)) {
      console.log("已取消的重复请求：" + error.message);
      // 这里注意一下，要不就返回空的错误信息，要不反馈特定的信息过滤，如果直接返回错误信息，你会发现会打印取消的线程名称，测试会觉得是BUG, catch里面记住要判断 error 有没有信息才进行打印
      return Promise.reject(new Error(''))
    } else {
      // 添加异常处理
      toast({ title: error.message || '请求失败！', icon: 'error' })
      return Promise.reject(error)
    }
    // #endif
    // #ifdef MP-WEIXIN || MP-ALIPAY
    return Promise.reject(error)
    // #endif
  }
)

// 函数返回唯一的请求key
function getRequestKey(config) {
  let { method, url, params, data } = config;
  return [method, url, JSON.stringify(params), JSON.stringify(data)].join("&");
}

// 添加请求信息
function addPendingRequest(config) {
  let requestKey = getRequestKey(config);
  config.cancelToken = config.cancelToken || new axios.CancelToken((cancel) => {
    if (!pendingRequest.has(requestKey)) {
      pendingRequest.set(requestKey, cancel);
    }
  });
}

// 取消重复请求，移除重复请求信息
function removePendingRequest(config) {
  let requestKey = getRequestKey(config);
  if (pendingRequest.has(requestKey)) {
    // 如果是重复的请求，则执行对应的cancel函数
    let cancel = pendingRequest.get(requestKey);
    cancel(requestKey);
    // 将前一次重复的请求移除
    pendingRequest.delete(requestKey);
  }
}
// 取消全部正在请求的请求,页面销毁的时候可以调用它，作为预留功能
export function removeAllPendingRequest() {
  if (pendingRequest && pendingRequest.size > 0) {
    var array = pendingRequest.keys()
    for (let key of array) {
      let cancel = pendingRequest.get(key)
      cancel(key)
      // 将前一次重复的请求移除
      pendingRequest.delete(key)
    }
  }
}

export default service

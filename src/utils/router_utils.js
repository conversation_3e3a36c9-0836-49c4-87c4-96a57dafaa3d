import {
  router
} from '@/router'

import { paramsToUrlQuerys } from './util'
export const miRouter = {
  push: (params) => {
    // #ifdef MP-WEIXIN || H5
    router.push(params)
    // #endif
    // #ifdef MP-ALIPAY
    // eslint-disable-next-line array-callback-return, no-undef
    my.navigateTo({
      url: getUrl(params)
    })
    // #endif
  },
  replace: (params) => {
    // #ifdef MP-WEIXIN || H5
    router.replace(params)
    // #endif
    // #ifdef MP-ALIPAY
    // eslint-disable-next-line array-callback-return, no-undef
    my.redirectTo({
      url: getUrl(params)
    })
    // #endif
  },
  replaceAll: (params) => {
    // #ifdef  H5
    router.replaceAll(params)
    // #endif
    // #ifdef MP-WEIXIN
    // eslint-disable-next-line array-callback-return, no-undef
    wx.reLaunch({
      url: getUrl(params)
    })
    // #endif
    // #ifdef MP-ALIPAY
    // eslint-disable-next-line array-callback-return, no-undef
    my.reLaunch({
      url: getUrl(params)
    })
    // #endif
  },
  pushTab: (params) => {
    // #ifdef MP-WEIXIN || H5
    router.pushTab(params)
    // #endif
    // #ifdef MP-ALIPAY
    // eslint-disable-next-line array-callback-return, no-undef
    my.switchTab({
      url: getUrl(params)
    })
    // #endif
  },
  back: (params) => {
    // #ifdef MP-WEIXIN || H5
    router.back(params)
    // #endif
    // #ifdef MP-ALIPAY
    // eslint-disable-next-line array-callback-return, no-undef
    my.navigateBack(params || 1)
    // #endif
  }
}

function getUrl(params) {
  let query = null
  let url = params.path ? params.path : params
  if (params.query) {
    query = paramsToUrlQuerys(params.query)
    if (query) url = url + '?' + query
  }
  return url
}

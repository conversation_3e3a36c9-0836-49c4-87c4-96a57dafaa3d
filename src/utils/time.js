
//  var list = this.getDateRange(6,true)
//  console.log("获取近一周日期范围：\n开始日期:"+list[0]+";\n结束日期:"+list[1]);
//  var list = this.getDateRange(30,true)
//  console.log("获取近一个月日期范围：\n开始日期:"+list[0]+";\n结束日期:"+list[1]);

//  var list = this.getDateRange(0,true)
//  console.log("获取今天日期范围：\n开始日期:"+list[0]+";\n结束日期:"+list[1]);

//  var list = this.getDateRange(1,true)
//  console.log("获取昨天日期范围：\n开始日期:"+list[0]+";\n结束日期:"+list[0]);

//  var list = this.getDateRange(6,false)
//  console.log("获取下一周日期范围：\n开始日期:"+list[0]+";\n结束日期:"+list[1]);

//  var list = this.getDateRange(30,false)
//  console.log("获取下一个月日期范围：\n开始日期:"+list[0]+";\n结束日期:"+list[1]);

export function getDateRange (intervalDays, bolPastTime) {
  var dateNow = new Date();
  let oneDayTime = 24 * 60 * 60 * 1000;
  let list = [];
  let lastDay;

  if (bolPastTime === true) {
    lastDay = new Date(dateNow.getTime() - intervalDays * oneDayTime);
    list.push(formateDate(lastDay));
    list.push(formateDate(dateNow));
  } else {
    lastDay = new Date(dateNow.getTime() + intervalDays * oneDayTime);
    list.push(formateDate(dateNow));
    list.push(formateDate(lastDay));
  }
  return list;
}// 获取一段时间
export function formateDate(time) {
  let year = time.getFullYear()
  let month = time.getMonth() + 1
  let day = time.getDate()

  if (month < 10) {
    month = '0' + month
  }

  if (day < 10) {
    day = '0' + day
  }

  return year + '-' + month + '-' + day
}
/**
 * 日期范围工具类
 */
export function startTime(time) {
  const nowTimeDate = new Date(time)
  return nowTimeDate.setHours(0, 0, 0, 0)
}

export function endTime(time) {
  const nowTimeDate = new Date(time)
  return nowTimeDate.setHours(23, 59, 59, 999)
}

/***
 * 当前时间
 */
export function getCurrentDate() {
  return new Date();
}

/***
 * 今天的开始时间
 */
export function getBeginToday() {
  return new Date(new Date(new Date().toLocaleDateString()).getTime());
}

/***
 * 昨天开始时间
 */
export function getBeginYesterday() {
  return startTime(getBeginToday() - 24 * 60 * 60 * 1000);
}

/***
 * 昨天结束时间时间
 */
export function getEndYesterday() {
  return endTime(getBeginToday() - 24 * 60 * 60 * 1000);
}
/***
 * 本周的第一天时间
 */
export function getBeginWeek() {
  var currentDate = getCurrentDate();
  var week = currentDate.getDay();

  // 一天的毫秒数
  var millisecond = 1000 * 60 * 60 * 24;
  // 减去的天数
  var minusDay = week !== 0 ? week - 1 : 6;
  // 本周 周一
  var monday = new Date(currentDate.getTime() - (minusDay * millisecond));
  return startTime(monday);
}

/***
 * 本周的最后一天时间
 */
export function getEndWeek() {
  var currentDate = getCurrentDate();
  var week = currentDate.getDay();
  // 一天的毫秒数
  var millisecond = 1000 * 60 * 60 * 24;
  // 减去的天数
  var minusDay = week !== 0 ? week - 1 : 6;
  // 本周 周日
  var monday = new Date(currentDate.getTime() - (minusDay * millisecond));
  var sunday = new Date(monday.getTime() + (6 * millisecond));
  // 返回
  return endTime(sunday);
}

/***
 * 上周的开始
 */
export function getBeginLastWeek() {
  var currentDate = getCurrentDate();
  var first = currentDate.getDate() - currentDate.getDay() - 6;
  var startDate = new Date(currentDate.setDate(first));
  return startTime(startDate);
}

/***
 * 上周的结束
 */
export function getEndLastWeek() {
  var currentDate = getCurrentDate();
  var first = currentDate.getDate() - currentDate.getDay() - 6;
  var last = first + 6;
  var endDate = new Date(currentDate.setDate(last));
  return endTime(endDate);
}

/***
 * 本月的第一天时间
 */
export function getBeginMonth() {
  var currentDate = getCurrentDate();
  var currentMonth = currentDate.getMonth();
  // 获得当前年份4位年
  var currentYear = currentDate.getFullYear();
  // 求出本月第一天
  var firstDay = new Date(currentYear, currentMonth, 1);

  return firstDay;
};

/***
 * 本月的最后一天时间
 */
export function getEndMonth() {
  // 获取当前时间
  var currentDate = getCurrentDate();
  var fullYear = currentDate.getFullYear();
  var month = currentDate.getMonth() + 1; // getMonth 方法返回 0-11，代表1-12月
  var endOfMonth = new Date(fullYear, month, 0);
  return endTime(endOfMonth);
};

/***
 * 上月的第一天时间
 */
export function getBeginLastMonth() {
  // 获取当前时间
  var currentDate = getCurrentDate();
  // 获得当前月份0-11
  var currentMonth = currentDate.getMonth();
  // 获得当前年份4位年
  var currentYear = currentDate.getFullYear();
  // 获得上一个月的第一天
  var priorMonthFirstDay = getPriorMonthFirstDay(currentYear, currentMonth);
  return priorMonthFirstDay;
};

/***
 * 上月的最后一天时间
 */
export function getEndLastMonth() {
  // 获取当前时间
  var currentDate = getCurrentDate();
  // 获得当前月份0-11
  var currentMonth = currentDate.getMonth();
  // 获得当前年份4位年
  var currentYear = currentDate.getFullYear();

  // 当为12月的时候年份需要加1
  // 月份需要更新为0 也就是下一年的第一个月
  if (currentMonth === 11) {
    // eslint-disable-next-line no-unused-vars
    currentYear++;
    currentMonth = 0; // 就为
  } else {
    // 否则只是月份增加,以便求的下一月的第一天
    currentMonth++;
  }

  // 一天的毫秒数
  var millisecond = 1000 * 60 * 60 * 24;
  // 求出上月的最后一天
  var lastDay = new Date(getBeginMonth().getTime() - millisecond);

  return endTime(lastDay);
};

/**
 * 返回上一个月的第一天Date类型
 * @param year 年
 * @param month 月
 **/
export function getPriorMonthFirstDay(year, month) {
  // 年份为0代表,是本年的第一月,所以不能减
  if (month === 0) {
    month = 11; // 月份为上年的最后月份
    year--; // 年份减1
    return new Date(year, month, 1);
  }
  // 否则,只减去月份
  month--;
  return new Date(year, month, 1); ;
};

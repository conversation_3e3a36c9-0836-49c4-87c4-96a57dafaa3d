/**
 * @description 判断数据类型 目前完美的写法
 * @param {*} obj
 * @returns
 */
//  注意，在 IE6 中，null 和 undefined 会被 Object.prototype.toString 识别成 [object Object]！，当前不需要兼容到ie6
export const type = function(obj) {
  // let res = Object.prototype.toString.call(obj).split(' ')[1]
  // res = res.substring(0, res.length - 1).toLowerCase()
  // return res
  // 更简短的写法
  return Object.prototype.toString
    .call(obj)
    .slice(8, -1)
    .toLowerCase()
}

/**
 * @description 判断是否为数组
 * @param {*} arr
 * @returns boolean
 */
export const isArray = function(arr) {
  if (!arr) {
    return false
  }
  return type(arr) === 'array'
}

/**
 * @description 判断数组是否不为空
 * @param {*} arr
 * @returns boolean
 */
export const isEmptyArray = function(arr) {
  if (!isArray(arr)) {
    return false
  }
  return arr.length === 0
}

/**
 * @description 判断是否为对象
 * @param { Object } obj
 * @returns boolean
 */
export const isObject = function(obj) {
  if (!obj) {
    return false
  }
  return type(obj) === 'object'
}
/**
 * @description 检查是否为空对象
 * @param {*} obj
 * @returns boolean
 */
export const isEmptyObject = function(obj) {
  if (!obj) {
    return false
  }
  // eslint-disable-next-line no-unreachable-loop
  for (let key in obj) {
    return false
  }
  return true
}

/**
 * @description 判断是否是类数组
 * @param {*} o
 * @returns boolean
 */
export const isArrayLike = function(o) {
  if (
    o && // o is not null, undefined, etc
    // o is an object
    typeof o === 'object' &&
    // o.length is a finite number
    isFinite(o.length) &&
    // o.length is non-negative
    o.length >= 0 &&
    // o.length is an integer
    o.length === Math.floor(o.length) &&
    // o.length < 2^32
    o.length < 4294967296
  ) {
    // 数组的上限值
    return true
  } else {
    return false
  }
}

import { $t } from "@/utils/i18n"
import { toast } from "@/utils/util"
export function checkUpdate() {
  console.log("checkUpdate");
  // #ifdef MP-WEIXIN || MP-ALIPAY
  const updateManager = uni.getUpdateManager()

  updateManager.onCheckForUpdate(function(res) {
    // 请求完新版本信息的回调
    console.log("onCheckForUpdate", res.hasUpdate)
  })

  updateManager.onUpdateReady(function() {
    uni.showModal({
      title: $t('tip.update.prompt'),
      content: $t('tip.new.version.ready'),
      success: function(res) {
        if (res.confirm) {
          // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
          updateManager.applyUpdate()
        }
      }
    })
  })

  updateManager.onUpdateFailed(function() {
    // 新版本下载失败
    toast({ title: '新版本下载失败' })
  })
  // #endif
}

/**
 * 图片压缩 返回base64
 * @param {*base64} base64URL
 */
export function compressImage(base64URL, orientation) {
  return new Promise(resolve => {
    let orientation = ''
    let img = new Image()
    let canvas = null
    // #ifdef H5
    canvas = document.createElement('canvas')
    // #endif
    // #ifndef H5
    // eslint-disable-next-line no-undef
    canvas = createCanvasContext('canvas')
    // #endif
    let ctx = canvas.getContext('2d')
    img.src = base64URL
    img.onload = () => {
      // 图片原始尺寸
      let originWidth = img.width
      let originHeight = img.height
      // 最大尺寸限制
      let maxWidth = img.width / 2
      let maxHeight = img.height / 2
      // 目标尺寸
      // 图片尺寸超过1000x1000的限制
      if (originWidth > maxWidth || originHeight > maxHeight) {
        if (originWidth / originHeight > maxWidth / maxHeight) {
          // 更宽，按照宽度限定尺寸
          img.width = maxWidth
          img.height = Math.round(maxWidth * (originHeight / originWidth))
        } else {
          img.height = maxHeight
          img.width = Math.round(maxHeight * (originWidth / originHeight))
        }
      }
      // canvas对图片进行缩放
      canvas.width = img.width
      canvas.height = img.height
      // 清除画布
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      if (orientation) {
        switch (Number(orientation)) {
          case 2:
            ctx.translate(img.width, 0)
            ctx.scale(-1, 1)
            ctx.drawImage(img, 0, 0, img.width, img.height)
            break
          case 3:
            ctx.rotate((180 * Math.PI) / 180)
            ctx.drawImage(img, -img.width, -img.height, img.width, img.height)
            break
          case 4:
            ctx.translate(img.width, 0)
            ctx.scale(-1, 1)
            ctx.rotate((180 * Math.PI) / 180)
            ctx.drawImage(img, -img.width, -img.height, img.width, img.height)
            break
          case 5:
            ctx.translate(img.width, 0)
            ctx.scale(-1, 1)
            ctx.rotate((90 * Math.PI) / 180)
            ctx.drawImage(img, 0, -img.width, img.height, img.width)
            break
          case 6:
            canvas.width = img.height
            canvas.height = img.width
            ctx.rotate((90 * Math.PI) / 180)
            ctx.drawImage(img, 0, 0, img.width, -img.height)
            break
          case 7:
            ctx.translate(img.width, 0)
            ctx.scale(-1, 1)
            ctx.rotate((270 * Math.PI) / 180)
            ctx.drawImage(img, -img.height, 0, img.height, img.width)
            break
          case 8:
            ctx.rotate((270 * Math.PI) / 180)
            ctx.drawImage(img, -img.height, 0, img.height, img.width)
            break
          default:
            ctx.drawImage(img, 0, 0, img.width, img.height)
            break
        }
      } else {
        ctx.drawImage(img, 0, 0, img.width, img.height)
      }
      let base64 = canvas.toDataURL('image/jpeg', 0.7)
      resolve(base64)
    }
  })
}

/**
 * 将以base64的图片url数据转换为Blob
 * @param urlData图片base64数据
 * @name convertBase64UrlToBlob
 */

export function convertBase64UrlToBlob(urlData) {
  let bytes = window.atob(urlData.split(',')[1]) // 去掉url的头，并转换为byte
  // 处理异常,将ascii码小于0的转换为大于0
  let ab = new ArrayBuffer(bytes.length)
  let ia = new Uint8Array(ab)
  for (let i = 0; i < bytes.length; i++) {
    ia[i] = bytes.charCodeAt(i)
  }
  return new Blob([ab], {
    type: 'image/jpg'
  })
}
/**
 * 将以base64的图片url数据转换为file
 * @param dataurl 图片base64数据
 * @param filename 图片名称
 * @name dataURLtoFile
 */

export function dataURLtoFile(dataurl, filename) {
  // 将base64转换为文件
  let arr = dataurl.split(',')
  let mime = arr[0].match(/:(.*?);/)[1]
  let bstr = atob(arr[1])
  let n = bstr.length
  let u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new File([u8arr], filename, {
    type: mime
  })
}

export function pathToBase64(path) {
  return new Promise(function (resolve, reject) {
    if (typeof window === 'object' && 'document' in window) {
      if (typeof FileReader === 'function') {
        var xhr = new XMLHttpRequest()
        xhr.open('GET', path, true)
        xhr.responseType = 'blob'
        xhr.onload = function () {
          if (this.status === 200) {
            let fileReader = new FileReader()
            fileReader.onload = function (e) {
              resolve(e.target.result)
            }
            fileReader.onerror = reject
            fileReader.readAsDataURL(this.response)
          }
        }
        xhr.onerror = reject
        xhr.send()
        return
      }
      var canvas = document.createElement('canvas')
      var c2x = canvas.getContext('2d')
      var img = new Image()
      img.onload = function () {
        canvas.width = img.width
        canvas.height = img.height
        c2x.drawImage(img, 0, 0)
        resolve(canvas.toDataURL())
        canvas.height = canvas.width = 0
      }
      img.onerror = reject
      img.src = path
      return
    }
    if (typeof plus === 'object') {
      // eslint-disable-next-line no-undef
      plus.io.resolveLocalFileSystemURL(getLocalFilePath(path),
        function (entry) {
          entry.file(
            function (file) {
              // eslint-disable-next-line no-undef
              var fileReader = new plus.io.FileReader()
              fileReader.onload = function (data) {
                resolve(data.target.result)
              }
              fileReader.onerror = function (error) {
                reject(error)
              }
              fileReader.readAsDataURL(file)
            },
            function (error) {
              reject(error)
            }
          )
        },
        function (error) {
          reject(error)
        }
      )
      return
    }
    // eslint-disable-next-line no-undef
    if (typeof wx === 'object' && wx.canIUse('getFileSystemManager')) {
      // eslint-disable-next-line no-undef
      wx.getFileSystemManager().readFile({
        filePath: path,
        encoding: 'base64',
        success: function (res) {
          resolve('data:image/png;base64,' + res.data)
        },
        fail: function (error) {
          reject(error)
        }
      })
      return
    }
    reject(new Error('not support'))
  })
}

/**
 * 微信压缩图片
 * @param canvasId 画布ID
 * @imgUrl 图片的本地路径
 */
export function compressImageWeixin(canvasId, imgUrl) {
  return new Promise(function(resolve, reject) {
    let iWidth, iHeight, cvsWidth, cvsHeight, destWidth, destHeight;
    const context = uni.createCanvasContext(canvasId);
    uni.getImageInfo({
      src: imgUrl,
      success: function (res) {
        iWidth = res.width; // 原图宽
        iHeight = res.height; // 原图高
        console.log('原图宽：' + iWidth + '---------------------原图高：' + iHeight);
        cvsHeight = 100;
        cvsWidth = iWidth / (iHeight / cvsHeight);
        // 绘制照片到canvas
        context.drawImage(imgUrl, 0, 0, cvsWidth, cvsHeight);
        uni.getFileSystemManager().getFileInfo({
          filePath: imgUrl,
          success: function (res) {
            console.log("原图大小" + res.size / 1024 / 1024)
          }
        })
        console.log("context.draw")
        context.draw(false, function (res) {
          console.log("context draw")
          // 设置上传图片的尺寸
          if (iHeight <= 2400) {
            destHeight = iHeight;
            destWidth = iWidth;
          } else {
            destHeight = 2400;
            destWidth = iWidth / (iHeight / destHeight);
          }
          console.log("上传图片的宽：" + destWidth + "--------高：" + destHeight);

          var cvs = canvasId;
          // 把当前画布指定区域的内容导出生成指定大小的图片
          uni.canvasToTempFilePath({
            canvasId: cvs,
            x: 0,
            y: 0,
            width: cvsWidth,
            height: cvsHeight,
            destWidth: destWidth,
            destHeight: destHeight,
            fileType: "jpg",
            quality: "1",
            success: function (res) {
              uni.getFileSystemManager().getFileInfo({
                filePath: res.tempFilePath,
                success: function (res1) {
                  console.log("压缩后图片大小" + res1.size / 1024 / 1024)
                }
              })
              // 返回压缩图片路径
              resolve(res.tempFilePath)
            },
            fail: function(error) {
              reject(error)
            }
          })
        });
      }
    })
  })
}

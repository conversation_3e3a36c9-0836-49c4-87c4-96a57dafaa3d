// 此类用作用户信息管理类

import cache from "@/utils/cache"
import common from "@/common/common"
import { to, showLoading, toast, getListByTree, times, divide, checkClient } from "@/utils/util"
import { apiCardServiceCardOperateCardQuit, apiBookingUserUploadUserFace, apiCardServiceCardOperateCancelLoss, apiCardServiceCardOperateLoss, apiCardServiceCardUserBatchFreezeCardUser, apiCardServiceCardUserModify } from "@/api/user"
import { apiBackgroundOrganizationOrganizationTreeList, apiCardServiceCardDepartmentGroupAllTreeList } from "@/api/dic"
import Exif from 'exif-js'
import { compressImage } from '@/utils/uploadFaceImg'
import config from "../config"
import { $t } from "@/utils/i18n"
import { apiBackgroundOrderOrderAppealDealOrderAppeal, apiBackgroundOrderOrderReviewOrderReviewOperate, apiBackgroundOrderOrderJiaofeiApprovalOperator, apiBackgroundAttendanceAttendanceForLeaveRecordModify, apiBackgroundAttendanceAttendanceSupplementRecordModify } from '@/api/order'
import imgPath from '@/common/imgBasePath'
import { isPhone } from '@/utils/validata'
// 定义一个上传的url
let uploadFileUrl = config.baseUrl + "/api/merchant_mobile/common/upload"
// 金额的测试正则
let amountReg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;
/**
 * 获取用户组织
 */
export function getUserOrgs() {
  // 在缓存里面获取组织信息
  var orgs = null
  var userInfo = cache.get(common.KEY_USER_INFO)
  if (orgs && Reflect.has(userInfo, 'orgs')) {
    orgs = userInfo.orgs || {}
    console.log("orgs", orgs);
  }
  return orgs
}

/**
 * 是否存在该组织
 * @param orgsValue  传入组织名称
 */
export function isCurrentOrgs(orgsValue) {
  var orgs = getUserOrgs()
  return orgs && orgs.includes(orgsValue)
}

/**
 * 设置退卡
 */
export function setCardWithdrawal(id, title, content, vm) {
  var that = vm
  return new Promise(function(resolve, reject) {
    that.$confirm(
      {
        dialogTypeValue: "content", // 弹窗类型
        titleTxt: title, // 弹窗标题
        contentTxt: content,
        isShowDialog: true, // 是否显示弹窗
        cancelCallBack: function() {
          console.log("点击取消");
          resolve("退卡失败")
        },
        confirmCallBack: function(data) {
          console.log("confirmCallback", data);
          showLoading({
            title: '获取中...',
            mask: true
          })
          apiCardServiceCardOperateCardQuit({ card_user_id: id }).then(res => {
            uni.hideLoading()
            if (res.code === 0) {
              console.log("apiCardServiceCardOperateCardQuit", res)
              toast({ title: '退卡成功' })
              resolve('退卡成功')
            } else {
              toast({ title: res.msg })
              resolve("退卡失败")
            }
          }).catch(error => {
            console.log("error", error);
            toast({ title: '退卡失败，' + error.message })
            uni.hideLoading()
            reject(error)
          })
        }
      }, that)
  })
}

/**
 * 设置取消挂失
 */
export async function setCardCancelLoss(id, title, content, vm) {
  var that = vm
  return new Promise(function(resolve, reject) {
    that.$confirm(
      {
        dialogTypeValue: "content", // 弹窗类型
        titleTxt: title, // 弹窗标题
        contentTxt: content,
        isShowDialog: true, // 是否显示弹窗
        cancelCallBack: function() {
          console.log("点击取消");
          resolve("取消挂失失败")
        },
        confirmCallBack: function(data) {
          console.log("confirmCallback", data);
          showLoading({
            title: '获取中...',
            mask: true
          })
          apiCardServiceCardOperateCancelLoss({ card_user_id: id }).then(res => {
            uni.hideLoading()
            if (res.code === 0) {
              console.log("apiCardServiceCardOperateCancelLoss", res)
              toast({ title: '取消挂失成功' })
              resolve('取消挂失成功')
            } else {
              toast({ title: res.msg })
              resolve("取消挂失失败")
            }
          }).catch(error => {
            console.log("error", error);
            toast({ title: '取消挂失失败，' + error.message })
            uni.hideLoading()
            reject(error)
          })
        }
      }, that)
  })
}
/**
 * 设置挂失卡
 */
export async function setCardLoss(id, title, content, vm) {
  var that = vm
  return new Promise(function(resolve, reject) {
    that.$confirm(
      {
        dialogTypeValue: "content", // 弹窗类型
        titleTxt: title, // 弹窗标题
        contentTxt: content,
        isShowDialog: true, // 是否显示弹窗
        cancelCallBack: function() {
          console.log("点击取消");
          resolve("取消挂失失败")
        },
        confirmCallBack: function(data) {
          console.log("confirmCallback", data);
          showLoading({
            title: '获取中...',
            mask: true
          })
          apiCardServiceCardOperateLoss({ card_user_id: id }).then(res => {
            uni.hideLoading()
            if (res.code === 0) {
              console.log("apiCardServiceCardOperateLoss", res)
              toast({ title: '挂失成功' })
              resolve('挂失成功')
            } else {
              toast({ title: res.msg })
              resolve("挂失失败")
            }
          }).catch(error => {
            console.log("error", error);
            toast({ title: '挂失失败，' + error.message })
            uni.hideLoading()
            reject(error.message)
          })
        }
      }, that)
  })
}
/**
 * 设置冻结卡
 */
export async function setCardFreeze(id, title, content, vm) {
  var that = vm
  return new Promise(function(resolve, reject) {
    that.$confirm(
      {
        dialogTypeValue: "content", // 弹窗类型
        titleTxt: title, // 弹窗标题
        contentTxt: content,
        isShowDialog: true, // 是否显示弹窗
        cancelCallBack: function() {
          console.log("点击取消");
          resolve("冻结失败")
        },
        confirmCallBack: function(data) {
          console.log("confirmCallback", data);
          showLoading({
            title: '获取中...',
            mask: true
          })
          apiCardServiceCardUserBatchFreezeCardUser({ card_user_ids: [id] }).then(res => {
            uni.hideLoading()
            if (res.code === 0) {
              console.log("apiCardServiceCardUserBatchFreezeCardUser", res)
              toast({ title: '冻结成功' })
              resolve('冻结成功')
            } else {
              toast({ title: res.msg })
              resolve("冻结失败," + res.msg)
            }
          }).catch(error => {
            console.log("error", error);
            toast({ title: '冻结失败' + error.message })
            uni.hideLoading()
            reject(error.message)
          })
        }
      }, that)
  })
}

/**
 * 设置上传face
 */
export async function setUploadFace(userId, companyId, personNo, vm) {
  var that = vm
  // #ifdef MP-WEIXIN || MP-ALIPAY
  that.$miRouter.push({
    path: '/pages_common_function/user/face_photo_graph',
    query: {
      userId: userId,
      companyId: companyId,
      personNo: personNo
    }
  })
  // #endif
  return new Promise((resolve, reject) => {
    let orientation = ''
    // #ifdef H5
    // 从相册选择1张图
    uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      async success(res) {
        console.log("res.tempFilePaths", res);
        let file = res.tempFiles[0]
        Exif.getData(file, () => {
          orientation = Exif.getTag(file, 'Orientation')
        })
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = async e => {
          let imgUrl = await compressImage(e.currentTarget.result, orientation)
          // 先上传文件形成url
          let [error, resultUrl] = await to(uploadFilePromise(imgUrl))
          if (resultUrl && !error) {
            // 调用上传face接口上传face
            let [errorFace, resultFace] = await to(uploadUserFace(userId, companyId, personNo, resultUrl))
            if (resultFace && !errorFace) {
              var uploadTxt = checkClient() === "mp-weixin" ? $t("page.user.upload.pic") : $t("page.user.upload.face")
              resolve(uploadTxt + "成功")
            } else {
              reject(errorFace)
            }
          } else {
            reject(error)
          }
        }
      },
      fail(error) {
        reject(error.message)
      }
    });
    // #endif
  })
}
/**
 * 上传文件
 * @param {传入文件的本地路径} url
 * @returns
 */
export function uploadFilePromise(url) {
  return new Promise((resolve, reject) => {
    showLoading({
      title: '上传中...',
      mask: true
    })
    uni.uploadFile({
      url: uploadFileUrl,
      filePath: url,
      name: 'file',
      header: {
        TOKEN: cache.get(common.API_TOKEN)
      },
      formData: {
        prefix: 'face'
      },
      success: res => {
        console.log("uni.uploadFile", res);
        uni.hideLoading()
        var faceURl = JSON.parse(res.data).data.public_url
        resolve(faceURl)
      },
      fail: error => {
        console.log("uni.uploadFile", error);
        uni.hideLoading()
        reject(error.message)
      }
    })
  })
}

/**
 * 上传face
 * @param {人ID} userId
 * @param {项目点Id} companyId
 * @param {人员编号} personNo
 * @param {图片路径} imgUrl
 * @returns
 */
export function uploadUserFace(userId, companyId, personNo, imgUrl) {
  var uploadTxt = checkClient() === "mp-weixin" ? $t("page.user.upload.pic") : $t("page.user.upload.face")
  return new Promise((resolve, reject) => {
    apiBookingUserUploadUserFace({
      company_id: companyId,
      user_id: userId,
      face_url: imgUrl,
      person_no: personNo
    }).then(res => {
      console.log("uploadUserFace", res);
      if (res && res.code === 0) {
        toast({ title: uploadTxt + '成功' })
        uni.$emit(common.MSG_UPLOAD_FACE_SUCCESS, { msg: uploadTxt + '成功' })
        resolve(uploadTxt + '成功')
      } else {
        toast({ title: uploadTxt + '失败' + res.msg })
        reject(res.msg)
      }
    }).catch(error => {
      console.log("uploadUserFace error", error);
      toast({ title: uploadTxt + '失败' + error.message })
      reject(error.message)
    })
  })
}

/**
 * 修改用户信息,有弹窗
 */
export async function modifyUserInfoDialog(userInfo, title, vm) {
  var that = vm
  return new Promise(function(resolve, reject) {
    that.$confirm({
      dialogTypeValue: "input", // 弹窗类型
      titleTxt: title, // 弹窗标题
      contentTxt: "",
      isShowDialog: true, // 是否显示弹窗
      inputTypeName: title === "手机号码修改" ? 'number' : 'text',
      maxLength: title === "手机号码修改" ? 11 : 20,
      cancelCallBack: function() {
        console.log("点击取消");
        resolve("修改失败")
      },
      confirmCallBack: function(data) {
        console.log("confirmCallback", data);
        var inputValue = data.inputContent
        if (!inputValue || inputValue.length === 0) {
          toast({ title: '请至少输入一个内容' })
          return
        }
        if (title === "名字修改") {
          userInfo.person_name = inputValue
        }
        if (title === "手机号码修改") {
          userInfo.phone = inputValue
          if (!isPhone(inputValue)) {
            toast({ title: '请输入正确的手机号' })
            return
          }
        }

        this.isShowDialog = false
        // 调用接口修改
        modifyUserInfo(userInfo).then(res => {
          if (res === "修改成功") {
            resolve("修改成功")
          } else {
            resolve("修改失败")
          }
        }).catch(error => {
          reject(error)
        })
      }
    }, that)
  })
}
/**
 * 修改用户信息
 * @param {*} userInfo
 */
export function modifyUserInfo(userInfo) {
  return new Promise(function(resolve, reject) {
    showLoading({
      title: '修改中...',
      mask: true
    })
    apiCardServiceCardUserModify(userInfo).then(res => {
      uni.hideLoading()
      if (res.code === 0) {
        console.log("apiCardServiceCardUserModify", res)
        toast({ title: '修改成功' })
        resolve('修改成功')
      } else {
        toast({ title: res.msg })
        resolve("修改失败")
      }
    }).catch(error => {
      console.log("error", error);
      toast({ title: '修改失败，' + error.message })
      uni.hideLoading()
      reject(error.message)
    })
  })
}
/**
 * 根据性别的code返回性别中文名
 */
export function getSexNameByCode (sexCode) {
  if (!sexCode) {
    return ''
  }
  switch (sexCode) {
    case "MAN":
      return "男"
      break;
    case "WOMEN":
      return "女"
      break;
    case "OTHER":
      return "其他"
      break;
    default:
      break;
  }
}

/**
 * 获取用户的组织树列表（此列表分上下级）
 * @param type 1,分级 2，不分级
 */
export function getUserOrgsList(type) {
  return new Promise(function(resolve, reject) {
    apiBackgroundOrganizationOrganizationTreeList().then(res => {
      if (Reflect.has(res, 'data')) {
        var orgsList = res.data || []
        // console.log("orgsList", orgsList);
        // 分级直接返回
        if (type === 1) {
          resolve(orgsList)
        } else {
          var newOrgsList = getListByTree(orgsList)
          //   console.log("orgsList new", newOrgsList);
          resolve(newOrgsList)
        }
      }
    }).catch(error => {
      console.log("error", error);
      reject(error)
    })
  })
}
/**
 * 获取用户部门列表
 */
export function getUserDepartmentList() {
  return new Promise(function(resolve, reject) {
    apiCardServiceCardDepartmentGroupAllTreeList().then(res => {
      if (Reflect.has(res, 'data')) {
        var departmentList = res.data || []

        // 分级直接返回
        if (departmentList && departmentList.length > 0) {
          departmentList.map(item => {
            item.name = item.group_name
            return item
          })
        }
        resolve(departmentList)
      }
    }).catch(error => {
      console.log("error", error);
      reject(error)
    })
  })
}

/**
 * 判断用户是否含有该key，如果有则true，否则 false
 * @param {*} key  传入要判断的key
 */
export function hasPermissionKey(key) {
  if (!key || key.length <= 0) {
    return false
  }
  // 获取本地存储的列表
  var permissionList = cache.get(common.KEY_MERCHANT_APP_PERMISSION_KEYS) || []
  // 判断是否有该key
  if (Array.isArray(permissionList) && permissionList.length > 0) {
    return permissionList.includes(key)
  }
  return false
}

/**
 * 审批同意/拒绝
 * @param id 入参id
 * @param isAgree  true 同意，false 拒绝
 * @param content  弹窗内容
 * @param  vm  页面上下文
 */
export async function modifyApprovalDialog(id, isAgree, content, vm) {
  var that = vm
  var agreeMsg = isAgree ? 'success' : 'reject'
  return new Promise(function(resolve, reject) {
    that.$confirm({
      dialogTypeValue: isAgree ? "content" : "input", // 弹窗类型
      titleTxt: isAgree ? $t('tip.prompt') : content, // 弹窗标题
      contentTxt: content,
      isTxtArea: !isAgree,
      isShowDialog: true, // 是否显示弹窗
      cancelCallBack: function() {
        console.log("点击取消");
        resolve("点击取消")
      },
      confirmCallBack: function(data) {
        console.log("confirmCallback", data);

        // 调用接口修改
        var parmas = {
          id: id,
          review_status: agreeMsg
        }
        if (!isAgree) {
          if (!data.inputContent || data.inputContent.length <= 0) {
            toast({ title: '请输入拒绝原因' })
            return
          }
          parmas.reject_reason = data.inputContent
        }
        this.isShowDialog = false
        showLoading({
          title: '修改中...',
          mask: true
        })
        apiBackgroundOrderOrderReviewOrderReviewOperate(parmas).then(res => {
          uni.hideLoading()
          if (res.code === 0) {
            toast({ title: '修改成功' })
            resolve("修改成功")
          } else {
            toast({ title: '修改失败,' + res.msg })
            resolve("修改失败")
          }
        }).catch(error => {
          uni.hideLoading()
          toast({ title: '修改失败,' + error.message })
          reject(error.message)
        })
      }
    }, that)
  })
}

/**
 * 缴费审批同意/拒绝
 * @param id 入参id
 * @param isAgree  true 同意，false 拒绝
 * @param content  弹窗内容
 * @param  vm  页面上下文
 */
export async function modifyPaymentDialog(id, isAgree, content, vm) {
  var that = vm
  var agreeMsg = isAgree ? 'agree' : 'refuse'
  return new Promise(function(resolve, reject) {
    that.$confirm({
      dialogTypeValue: isAgree ? "content" : "input", // 弹窗类型
      titleTxt: isAgree ? $t('tip.prompt') : content, // 弹窗标题
      contentTxt: content,
      isTxtArea: !isAgree,
      isShowDialog: true, // 是否显示弹窗
      cancelCallBack: function() {
        console.log("点击取消");
        resolve("点击取消")
      },
      confirmCallBack: function(data) {
        console.log("confirmCallback", data);

        // 调用接口修改
        var parmas = {
          approval_id: id,
          approval_status: agreeMsg
        }
        if (!isAgree) {
          if (!data.inputContent || data.inputContent.length <= 0) {
            toast({ title: '请输入拒绝原因' })
            return
          }
          parmas.refuse_reason = data.inputContent
        }
        this.isShowDialog = false
        showLoading({
          title: '修改中...',
          mask: true
        })
        apiBackgroundOrderOrderJiaofeiApprovalOperator(parmas).then(res => {
          uni.hideLoading()
          if (res.code === 0) {
            toast({ title: '修改成功' })
            resolve("修改成功")
          } else {
            toast({ title: '修改失败,' + res.msg })
            resolve("修改失败")
          }
        }).catch(error => {
          uni.hideLoading()
          toast({ title: '修改失败,' + error.message })
          reject(error.message)
        }, that)
      }
    })
  })
}

/**
 * 请假同意/拒绝
 * @param id 入参id
 * @param isAgree  true 同意，false 拒绝
 * @param content  弹窗内容
 * @param  vm  页面上下文
 */
export async function modifyLeaveDialog(id, isAgree, content, vm) {
  var that = vm
  var agreeMsg = isAgree ? 'agree' : 'reject'
  return new Promise(function(resolve, reject) {
    that.$confirm({
      dialogTypeValue: isAgree ? "content" : "input", // 弹窗类型
      titleTxt: isAgree ? $t('tip.prompt') : content, // 弹窗标题
      contentTxt: content,
      isTxtArea: !isAgree,
      isShowDialog: true, // 是否显示弹窗
      cancelCallBack: function() {
        console.log("点击取消");
        resolve("点击取消")
      },
      confirmCallBack: function(data) {
        console.log("confirmCallback", data);

        // 调用接口修改
        var parmas = {
          id: id,
          for_leave_status: agreeMsg
        }
        if (!isAgree) {
          if (!data.inputContent || data.inputContent.length <= 0) {
            toast({ title: '请输入拒绝原因' })
            return
          }
          parmas.merchant_remark = data.inputContent
        }
        this.isShowDialog = false
        showLoading({
          title: '修改中...',
          mask: true
        })
        apiBackgroundAttendanceAttendanceForLeaveRecordModify(parmas).then(res => {
          uni.hideLoading()
          if (res.code === 0) {
            toast({ title: '修改成功' })
            resolve("修改成功")
          } else {
            toast({ title: '修改失败,' + res.msg })
            resolve("修改失败")
          }
        }).catch(error => {
          uni.hideLoading()
          toast({ title: '修改失败,' + error.message })
          reject(error.message)
        })
      }
    }, that)
  })
}
/**
 * 补卡同意/拒绝
 * @param id 入参id
 * @param isAgree  true 同意，false 拒绝
 * @param content  弹窗内容
 * @param  vm  页面上下文
 */
export async function modifyCardReplacementDialog(id, isAgree, content, vm) {
  var that = vm
  var agreeMsg = isAgree ? 'agree' : 'reject'
  return new Promise(function(resolve, reject) {
    that.$confirm({
      dialogTypeValue: isAgree ? "content" : "input", // 弹窗类型
      titleTxt: isAgree ? $t('tip.prompt') : content, // 弹窗标题
      contentTxt: content,
      isTxtArea: !isAgree,
      isShowDialog: true, // 是否显示弹窗
      cancelCallBack: function() {
        console.log("点击取消");
        resolve("点击取消")
      },
      confirmCallBack: function(data) {
        console.log("confirmCallback", data);

        // 调用接口修改
        var parmas = {
          id: id,
          supplement_status: agreeMsg
        }
        if (!isAgree) {
          if (!data.inputContent || data.inputContent.length <= 0) {
            toast({ title: '请输入拒绝原因' })
            return
          }
          parmas.merchant_remark = data.inputContent
        }
        this.isShowDialog = false
        showLoading({
          title: '修改中...',
          mask: true
        })
        apiBackgroundAttendanceAttendanceSupplementRecordModify(parmas).then(res => {
          uni.hideLoading()
          if (res.code === 0) {
            toast({ title: '修改成功' })
            resolve("修改成功")
          } else {
            toast({ title: '修改失败,' + res.msg })
            resolve("修改失败")
          }
        }).catch(error => {
          uni.hideLoading()
          toast({ title: '修改失败,' + error.message })
          reject(error.message)
        })
      }
    }, that)
  })
}

/**
 * 审批处理，退款，驳回
 * @param tradeNo 订单编号
 * @param orderAppealId 订单ID
 * @param type 类型
 * @param title 标题
 * @param inputTitle  输入框头部内容、
 * @param inputLeftTip 输入框左侧内容
 * @param isShowInputTitle 是否显示输入框头部
 * @param isShowInputLeft  是否显示输入框左侧提示
 * @param isTxtArea  是否输入框是文本域
 * @param realFee  可退金额
 * @param goodSInfo  菜品信息
 * @param  vm  页面上下文
 */
export async function processOrderApprovalDialog(tradeNo, orderAppealId, realFee, goodSInfo, type, title, inputTitle, inputLeftTip, isShowInputTitle, isShowInputLeft, isTxtArea, vm) {
  var that = vm
  return new Promise(function(resolve, reject) {
    var parmas = {
      trade_no: tradeNo,
      order_appeal_id: orderAppealId,
      deal_action: type
    }

    that.$confirm({
      dialogTypeValue: type === "FINISH" ? "content" : "input", // 弹窗类型
      titleTxt: title, // 弹窗标题
      contentTxt: type === "FINISH" ? inputTitle : "",
      inputTitle: inputTitle,
      inputTypeName: 'digit',
      inputLeftTip: inputLeftTip,
      isShowInputTitle: isShowInputTitle,
      isShowInputLeft: isShowInputLeft,
      isTxtArea: isTxtArea,
      isShowDialog: true, // 是否显示弹窗
      cancelCallBack: function() {
        console.log("点击取消");
        resolve("点击取消")
      },

      confirmCallBack: function(data) {
        console.log("confirmCallback", data);
        switch (type) {
          case "ORDER_REJECT":// 拒绝，整单驳回
            if (!data.inputContent || data.inputContent.length <= 0) {
              toast({ title: '请输入驳回原因' })
              return
            }
            parmas.reject_reason = data.inputContent
            // 单一产品处理
            if (goodSInfo != null && Reflect.has(goodSInfo, "payment_food_id")) {
              parmas.payment_food_fee = goodSInfo.real_fee
              parmas.payment_food_id = goodSInfo.payment_food_id
            }
            break;
          case "FINISH":// 完成处理，暂时不用操作
            break;
            break;
          case "ORDER_REFUND":// 修改菜品金额
            var goodFee = divide(goodSInfo.real_fee.toString().replace(/[^\d]/g, ""))
            console.log("goodFee", goodFee, data.inputContent);
            if (!amountReg.test(data.inputContent)) {
              toast({ title: `退款金额格式错误！` })
              return
            }
            if (parseInt(data.inputContent) > parseInt(goodFee)) {
              toast({ title: `可退余额${goodFee}元` })
              return
            }
            parmas.payment_food_fee = times(data.inputContent)
            parmas.payment_food_id = goodSInfo.payment_food_id
            break;
          case 'ALL_ORDER_REFUND':// 整单退款
            realFee = divide(realFee.replace(/[^\d]/g, ""))
            console.log("ALL_ORDER_REFUND", parseInt(data.inputContent), parseInt(realFee));
            if (!amountReg.test(data.inputContent)) {
              toast({ title: `退款金额格式错误！` })
              return
            }
            if (parseInt(data.inputContent) > parseInt(realFee)) {
              toast({ title: `可退余额${realFee}元` })
              return
            }
            parmas.payment_food_fee = times(data.inputContent)
            break;
          default:
            break;
        }
        this.isShowDialog = false
        // 调用接口修改
        showLoading({
          title: '修改中...',
          mask: true
        })
        apiBackgroundOrderOrderAppealDealOrderAppeal(parmas).then(res => {
          uni.hideLoading()
          console.log('apiBackgroundOrderOrderAppealDealOrderAppeal', res);
          if (res.code === 0) {
            toast({ title: '修改成功' })
            resolve("修改成功")
          } else {
            toast({ title: '修改失败，' + res.msg })
            resolve("修改失败," + res.msg)
          }
        }).catch(error => {
          uni.hideLoading()
          toast({ title: '修改失败，' + error.message })
          reject(error)
        })
      }
    }, that)
  })
}

/**
 * 根据类型返回照片
 * @param {*} type
 * @returns
 */
export function getSexImageByType(type) {
  var imgUrl = ''
  switch (type) {
    case '女':// 女
      imgUrl = imgPath.IMG_FEMALE_PINK
      break;
    case '男':// 男
      imgUrl = imgPath.IMG_MALE_BLUE
      break;

    default:
      break;
  }
  return imgUrl
}
/**
 * 显示功能开发中弹窗
 * @returns
 */
export function showMsgDeveloping() {
  return toast({
    title: $t('tabbar.completefunction')
  })
}

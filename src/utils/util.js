import { isObject, isArray } from './type'

import NP from './np'

/**
 * @param { Function } func
 * @param { Number } time
 * @param { 上下文 } context
 * @return { Function }
 * @description 节流
 */
export function throttle(func, time = 1000, context) {
  let previous = new Date(0).getTime()
  return function (...args) {
    let now = new Date().getTime()
    if (now - previous > time) {
      previous = now
      return func.apply(context, args)
    }
  }
}

/**
 * @param { String } str
 * @return { Object }
 * @description Query语法格式化为对象
 */
export function queryToObject(str) {
  const params = {}
  for (let item of str.split('&')) {
    params[item.split('=')[0]] = item.split('=')[1]
  }
  return params
}

/**
 * @param { Object } params
 * @return { string } Query语法
 * @description 对象格式化为Query语法
 */
export function objectToQuery(params) {
  let p = ''
  if (typeof params === 'object') {
    p = '?'
    for (let props in params) {
      p += `${props}=${params[props]}&`
    }
    p = p.slice(0, -1)
  }
  return p
}

/**
 * @param  { Number } length
 * @return { String } id
 * @description 获取不重复的id
 */
export const getNonDuplicateID = (length = 8, isNumber = false) => {
  let idStr = ''
  idStr = isNumber
    ? Math.random().toString().substr(3, length)
    : Date.now().toString(36) + Math.random().toString(36).substr(3, length)
  return idStr
}

/**
 * @param  { Object } info
 * @param  { Object | Function } navigateOpt
 * @description 轻提示
 */
export function toast(info = {}, navigateOpt) {
  let title = info.title || ''
  let icon = info.icon || 'none'
  let endtime = info.endtime || 2000
  let params = {
    title: title,
    icon: icon,
    duration: endtime
  }
  // #ifdef MP-ALIPAY
  // 支付宝不支持duration
  delete params.duration
  // #endif
  if (title) uni.showToast(params)
  if (navigateOpt !== undefined) {
    if (typeof navigateOpt === 'object') {
      let tab = navigateOpt.tab || 1
      let url = navigateOpt.url || ''

      switch (tab) {
        case 1:
          // 跳转至 table
          setTimeout(function () {
            uni.switchTab({
              url: url
            })
          }, endtime)
          break
        case 2:
          // 跳转至非table页面
          setTimeout(function () {
            uni.navigateTo({
              url: url
            })
          }, endtime)
          break
        case 3:
          // 返回上页面
          setTimeout(function () {
            uni.navigateBack({
              delta: parseInt(url)
            })
          }, endtime)
          break
        case 4:
          // 关闭当前所有页面跳转至非table页面
          setTimeout(function () {
            uni.reLaunch({
              url: url
            })
          }, endtime)
          break
        case 5:
          // 关闭当前页面跳转至非table页面
          setTimeout(function () {
            uni.redirectTo({
              url: url
            })
          }, endtime)
          break
      }
    } else if (typeof navigateOpt === 'function') {
      setTimeout(function () {
        navigateOpt && navigateOpt()
      }, endtime)
    }
  }
}

export function showLoading(options) {
  let params = {
    ...options
  }
  // #ifdef MP-ALIPAY
  // 支付宝不支持mask
  delete params.mask
  // #endif

  uni.showLoading(params)
}

export function hideLoading() {
  uni.hideLoading()
}

/**
 * @param  { Id } selector
 * @param  { Boolean } all
 * @param  { Object } context
 * @param  { Object | Function } getRect
 * @description 获取元素
 */
export function getRect(selector, all, context) {
  return new Promise(function (resolve) {
    let qurey = uni.createSelectorQuery()

    if (context) {
      qurey = uni.createSelectorQuery().in(context)
    }

    qurey[all ? 'selectAll' : 'select'](selector)
      .boundingClientRect(function (rect) {
        if (all && Array.isArray(rect) && rect.length) {
          resolve(rect)
        }
        if (!all && rect) {
          resolve(rect)
        }
      })
      .exec()
  })
}

/**
 * @param { String } str
 * @description 复制文字
 **/
export function copy(str) {
  // #ifdef H5
  let textarea = document.createElement('textarea')
  textarea.value = str
  textarea.readOnly = 'readOnly'
  document.body.appendChild(textarea)
  textarea.select() // 选中文本内容
  textarea.setSelectionRange(0, str.length)
  document.execCommand('copy')
  textarea.remove()
  uni.showToast({
    title: '复制成功'
  })
  // #endif
  // #ifndef H5
  uni.setClipboardData({
    data: str.toString(),
    success: function () {
      uni.showToast({
        title: '复制成功'
      })
    }
  })
  // #endif
}
// 全局挂在跳转参数
// query参数转下码，防止明文在地址栏显示
export const encodeQuery = function (data) {
  if (typeof data !== 'string') {
    return encodeURIComponent(JSON.stringify(data))
  } else {
    return encodeURIComponent(data)
  }
}
// query参数解码对应 encodeQuery
export const decodeQuery = function (data) {
  let query = null
  if (!data) return
  // 如果是Object直接返回
  if (isArray(data) || isObject(data)) return data
  data = decodeURIComponent(data)
  try {
    query = JSON.parse(data)
  } catch (error) {
    console.error('解析urlQuery失败！')
  }
  return query
}

/**
 * @description 格式化数据返回get请求方式的数据
 * @param {*} data
 * @param {*} encode
 * @returns
 */
export function paramsToUrlQuerys(data, encode = false) {
  const queryArr = []
  for (let key in data) {
    if (encode) {
      queryArr.push(encodeURIComponent(key) + '=' + encodeURIComponent(data[key]))
    } else {
      queryArr.push(key + '=' + data[key])
    }
  }
  return queryArr.join('&')
}

/**
 * @description 获取地址栏的参数
 * @param {*} url
 * @returns Object
 */
export const getQueryObject = function (url) {
  // #ifdef H5
  url = url || window.location.href
  // #endif
  var request = {}
  let index = url.indexOf('?')
  if (index !== -1) {
    var str = url.substring(index + 1)
    var strs = str.split('&')
    for (var i = 0; i < strs.length; i++) {
      request[strs[i].split('=')[0]] = decodeURI(strs[i].split('=')[1])
    }
  }
  return request
}
/**
 * @description 判断是否是微信环境，还是支付宝
 * @returns String
 */
export const checkClient = function () {
  // #ifdef H5
  let ua = window.navigator.userAgent.toLowerCase()
  if (ua.indexOf('micromessenger') !== -1) {
    return 'wechat'
  }
  if (ua.indexOf('alipay') !== -1) {
    return 'alipay'
  }
  // 中行环境
  if (ua.indexOf('bocapp') !== -1) {
    return 'bocapp'
  }
  // 中国邮政储蓄银行
  if (ua.indexOf('psbc') !== -1) {
    return 'psbc'
  }
  if (ua.match(/(bankabcandroid|bankabciphone)/i)) {
    return 'abc'
  }
  return null
  // #endif
  // #ifndef H5
  // mp-weixin, mp-alipay
  // return process.env.VUE_APP_PLATFORM
  return uni.getSystemInfoSync().uniPlatform
  // #endif
}

/**
 * 休眠xxxms
 * @param {Number} milliseconds
 */
export function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * @description 继承
 * @param {*} destination
 * @param {*} source
 * @returns Object
 */
export const extend = function (destination, source) {
  for (let property in source) {
    if (source[property]) {
      destination[property] = source[property]
    }
  }
  return destination
}

/**
 * @param {*} name
 * @returns
 */

export const getCookie = function (name) {
  // eslint-disable-next-line one-var
  let arr,
    reg = new RegExp('(^|| )' + name + '=([^;]*)(;|$)')

  if ((arr = document.cookie.match(reg))) return arr[2]
  else return null
}

/**
 *
 * @param {*} cookie
 * @returns Object
 */
export const parseCookie = function (cookie) {
  if (!cookie) return {}
  let result = {}
  let arr = trim(cookie, 1).split(';')
  if (arr.length) {
    // eslint-disable-next-line array-callback-return
    arr.map(v => {
      let res = v.split('=')
      if (res.length) {
        result[res[0]] = res[1]
      }
    })
  }
  return result
}

/**
 *
 * @param {*} cookie
 * @returns String
 */
export const toStringCookie = function (cookie) {
  if (!cookie) return ''
  let result = Object.keys(cookie).map(key => {
    return `${key}=${cookie[key] ? cookie[key] : true}`
  })
  return result.join('; ')
}

/**
 *
 * @param {*} cookie1
 * @param {*} cookie2
 * @returns String
 */
export const mergeCookie = function (cookie1, cookie2) {
  let data1 = parseCookie(cookie1)
  let data2 = parseCookie(cookie2)
  return toStringCookie(extend(data1, data2))
}

/**
 *
 * @param {*} header
 * @returns String
 */
export const getHeaderCookies = function (header) {
  // console.log('header', header)
  let list = []
  let result = {}
  // isObject 目前发现编辑器上面启动的是会获取到一个类数组的要做一层兼容
  for (const key in header) {
    if (Object.hasOwnProperty.call(header, key)) {
      if (key === 'Set-Cookie' || key === 'set-cookie' || key === 'contract') {
        // console.log('cookie type', Object.prototype.toString(header[key]))
        // console.log('cookie', header[key])
        // console.log('cookie', isObject(header[key]))
        // 当有多个set-cookie的情况时
        // 支付宝获取到的是类数组，微信获取到的是带,合并的字符串
        if (isObject(header[key]) || typeof header[key] === 'object') {
          list = header[key]
          break
        }
        list.push(header[key])
      }
    }
  }
  let len = list.length
  if (len) {
    list.forEach((item, index) => {
      // 为了兼容微信的,合并的数据需要切割处理
      // 切割会把时间的也时间的也处理了，expires=Wed,111111 17 Aug 2022 07:18:19 GMT;
      // 正则匹配把时间拿出来吧
      // start 奇葩的微信小程序，小程序不兼容cookie啊，为什么要用cookie啊
      let reg = new RegExp('(^|| )' + 'expires' + '=([^;]*)(;|$)')
      let arr = item.match(reg)
      let expires = arr ? arr[0] : ''
      if (expires) {
        item = item.replace(expires, '')
      }
      if (item && item.indexOf(',') > -1) {
        item = item.replace(/,/g, ';')
        item += '; ' + expires
      }
      // end
      let data = parseCookie(item)
      result = extend(result, data)
    })
  }
  return toStringCookie(result)
}

export function getRunScene(ms) {
  return new Promise(resolve => {
    // #ifdef MP-WEIXIN
    let envVersion = uni.getAccountInfoSync().miniProgram.envVersion
    console.log(13, envVersion)
    resolve(envVersion)
    // #endif

    // #ifdef MP-ALIPAY
    // eslint-disable-next-line array-callback-return, no-undef
    my.getRunScene({
      success(result) {
        console.log(12, result)
        resolve(result.envVersion)
      }
    })
    // #endif
  })
}

export const getRunSceneSync = async function () {
  let result = await getRunScene()
  return result
}

/**
 * @description 删除空字符串字段
 * @param {*} data
 * @returns Object
 */
export const deleteEmptyKey = function (data) {
  Object.keys(data).forEach(key => {
    if (data[key] === '') {
      delete data[key]
    }
  })
  return data
}

export function trim(str, type) {
  if (!trim) return str
  switch (type) {
    case 1:
      return str.replace(/\s+/g, '')
    case 2:
      return str.replace(/(^\s*)|(\s*$)/g, '')
    case 3:
      return str.replace(/(^\s*)/g, '')
    case 4:
      return str.replace(/(\s*$)/g, '')
    default:
      return str
  }
}

export const isBankAbcClient = () => {
  // #ifdef H5
  const ua = window.navigator.userAgent.toLowerCase()
  const isAbc = ua.match(/(bankabcandroid|bankabciphone)/i)
  if (isAbc) {
    return true
  } else {
    return false
  }
  // #endif
  // #ifdef MP-WEIXIN || MP-ALIPAY
  return true
  // #endif
}
/**
 *
 * @param {*} time
 * @returns
 */
export function replaceDate(time) {
  if (typeof time === 'string') {
    if (/^[0-9]+$/.test(time)) {
      // support "*************"
      time = parseInt(time)
    } else {
      // eslint-disable-next-line prefer-regex-literals
      time = time.replace(new RegExp(/-/gm), '/')
      // .replace(new RegExp(/T/gm), ' ') // "2021-10-27T19:49:43.806066"
    }
  }
  return time
}

/**
 * @description 数组去重
 * @param {*} arr
 * @returns
 */
export function unique(arr) {
  if (!Array.isArray(arr)) {
    throw new Error('无效的数组')
  }
  return [...new Set(arr)]
}

/**
 * @description 数组对象根据key去重
 * @param {array} arr
 * @param {string} key
 * @returns {array}
 */
export function uniqueArrKey(arr, key) {
  key = key || 'id'
  const newArray = []
  const tmp = {}
  for (let i = 0; i < arr.length; i++) {
    //
    if (!tmp[arr[i][key].toString()]) {
      //
      tmp[arr[i][key].toString()] = 1
      newArray.push(arr[i])
    }
  }
  return newArray
}

/**
 * @description async/await 的 try_catch处理吧 参考await-to-js
 * @param {*} promise
 * @param {*} errorExt
 */
export const to = (promise, errorExt) => {
  return promise
    .then(res => [null, res])
    .catch(err => {
      if (errorExt) {
        Object.assign(err, errorExt)
      }
      return [err, undefined]
    })
}

/**
 * @description 深拷贝针对object/array
 * @param {*} source
 * @returns
 */
export const deepClone = function (source) {
  if (!source && typeof source !== 'object') {
    throw new Error('error arguments', 'deepClone')
  }
  const targetObj = source.constructor === Array ? [] : {}
  Object.keys(source).forEach(keys => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = deepClone(source[keys])
    } else {
      targetObj[keys] = source[keys]
    }
  })
  return targetObj
}

export const times = money => {
  return NP.times(money, 100)
}

export const plus = (money1, money2) => {
  return NP.plus(money1, money2)
}

/**
 * @description 获取某个时间某月的天数
 * @param {*} date1
 * @returns
 */
export function getMonthLength(date1) {
  const date = new Date(date1)
  const year = date.getFullYear()
  // 月份是从 0 开始计算的
  const _month = date.getMonth() + 1
  return new Date(year, _month, 0).getDate()
}

// 将HTML特殊字符转换成等值的实体
export function escapeHTML(str) {
  var escapeChars = {
    '<': 'lt',
    '>': 'gt',
    '"': 'quot',
    '&': 'amp',
    "'": '#39'
  }
  return str.replace(
    new RegExp('[' + Object.keys(escapeChars).join('') + ']', 'g'),
    function (match) {
      return '&' + escapeChars[match] + ';'
    }
  )
}

// 实体字符转换为等值的HTML。
export function unescapeHTML(str) {
  var htmlEntities = {
    nbsp: ' ',
    lt: '<',
    gt: '>',
    quot: '"',
    amp: '&',
    apos: "'"
  }
  // eslint-disable-next-line no-useless-escape
  return str.replace(/\&([^;]+);/g, function (match, key) {
    if (key in htmlEntities) {
      return htmlEntities[key]
    }
    return match
  })
}

export const getTreeDeepArr = function (treeData, key) {
  let arr = [] // 在递归时操作的数组
  let returnArr = [] // 存放结果的数组
  let depth = 0 // 定义全局层级
  // 定义递归函数
  function childrenEach(childrenData, depthN) {
    for (var j = 0; j < childrenData.length; j++) {
      depth = depthN // 层级赋值
      arr[depthN] = childrenData[j].addr_center_id
      if (childrenData[j].addr_center_id === key) {
        returnArr = arr.slice(0, depthN + 1) // 拿到当前从零到当前层级的数据
        break
      } else {
        if (childrenData[j].children_list) {
          depth++
          childrenEach(childrenData[j].children_list, depth)
        }
      }
    }
    return returnArr
  }
  return childrenEach(treeData, depth)
}

/**
 * @description 防抖
 * @param {*} func
 * @param {*} wait
 * @param {*} immediate
 * @returns
 */
export const debounce = function (func, wait, immediate) {
  let timeout, args, context, timestamp, result

  const later = function () {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function (...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}

// 默认时间多久
export let getSevenDateRange = num => {
  let endDate = [
    new Date().getFullYear(),
    (new Date().getMonth() + 1).toString().padStart(2, '0'),
    new Date().getDate().toString().padStart(2, '0')
  ].join('-')
  if (num > 1) {
    let timeStamp = new Date().getTime() - 86400000 * (num - 1)
    let startDate = [
      new Date(timeStamp).getFullYear(),
      (new Date(timeStamp).getMonth() + 1).toString().padStart(2, '0'),
      new Date(timeStamp).getDate().toString().padStart(2, '0')
    ].join('-')
    return [startDate, endDate]
  } else if (num === 1) {
    return [endDate, endDate]
  } else {
    return false
  }
}

/**
 * 日期格式化
 * @param time
 * @param format
 * @returns {string}
 */
export const parseTime = function (time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string') {
      if (/^[0-9]+$/.test(time)) {
        // support "*************"
        time = parseInt(time)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        // eslint-disable-next-line prefer-regex-literals
        time = time.replace(new RegExp(/-/gm), '/').replace(new RegExp(/T/gm), ' ') // "2021-10-27T19:49:43.806066"
      }
    }

    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  // padStart兼容处理
  if (!String.prototype.padStart) {
    // eslint-disable-next-line no-extend-native
    String.prototype.padStart = function padStart(targetLength, padString) {
      targetLength = targetLength >> 0 // floor if number or convert non-number to 0;
      padString = String(typeof padString !== 'undefined' ? padString : ' ')
      if (this.length > targetLength) {
        return String(this)
      } else {
        targetLength = targetLength - this.length
        if (targetLength > padString.length) {
          padString += padString.repeat(targetLength / padString.length) // append to original to ensure we are longer than needed
        }
        return padString.slice(0, targetLength) + String(this)
      }
    }
  }
  const timeStr = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return timeStr
}
/**
 * 隐藏手机号中间部分
 * @param {手机号} phone
 * @returns
 */
export const hidePhoneNum = function (phone) {
  // 没有到11位，不操作
  if (!phone || phone.length < 11) {
    return phone
  }
  var regPhone = /^(\d{3})\d{4}(\d{4})$/
  return phone.replace(regPhone, '$1***$2')
}
/**
 * 根据输入的num获取最近天数的时间段
 * @param num  用户输入的天数
 * @param format  格式化类型默认 '{y}-{m}-{d} {h}:{i}:{s}'
 * @param isHasToday  是否包含今天
 */
export const getLastDayRange = (num, format = '{y}-{m}-{d} {h}:{i}:{s}', isHasToday = true) => {
  var startDate = ''
  var endDate = ''
  var currentTime = new Date()
  if (!isHasToday) {
    currentTime.setTime(currentTime.getTime() - 24 * 60 * 60 * 1000)
  }
  if (num === 1) {
    // 当天是 零点 到 23.59 的时间
    startDate = parseTime(new Date(currentTime.toLocaleDateString()).getTime(), format)
    endDate = parseTime(
      new Date(currentTime.toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1,
      format
    )
  } else {
    // 其他情况就是当前时间向前
    endDate = parseTime(currentTime, format)
    var endTime = currentTime.getTime() - 3600 * 1000 * 24 * num
    startDate = parseTime(endTime, format)
  }
  return [startDate, endDate]
}
/**
 * 将树形列表转换成一个列表返回
 * @param list  传入树形列表
 */
export const getListByTree = list => {
  var newList = []
  if (!list) {
    return []
  }
  // 写个递归循环它和它的子集
  let fn = source => {
    source.forEach(item => {
      newList.push(item)
      if (item.children_list && item.children_list.length > 0) {
        fn(item.children_list)
      }
    })
  }
  fn(list)
  return newList
}
/**
 *把数字在转换成每三位加逗号
 * @param {*} num
 * @param isLastAddZero  是否在最后面补零位.00
 * @returns
 */
export const numberToThousands = (num = 0, isLastAddZero = false) => {
  if (!num || num == null || isNaN(num)) {
    return '0'
  }
  return num.toString().replace(/\d+/, function (n) {
    var newNum = n.replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
    return isLastAddZero ? newNum + '.00' : newNum
  })
}
// 格式化金额/100
export const divide = money => {
  if (!money) return '0.00'
  if (typeof money === 'number') {
    return NP.divide(money, 100).toFixed(2)
  } else if (typeof money === 'string' && !isNaN(Number(money))) {
    return NP.divide(money, 100).toFixed(2)
  } else {
    return money
  }
}

/**
 * 获取系统信息
 */
export const getSystemInfo = () => {
  return new Promise((resolve, reject) => {
    uni.getSystemInfo({
      success: res => {
        // 获取手机型号
        const model = res.model
        console.log('手机型号-------：' + model)
        resolve(res)
      },
      fail: error => {
        reject(error)
      }
    })
  })
}

// 是否是邮箱
export const isEmail = (value) => {
  return /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value);
}

// 是否包含中文
export const haveCNChars = (value) => {
  return /[\u4e00-\u9fa5]/.test(value);
}

// 是否是手机号
export const isPhone = (value) => {
  return /^1[3,4,5,6,7,8,9][0-9]{9}$/.test(value.toString());
}

// 是否是座机
export const isTel = (value) => {
  return /^([0-9]{3,4}-)?[0-9]{7,8}$/.test(value.toString());
}

// 是否8-20为数字
export const isPass8To20 = (value) => {
  return /^(?=.*[0-9])(?=.*([a-z]|[A-Z])).{8,20}$/.test(value.toString())
}

// 是否8-20为数字 测试说放开 含 8位数字?? 直接 \w
export const isPass8To20orNumber = (value) => {
  return /^(?=.*[0-9])(?=.*([a-z]|[A-Z]|[0-9])).{8,20}$/.test(value.toString())
}

export const isNumber = (value) => {
  return /^(?=[1-9])\d+$/.test(value.toString())
}

// 校验正数并可为0，可包含2位小数
export const positiveMoney = (value) => {
  return /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/.test(value.toString())
}

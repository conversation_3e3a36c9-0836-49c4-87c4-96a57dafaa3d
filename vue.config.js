const TransformPages = require('uni-read-pages')
const { webpack } = new TransformPages()
let filePath = ''
let timeStamp = ''
if (process.env.UNI_PLATFORM === 'h5') {
  filePath = 'static/js/'
  timeStamp = '.' + new Date().getTime()
  module.exports = {
    transpileDependencies: ['uview-ui'],
    filenameHashing: false,
    configureWebpack: {
      plugins: [
        new webpack.DefinePlugin({
          ROUTES: webpack.DefinePlugin.runtimeValue(() => {
            const tfPages = new TransformPages({
              includes: ['path', 'name', 'meta', 'aliasPath']
            });
            return JSON.stringify(tfPages.routes)
          }, true)
        })
      ],
      output: {
        filename: `${filePath}[name]${timeStamp}.js`,
        chunkFilename: `${filePath}[name]${timeStamp}.js`
      }
    }
  }
} else {
  module.exports = {
    transpileDependencies: ['uview-ui'],
    configureWebpack: {
      plugins: [
        new webpack.DefinePlugin({
          ROUTES: webpack.DefinePlugin.runtimeValue(() => {
            const tfPages = new TransformPages({
              includes: ['path', 'name', 'meta', 'aliasPath']
            });
            return JSON.stringify(tfPages.routes)
          }, true)
        })
      ]
    }
  }
}
